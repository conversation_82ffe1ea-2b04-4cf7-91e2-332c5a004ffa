<template>
    <view class="help-center-container">
        <!-- 顶部导航栏 -->
        <u-navbar :autoBack="true" title="帮助中心" :titleStyle="{
            color: '#222',
            fontWeight: 'bold',
            fontSize: '36rpx'
        }" :leftIconSize="22" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>

        <!-- Tab切换 -->
        <view class="tab-bar">
            <view v-for="(tab, idx) in tabList" :key="idx" class="tab-item" :class="{ active: currentTab === idx }"
                @click="switchTab(idx)">
                {{ tab }}
            </view>
        </view>

        <!-- 搜索框 -->
        <view class="search-box">
            <u-search v-model="keyword" placeholder="搜索" :showAction="false" bgColor="#f6f6f6" height="64rpx"
                :clearabled="true" :disabled="false" :inputStyle="{ fontSize: '30rpx' }" />
        </view>

        <!-- 折叠面板 -->
        <view class="collapse-list">
            <u-collapse :value="activeNames" :accordion="true">
                <u-collapse-item v-for="(item, idx) in list[currentTab]" :key="idx" :title="item.title" :name="idx">
                    <view class="collapse-content">
                        <text class="answer-label">答：</text>
                        <text class="answer-text">{{ item.content }}</text>
                    </view>
                </u-collapse-item>
            </u-collapse>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            tabList: ['报名处理规则', '常见问题'],
            currentTab: 0,
            keyword: '',
            activeNames: 0,
            list: [
                // 报名处理规则
                [
                    {
                        title: '1.用户报名了我的兼职，接下来这么做',
                        content:
                            '您可以根据用户的简历初步判断该用户是否合适，若信息不足可主动电话联系用户确认，联系之后可以在用户简历里标记已联系，可选择合适/不合适，15天未标记，系统将自动标记该用户，进入“合适”栏。'
                    },
                    {
                        title: '2.已标记用户“合适”后，面试发现不合适',
                        content:
                            '您可以在候选人状态里面选择“合适”，找到这个用户，点开简历详情里面标记，可选择“不合适”，青团社建议您，如有必要请先面试再进行标记选择，以免造成用户误会。'
                    },
                    {
                        title: '3.如何批量标记用户',
                        content:
                            '在候选人“已报名”页面，右上角有一个批量按钮，可以点击批量选择“已联系”，并在“已联系”中标记用户。'
                    },
                    {
                        title: '4.用户违规，怎么投诉',
                        content: '你可以在"简历详情"页面，右上角点击"投诉"，选择相应的投诉原因，并上传相应的凭证。'
                    },
                    {
                        title: '5.怎么删除岗位',
                        content: ''
                    }
                ],
                // 常见问题
                [
                    {
                        title: '1.岗位审核时间一般多少？',
                        content: '一般在工作日24小时内审核完成（工作时间最快6分钟，审核通过与不通过都会短信提醒您的）'
                    },
                    {
                        title: '2.小任务审核时间一般多久？',
                        content: '一般在工作日24小时内审核完成（工作时间最快6分钟，审核通过与不通过都会短信提醒您的）'
                    },
                    {
                        title: '3.我要发布小人物，怎么操作？',
                        content: '您可以使用电脑，登陆我司官网，选择“发布小任务”，按照要求填写信息，提交审核即可。'
                    },
                    {
                        title: '4.我发布了一个简直，如何修改岗位内容？',
                        content: '岗位发布审核通过后可登陆APP端，PC端或微信/支付宝小程序，在岗位详情页面修改即可'
                    },
                    {
                        title: '5.我离职了，如何解绑公司？',
                        content: '您可以登陆网页端，在“设置”-“解绑公司”，按照要求填写信息，提交审核即可。'
                    },
                    {
                        title: '6.怎么绑定公司',
                        content: '您可以在APP-我的-点击左下角个人信息-上传营业执照，审核通过后企业绑定成功'
                    },
                    {
                        title: '1.岗位审核时间一般多少？',
                        content: '一般在工作日24小时内审核完成（工作时间最快6分钟，审核通过与不通过都会短信提醒您的）'
                    },
                    {
                        title: '2.小任务审核时间一般多久？',
                        content: '一般在工作日24小时内审核完成（工作时间最快6分钟，审核通过与不通过都会短信提醒您的）'
                    },
                    {
                        title: '3.我要发布小人物，怎么操作？',
                        content: '您可以使用电脑，登陆我司官网，选择“发布小任务”，按照要求填写信息，提交审核即可。'
                    },
                    {
                        title: '4.我发布了一个简直，如何修改岗位内容？',
                        content: '岗位发布审核通过后可登陆APP端，PC端或微信/支付宝小程序，在岗位详情页面修改即可'
                    },
                    {
                        title: '5.我离职了，如何解绑公司？',
                        content: '您可以登陆网页端，在“设置”-“解绑公司”，按照要求填写信息，提交审核即可。'
                    },
                    {
                        title: '6.怎么绑定公司',
                        content: '您可以在APP-我的-点击左下角个人信息-上传营业执照，审核通过后企业绑定成功'
                    },
                    {
                        title: '1.岗位审核时间一般多少？',
                        content: '一般在工作日24小时内审核完成（工作时间最快6分钟，审核通过与不通过都会短信提醒您的）'
                    },
                    {
                        title: '2.小任务审核时间一般多久？',
                        content: '一般在工作日24小时内审核完成（工作时间最快6分钟，审核通过与不通过都会短信提醒您的）'
                    },
                    {
                        title: '3.我要发布小人物，怎么操作？',
                        content: '您可以使用电脑，登陆我司官网，选择“发布小任务”，按照要求填写信息，提交审核即可。'
                    },
                    {
                        title: '4.我发布了一个简直，如何修改岗位内容？',
                        content: '岗位发布审核通过后可登陆APP端，PC端或微信/支付宝小程序，在岗位详情页面修改即可'
                    },
                    {
                        title: '5.我离职了，如何解绑公司？',
                        content: '您可以登陆网页端，在“设置”-“解绑公司”，按照要求填写信息，提交审核即可。'
                    },
                    {
                        title: '6.怎么绑定公司',
                        content: '您可以在APP-我的-点击左下角个人信息-上传营业执照，审核通过后企业绑定成功'
                    }
                ]
            ]
        };
    },
    methods: {
        switchTab(idx) {
            this.currentTab = idx; // 切换 tab
            this.activeNames = -1; // 折叠所有面板
            console.log(this.activeNames);
        },
    }
};
</script>

<style lang="scss" scoped>
.help-center-container {
    min-height: 100vh;
    background: #fff;
}

.tab-bar {
    display: flex;
    justify-content: center;
    align-items: center;
    // background: #f6f6f6;
    padding: 24rpx 0 0 0;
    margin-bottom: 12rpx;
}

.tab-item {
    min-width: 180rpx;
    text-align: center;
    padding: 16rpx 16rpx;
    // margin-left: 32rpx;
    font-size: 30rpx;
    color: #222;
    background: #f6f6f6;
    // border-radius: 13rpx;
    font-weight: 500;
}

.tab-item.active {
    background: #09c6b5;
    color: #fff;
    font-weight: bold;
    box-shadow: 0 2rpx 8rpx rgba(9, 198, 181, 0.08);
}

.search-box {
    padding: 24rpx 32rpx 0 32rpx;
    background: #fff;
}

.collapse-list {
    margin-top: 24rpx;
    padding: 0 24rpx;
    height: calc(100vh - 320rpx);
    overflow-y: auto;
}

.collapse-content {
    display: flex;
    align-items: flex-start;
    padding: 12rpx 0 12rpx 0;
    font-size: 28rpx;
    color: #333;
    line-height: 1.7;
}

.answer-label {
    color: #ff7e00;
    font-weight: bold;
    margin-right: 8rpx;
}

.answer-text {
    color: #333;
    font-size: 28rpx;
}
</style>