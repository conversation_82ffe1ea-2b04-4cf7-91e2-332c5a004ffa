<template>
	<view class="position-container">
		<!-- 顶部导航 -->
		<view class="header">

			<u-navbar :autoBack="false" :titleStyle="{
				color: '#333',
				fontSize: '34rpx',
				fontWeight: '500'
			}" rightText="1" rightClick fixed safeAreaInsetTop placeholder bgColor="#ffffff">
				<view class="u-nav-slot" slot="left">
					<u-icon name="arrow-left" size="30" @click="handleBack"></u-icon>
					<!-- <u-line direction="column" :hairline="false" length="16" margin="0 8px"></u-line>
						<u-icon name="home" size="20"></u-icon> -->
				</view>
				<template #right>

					<view class="navbar-right">
						<view class="service" @click="handleService">
							<view class="service-avatar">
								<image src="/static/images/service-avatar.png" mode="aspectFill"></image>
							</view>
							<text>客服</text>
						</view>
						<view class="help" @click="handleHelp">
							<uni-icons type="download" size="20" color="#333"></uni-icons>
							<text>帮助</text>
						</view>
					</view>
				</template>
			</u-navbar>
		</view>

		<!-- 标题 -->
		<view class="title">选择要招的职位</view>
		<view class="section-title">招聘类型</view>
		<!-- 招聘类型 -->
		<view class="recruit-type-container">
			<view class="recruit-type-tabs">
				<view v-for="(type, index) in recruitTypes" :key="index" class="recruit-type-item"
					:class="{ 'recruit-active': activeRecruitType === index }" @click="switchRecruitType(index)">
					<text>{{ type.name }}</text>
				</view>
			</view>
		</view>

		<!-- 职位名称 -->
		<view class="more-link" v-if="!nameFromUrl">
			<view class="section-title">职位名称</view>
			<view class="address-picker" @click="handleMore">
				<text>{{ nameFromUrl || '请选择工作地址' }}</text>
				<uni-icons type="right" size="16" color="#666"></uni-icons>
			</view>
		</view>
		<view class="more-link" v-else>
			<view class="section-title">职位名称</view>
			<view class="address-picker" @click="handleMore">
				<text>{{ nameFromUrl || '请选择工作地址' }}</text>
				<uni-icons type="right" size="16" color="#666"></uni-icons>
			</view>
			<!-- <text class="link" @click="handleMore"> {{ nameFromUrl }}.去修改 &emsp; <uni-icons
					style="margin-left: 10rpx;color: #39b59e;" type="right" size="14" color="#666"></uni-icons></text> -->
		</view>
		<!-- 职位描述 -->
		<view class="job-description-section">
			<view class="section-title">职位描述</view>
			<view class="description-content">
				<textarea style="height: 200rpx;" class="description-textarea" v-model="jobDescription" placeholder="请输入岗位职责" maxlength="5000"
					@input="onDescriptionInput"></textarea>
				<view class="description-tips">
					<view class="tips-list">
						<text class="tip-item">1.岗位职责</text>
						<text class="tip-item">2.任职要求</text>
						<text class="tip-item">3.工作内容</text>
					</view>
					<view class="char-count">{{ jobDescription.length }}/5000</view>
				</view>
			</view>
		</view>

		<!-- 工作地址 -->
		<view class="address-section" v-if="showAddress">
			<view class="section-title">工作地址</view>
			<view class="address-picker" @click="showAddressPicker">
				<text>{{ selectedAddress || '请选择工作地址' }}</text>
				<uni-icons type="right" size="16" color="#666"></uni-icons>
			</view>
		</view>
		<view class="job-description-section">
			<view class="section-title">详细地址</view>
			<view class="description-content">
				<textarea style="height: 50rpx;" class="description-textarea" v-model="address" placeholder="请输入详细地址" maxlength="5000"
					@input="onDescriptionInputlate"></textarea>
			</view>
		</view>

		<!-- 下一步按钮 -->
		<!-- <view class="next-btn" :class="{ 'btn-active': canNext }" @click="nextStep">
			下一步
		</view> -->
		<view class="next-btn" :class="{ 'btn-active':  nameFromUrl && selectedAddress && jobDescription }" @click="nextStep">
			下一步
		</view>
	</view>
</template>

<script>
import { priseApi } from '../../utils/api'
export default {
	data() {
		return {
			selectedPosition: null,
			selectedAddress: '',
			showAddress: true,
			show: false,
			nameFromUrl: '',
			// 招聘类型数据
			activeRecruitType: 0, // 默认选中招聘全职
			recruitTypes: [{
				name: '招聘全职',
				value: 'fulltime'
			}, {
				name: '招聘兼职/学工',
				value: 'parttime'
			}],
			// 职位描述
			jobDescription: '',
			address:''
		}
	},
	onLoad(options) {
		// options = this.$store.state.work;
		// if (options.name) {
		// 	console.log(options.name, '@@');
		// 	this.nameFromUrl = options.name;
		// }
	},
	computed: {
		canPublish() {
			if (this.selectedPosition === 11 || this.selectedPosition === 12) {
				return !!this.selectedPosition;
			}
			return !!this.selectedPosition && !!this.selectedAddress;
		},
		// 判断是否可以进入下一步
		canNext() {
			return this.nameFromUrl && this.selectedAddress && this.jobDescription.trim();
		}
	},
	watch: {
		'$store.state.city'(newVal) {
			console.log(newVal, 'city');
			// 兼容新的地图选址和原有的地址选择
			if (newVal && typeof newVal === 'object') {
				this.selectedAddress = newVal.name || newVal.title || newVal.address;
			} else {
				this.selectedAddress = newVal;
			}
		},
		'$store.state.work'(newVal) {
			this.nameFromUrl = newVal.name;
			console.log(this.nameFromUrl, 'nameFromUrl');
		}
	},
	onShow() {
		uni.hideLoading();
	},
	methods: {
		// 切换招聘类型
		switchRecruitType(index) {
			this.activeRecruitType = index;
			console.log('切换到:', this.recruitTypes[index].name);
			// 这里可以添加切换后的业务逻辑
			// 比如重新加载数据、更新UI等
		},
		// 职位描述输入处理
		onDescriptionInput(e) {
			this.jobDescription = e.detail.value;
		},
		//详细地址
		onDescriptionInputlate(e) {
			this.address = e.detail.value;
		},
		handleBack() {
			uni.switchTab({
				url: '/pages/homeIndex/index'
			});
		},
		handleService() {
			uni.showToast({
				title: '正在连接客服...',
				icon: 'none'
			});
		},
		handleHelp() {
			// uni.showToast({
			// 	title: '帮助中心',
			// 	icon: 'none'
			// });
			uni.navigateTo({
				url:"./map/index"
			})
		},
		selectPosition(item) {
			this.selectedPosition = item.id;
			this.$store.commit('clearWork');
			if (item.id === 11) {
				this.open();
			} else {
				this.showAddress = !(item.id === 11 || item.id === 12);
			}
		},
		handleMore() {
			uni.navigateTo({
				url: '/pages/second/positions/positions'
			});
		},
		showAddressPicker() {
			uni.navigateTo({
				url: './map/mapPicker'
			});
		},
		nextStep() {
			if (this.nameFromUrl == '' || this.selectedAddress == '' || this.jobDescription == '') {
				uni.showToast({
					title: '请填写完整信息',
					icon: 'none'
				});
				return;

			}
			console.log(this.jobDescription, 'jobDescription');
			this.$store.commit('setIntroduction', this.jobDescription);
			this.$store.commit('setaddress', this.address);
			this.$store.commit('setPartTab',this.activeRecruitType)
			uni.navigateTo({
				url: '/pages/second/released/index'
			});
		},
		async handlePublish() {

			if (this.canPublish == false && this.selectedAddress == '') {
				uni.showToast({
					title: '请选择职位或地址',
					icon: 'none'
				});
				return;

			}
			try {
				let ress = await priseApi.getUserInfo()
				if (ress.code == 200) {
					if (ress.data.identity) {
						let res = await priseApi.getCompanyInfoe();
						if (res.code == 200) {
							if (this.nameFromUrl != '' && this.selectedAddress != '') {
								uni.navigateTo({
									url: '/pages/second/released/index'
								});
							} else if (this.canPublish != false && this.selectedAddress != '') {
								uni.navigateTo({
									url: '/pages/second/released/index'
								});
							}
						} else if (res.code == 404) {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
							setTimeout(() => {
								uni.navigateTo({
									url: '/pages/authentication/index'
								});
							}, 1000);
						} else if (res.code == 405) {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
							setTimeout(() => {
								uni.navigateTo({
									url: '/pages/authentication/index'
								});
							}, 1000);
						} else if (res.code == 406) {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
							setTimeout(() => {
								uni.navigateTo({
									url: '/pages/audits/index'
								});
							}, 1000);

						}
					} else {
						uni.showToast({
							title: '您还未实名认证，请先实名认证',
							icon: 'none'
						});
						setTimeout(() => {
							uni.navigateTo({
								url: '/pages/user/realName'
							});
						}, 1000);
					}
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					});


				}


			} catch (error) {
				// console.error('请求出错:', error);
				uni.showToast({
					title: '网络异常，请稍后再试',
					icon: 'none'
				});
			}

		},
		// closePopup() {
		// 	close();
		// },
		open() {
			this.show = true
		},
		close() {
			this.show = false
			this.selectedPosition = null
			this.selectedAddress = ''
			this.showAddress = false
		},
		selectJobType(type) {
			// this.closePopup();
			// 根据选择的类型处理逻辑
			if (type === 'online') {
				this.showAddress = false;
				this.close()
			} else {
				this.close()
				this.showAddress = true

			}
		}
	}
}
</script>

<style lang="scss">
@font-face {
	font-family: CustomFont;
	src: url('../../static/app/second/sales.png');
}

.position-container {
	min-height: 100vh;
	background-color: #fff;
	padding: 0 30rpx;
}

.header {
	// height: 88rpx;
	// display: flex;
	// justify-content: space-between;
	// align-items: center;

	.navbar-right {
		display: flex;
		align-items: center;
		height: 100%;
	}

	.avatar {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		margin-right: 20rpx;
	}

	.help-text {
		font-size: 28rpx;
		color: #333;
	}

	.navbar-right .service,
	.navbar-right .help {
		display: flex;
		// flex-direction: column;
		align-items: center;
		justify-content: center;
		margin-left: 40rpx;
		height: 100%;
	}

	.service-avatar {
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
		overflow: hidden;
		margin-bottom: 4rpx;
	}

	.service-avatar image {
		width: 100%;
		height: 100%;
	}

	.navbar-right text {
		font-size: 24rpx;
		margin-top: 4rpx;
	}
}

.title {
	font-size: 54rpx;
	font-weight: bold;
	color: #333;
	margin: 30rpx 0;
	margin-top: 60rpx;
	text-align: center;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

// 招聘类型切换样式
.recruit-type-container {
	margin: 30rpx 0;
	padding: 0 20rpx;
}

.recruit-type-tabs {
	display: flex;
	background-color: #f5f5f5;
	border-radius: 12rpx;
	padding: 6rpx;
	gap: 6rpx;
}

.recruit-type-item {
	flex: 1;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 8rpx;
	transition: all 0.3s ease;
	cursor: pointer;

	text {
		font-size: 28rpx;
		color: #666;
		font-weight: 500;
		transition: color 0.3s ease;
	}

	&.recruit-active {
		background-color: #10d2c3;
		box-shadow: 0 2rpx 8rpx rgba(16, 210, 195, 0.3);

		text {
			color: #fff;
			font-weight: 600;
		}
	}

	&:active {
		transform: scale(0.98);
	}
}

// 职位描述样式
.job-description-section {
	margin: 40rpx 0;

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}
}

.description-content {
	background-color: #f8f8f8;
	border-radius: 12rpx;
	padding: 30rpx;

	.description-textarea {
		width: 100%;
		min-height: 00rpx;
		background-color: transparent;
		border: none;
		outline: none;
		font-size: 28rpx;
		line-height: 1.6;
		color: #333;
		resize: none;

		&::placeholder {
			color: #999;
			font-size: 26rpx;
			line-height: 1.5;
		}
	}

	.description-tips {
		display: flex;
		justify-content: space-between;
		align-items: flex-end;
		margin-top: 20rpx;
		padding-top: 20rpx;
		border-top: 1rpx solid #e5e5e5;

		.tips-list {
			display: flex;
			flex-direction: column;
			gap: 8rpx;

			.tip-item {
				font-size: 24rpx;
				color: #666;
				line-height: 1.4;
			}
		}

		.char-count {
			font-size: 24rpx;
			color: #999;
			flex-shrink: 0;
		}
	}
}


.more-link {
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}

	text {
		font-size: 26rpx;
		color: #999;
	}

	.link {
		color: #39b59e;
	}

	.address-picker {
		// width: 100%;
		height: 88rpx;
		background-color: #f8f8f8;
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;

		text {
			font-size: 28rpx;
			color: #333;
		}
	}
}

.address-section {
	margin: 40rpx 0;

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}

	.address-picker {
		height: 88rpx;
		background-color: #f8f8f8;
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;

		text {
			font-size: 28rpx;
			color: #333;
		}
	}
}

.next-btn {
	position: fixed;
	bottom: 40rpx;
	left: 30rpx;
	right: 30rpx;
	height: 88rpx;
	background-color: #ccc;
	color: #fff;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	transition: all 0.3s;

	&.btn-active {
		background-color: #10d2c3;
	}
}

.popup-content {
	padding: 30rpx;
	border-radius: 24rpx 24rpx 0 0;

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 40rpx;

		.popup-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}

		.close-btn {
			padding: 10rpx;
		}
	}

	.type-list {
		display: flex;
		gap: 20rpx;
		padding: 20rpx 0;

		.type-item {
			flex: 1;
			background-color: #e1f5f4;
			border-radius: 24rpx;
			padding: 30rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 20rpx;

			.type-icon {
				width: 80rpx;
				height: 80rpx;
				// background-color: #fff;
				// border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.type-info {
				text-align: center;

				.type-name {
					font-size: 32rpx;
					color: #333;
					font-weight: bold;
					display: block;
					margin-bottom: 8rpx;
				}

				.type-desc {
					font-size: 24rpx;
					color: #999;
				}
			}
		}
	}
}
</style>