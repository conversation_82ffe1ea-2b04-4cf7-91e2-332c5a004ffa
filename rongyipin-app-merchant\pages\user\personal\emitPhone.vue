<template>
    <view class="phone-edit-container">
        <!-- 导航栏 -->
        <view class="navbar">
            <view class="nav-left" @click="goBack">
                <text class="nav-icon">‹</text>
            </view>
            <view class="nav-title">
                <text class="title-text">修改手机号</text>
            </view>
            <view class="nav-right"></view>
        </view>

        <!-- 内容区域 -->
        <view class="content">
            <!-- 提示信息 -->
            <view class="tip-section">
                <text class="tip-text">{{ tipText }}</text>
            </view>

            <!-- 输入区域 -->
            <view class="input-section">
                <!-- 手机号输入组 -->
                <view class="phone-input-group">
                    <view class="country-code">
                        <text class="code-text">+86</text>
                    </view>
                    <view class="phone-input-wrapper">
                        <input class="phone-input" type="number" v-model="phoneNumber" placeholder="请输入手机号"
                            maxlength="11" :focus="true" />
                    </view>
                </view>

                <!-- 验证码输入组 -->
                <view class="verify-code-group">
                    <view class="code-input-wrapper">
                        <input class="code-input" type="number" v-model="verifyCode" placeholder="请输入验证码"
                            maxlength="6" />
                    </view>
                    <!-- 获取验证码按钮 -->
                    <view class="verify-code-btn" :class="{ 'disabled': countdown > 0 || !isPhoneValid }"
                        @click="sendVerifyCode">
                        <text class="verify-btn-text">
                            {{ countdown > 0 ? `获取验证码(${countdown}s)` : '获取验证码' }}
                        </text>
                    </view>
                </view>
            </view>

            <!-- 底部按钮 -->
            <view class="bottom-section">
                <view class="save-btn" @click="savePhoneNumber">
                    <text class="btn-text">保存</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { userApi, realApi } from "@/utils/api"
export default {
    data() {
        return {
            phoneNumber: '', // 手机号
            originalPhoneNumber: '', // 原始手机号，用于比较是否有变化
            tipText: '', // 提示文字
            countdown: 0, // 倒计时
            timer: null, // 定时器
            verifyCodeSent: false, // 验证码是否已发送
            verifyCode: '' // 验证码
        }
    },
    computed: {
        // 手机号是否有效
        isPhoneValid() {
            const phoneRegex = /^1[3-9]\d{9}$/;
            return phoneRegex.test(this.phoneNumber);
        },
        // 脱敏手机号显示
        maskedPhone() {
            if (this.originalPhoneNumber && this.originalPhoneNumber.length === 11) {
                return this.originalPhoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
            }
            return this.originalPhoneNumber;
        }
    },
    onLoad() {
        // 获取用户信息
        const userInfo = uni.getStorageSync('usinfo');
        if (userInfo && userInfo.telephone) {
            // this.phoneNumber = userInfo.telephone;
            this.originalPhoneNumber = userInfo.telephone;
            // 格式化手机号显示（隐藏中间部分）
            const maskedPhone = this.maskedPhone;
            this.tipText = `当前交换手机号为：${maskedPhone}，修改后，"交换电话"功能的手机号会被修改。`;
        } else {
            this.tipText = '暂无绑定手机号，请绑定手机号';
        }
    },
    onUnload() {
        // 页面卸载时清除定时器
        if (this.timer) {
            clearInterval(this.timer);
        }
    },
    methods: {
        // 返回上一页
        goBack() {
            // 检查是否有未保存的更改
            if (this.phoneNumber !== this.originalPhoneNumber) {
                uni.showModal({
                    title: '提示',
                    content: '您有未保存的更改，确定要离开吗？',
                    success: (res) => {
                        if (res.confirm) {
                            uni.navigateBack();
                        }
                    }
                });
            } else {
                uni.navigateBack();
            }
        },

        // 发送验证码
        async sendVerifyCode() {
            if (!this.isPhoneValid) {
                uni.showToast({
                    title: '请输入正确的手机号',
                    icon: 'none'
                });
                return;
            }

            // 如果正在倒计时中，不允许重复发送
            if (this.countdown > 0) {
                return;
            }

            try {
                uni.showLoading({ title: '发送中...' });

                const params = {
                    phone: this.phoneNumber,
                    event: 'changetelephone' // 修改手机号验证类型
                };

                const result = await userApi.Captcha(params);

                if (result.code === 200) {
                    uni.showToast({
                        title: '验证码已发送',
                        icon: 'success'
                    });
                    this.verifyCodeSent = true;
                    // 发送成功后立即开始60秒倒计时
                    this.startCountdown();
                } else {
                    uni.showToast({
                        title: result.msg || '发送失败',
                        icon: 'none'
                    });
                }
            } catch (error) {
                console.error('发送验证码失败:', error);
                uni.showToast({
                    title: '发送失败，请重试',
                    icon: 'none'
                });
            } finally {
                uni.hideLoading();
            }
        },

        // 开始倒计时
        startCountdown() {
            this.countdown = 60;
            this.timer = setInterval(() => {
                this.countdown--;
                if (this.countdown <= 0) {
                    clearInterval(this.timer);
                    this.timer = null;
                }
            }, 1000);
        },

        // 保存手机号
        async savePhoneNumber() {
            if (!this.isPhoneValid) {
                uni.showToast({
                    title: '请输入正确的手机号',
                    icon: 'none'
                });
                return;
            }

            if (!this.verifyCodeSent) {
                uni.showToast({
                    title: '请先获取验证码',
                    icon: 'none'
                });
                return;
            }

            if (!this.verifyCode || this.verifyCode.length !== 6) {
                uni.showToast({
                    title: '请输入6位验证码',
                    icon: 'none'
                });
                return;
            }

            try {
                uni.showLoading({ title: '验证中...' });

                const params = {
                    phone: this.phoneNumber,
                    code: this.verifyCode,
                    event: 'changetelephone' // 修改手机号验证类型
                };

                const result = await userApi.phoneCheck(params);

                if (result.code === 200) {
                    const params = {
                        telephone: this.phoneNumber
                    }
                    const results = await realApi.wechatEdit(params);
                    if (results.code === 200) {
                        uni.showToast({
                            title: '手机号修改成功',
                            icon: 'success'
                        });

                        // 更新本地存储的用户信息
                        const userInfo = uni.getStorageSync('usinfo') || {};
                        userInfo.telephone = this.phoneNumber;
                        uni.setStorageSync('usinfo', userInfo);

                        // 延迟返回上一页
                        setTimeout(() => {
                            uni.navigateBack();
                        }, 1500);
                    }
                } else {
                    uni.showToast({
                        title: result.msg || '验证失败',
                        icon: 'none'
                    });
                }
            } catch (error) {
                console.error('验证失败:', error);
                uni.showToast({
                    title: '验证失败，请重试',
                    icon: 'none'
                });
            } finally {
                uni.hideLoading();
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.phone-edit-container {
    min-height: 100vh;
    background: #f8f9fa;
}

/* 导航栏样式 */
.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    padding: 0 30rpx;
    background: #ffffff;
    border-bottom: 1rpx solid #f0f0f0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-left,
.nav-right {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-icon {
    font-size: 40rpx;
    color: #333333;
    font-weight: 500;
}

.nav-title {
    flex: 1;
    text-align: center;
}

.title-text {
    font-size: 32rpx;
    color: #333333;
    font-weight: 500;
}

/* 内容区域 */
.content {
    flex: 1;
    padding: 40rpx 30rpx;
}

/* 提示信息区域 */
.tip-section {
    margin-bottom: 40rpx;
    padding: 0 10rpx;
}

.tip-text {
    font-size: 26rpx;
    color: #999999;
    line-height: 36rpx;
}

/* 输入区域 */
.input-section {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 40rpx 30rpx;
    margin-bottom: 40rpx;
}

/* 手机号输入组 */
.phone-input-group {
    display: flex;
    align-items: center;
    padding: 0;
    margin-bottom: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    padding-bottom: 20rpx;
}

.country-code {
    margin-right: 20rpx;
    padding-right: 20rpx;
    border-right: 1rpx solid #f0f0f0;
    flex-shrink: 0;
}

.code-text {
    font-size: 32rpx;
    color: #333333;
    font-weight: 500;
}

.phone-input-wrapper {
    flex: 1;
}

.phone-input {
    width: 100%;
    font-size: 32rpx;
    color: #333333;
    line-height: 44rpx;
    padding: 0;
    border: none;
    outline: none;
    background: transparent;
}

.phone-input::placeholder {
    color: #cccccc;
}

/* 验证码输入组 */
.verify-code-group {
    display: flex;
    align-items: center;
    padding: 0;
    margin-bottom: 0;
}

.code-input-wrapper {
    flex: 1;
    margin-right: 20rpx;
}

.code-input {
    width: 100%;
    font-size: 32rpx;
    color: #333333;
    line-height: 44rpx;
    padding: 0;
    border: none;
    outline: none;
    background: transparent;
}

.code-input::placeholder {
    color: #cccccc;
}

/* 获取验证码按钮 */
.verify-code-btn {
    width: 200rpx;
    height: 64rpx;
    background: transparent;
    border: none;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
    flex-shrink: 0;
}

.verify-code-btn.disabled {
    cursor: not-allowed;
}

.verify-btn-text {
    font-size: 28rpx;
    color: #4ECDC4;
    font-weight: 400;
}

.verify-code-btn.disabled .verify-btn-text {
    color: #cccccc;
}

/* 底部区域 */
.bottom-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 40rpx 30rpx;
    padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
    background: #f8f9fa;
}

.save-btn {
    width: 100%;
    height: 88rpx;
    background: #4ECDC4;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(78, 205, 196, 0.3);
}

.btn-text {
    font-size: 32rpx;
    color: #ffffff;
    font-weight: 500;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
    .navbar {
        height: 80rpx;
        padding: 0 24rpx;
    }

    .content {
        padding: 32rpx 24rpx;
    }

    .input-section {
        padding: 32rpx 24rpx;
    }

    .bottom-section {
        padding: 32rpx 24rpx;
        padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
    }
}
</style>