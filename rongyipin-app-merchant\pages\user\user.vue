<template>
  <view class="container">
    <view class="title-bar" :class="{ 'show-title-bar': showHeader }">
      <text class="title">我的</text>
    </view>
    <scroll-view scroll-y class="scroll-container" @scroll="handleScroll" :scroll-top="scrollTop" :enhanced="true"
      :show-scrollbar="true" :bounces="true">
      <view class="horizontal-bar">
        <image src="@/static/app/my/horizontal.png"></image>
        <uni-icons style="margin-right: 30rpx;" type="gear" size="25" @click="settings()"></uni-icons>
      </view>
      <view class="auth-section">
        <!-- 实名认证 -->
        <view class="auth-item" v-if="!userInfo">
          <view class="auth-left">
            <view class="auth-icon warning">!</view>
            <view class="auth-info">
              <text class="auth-title">未完成实名认证</text>
              <text class="auth-desc">应相关法规要求，实名认证才可招聘</text>
            </view>
          </view>
          <view class="auth-btn" @click="RealName()">实名认证</view>
        </view>

        <!-- <view class="divider"></view> -->

        <!-- 商家资质认证 -->
        <view class="auth-item" v-if="is_auth != 1">
          <view class="auth-left">
            <view class="auth-icon doc">□</view>
            <view class="auth-info">
              <text class="auth-title">未完成商家资质认证</text>
              <text class="auth-desc">建议尽快完成，以免影响正常招聘</text>
            </view>
          </view>
          <view class="auth-btn outline">资质认证</view>
        </view>
        <view class="auth-item" v-if="is_auth == 1 && !userInfo">
          <view class="auth-left">
            获取到企业信息
          </view>
        </view>
      </view>
      <view class="mation" v-if="userInfo && is_auth == 1">
        <view class="mation_name">
          <image :src="usermei.avatarUrl" alt=""></image>
        </view>
        <view style="width: 77%;">
          <view
            style="margin-left: 20rpx;display: flex; justify-content: space-between;width: 100%;align-items: center;">
            <p>{{ usermei.name }}</p>
            <p @click="personal" style="font-size: 25rpx;">个人信息<uni-icons type="right" size="10"
                color="#fff"></uni-icons></p>
          </view>
          <view style="margin-left: 20rpx;font-size: 25rpx;margin-top: 10rpx;">
            {{ prises.name }}<text>.{{ usermei.job_position_name }}</text>
          </view>
        </view>
      </view>
      <view class="rights-sectionlate">
        <view class="banner-section">
          <swiper class="banner-swiper" :autoplay="true" :interval="3000" :duration="500" circular>
            <swiper-item v-for="(item, index) in banners" :key="index">
              <image :src="item.imageUrl" class="banner-image"></image>
            </swiper-item>
          </swiper>
        </view>

        <!-- 我的权益 -->
        <view class="rights-section">
          <view class="rights-header">
            <text class="rights-title">我的权益</text>

          </view>

          <!-- 权益数据 -->
          <view class="rights-data">
            <view class="rights-item" @click="handonline">
              <text class="rights-num">{{ residuallist.remaining_job_num }}/{{ residuallist.total_job_num }}</text>
              <text class="rights-label">在线职位</text>
            </view>
            <view class="rights-item">
              <text class="rights-num">{{ residuallist.remaining_job_refresh_num }}/{{ residuallist.total_job_refresh_num
                }}</text>
              <text class="rights-label">职位刷新</text>
            </view>
            <view class="rights-item" @click="handjobUpdate">
              <text class="rights-num">{{ residuallist.remaining_tel_num }}/{{ residuallist.total_tel_num }}</text>
              <text class="rights-label">电话直拨</text>
            </view>
            <view class="rights-item" @click="handinvita">
              <text class="rights-num">{{ residuallist.remaining_chat_num }}/{{ residuallist.total_chat_num }}</text>
              <text class="rights-label">在线畅聊</text>
            </view>
            <!-- <view class="rights-item">
              <text class="rights-num">0</text>
              <text class="rights-label">智能群发</text>
            </view> -->
          </view>

        </view>

        <!-- 招聘道具 -->
        <view class="tools-section">
          <view class="section-title">招聘道具</view>

          <view class="tools-grid">
            <view class="tool-item" v-for="(tool, index) in tools" :key="index" @click="handtoolClick(tool)">
              <image :src="tool.icon" class="tool-icon"></image>
              <text class="tool-name">{{ tool.name }}</text>
              <text class="tool-desc">{{ tool.desc }}</text>
            </view>
          </view>
        </view>

        <!-- 其他功能 -->
        <view class="other-section">
          <view class="section-title">其它功能</view>

          <view class="other-grid">
            <view class="other-item" v-for="(item, index) in otherFunctions" :key="index"
              @click="handleItemClick(item)">
              <text class="other-icon">{{ item.icon }}</text>
              <text class="other-name">{{ item.name }}</text>
            </view>
          </view>

          <!-- 客服信息 -->


        </view>
        <view class="service-info">
          <text class="service-text">客服电话 05715607688 工作时间 9:00-18:00</text>
          <text class="service-text">推荐算法举报与未成年人举报渠道同上</text>
          <text class="service-text">人力资源许可证 营业执照</text>
          <text class="service-text">浙ICP备14013428号-9A</text>
        </view>
      </view>
    </scroll-view>
    <!-- 新人福利 -->
    <!-- <view class="benefit-section">
      <view class="benefit-header">
        <text class="check-icon">✓</text>
        <text class="benefit-title">新人福利</text>
      </view>

      <view class="benefit-content">
        <text class="benefit-text">在青团社，最快5分钟招到人</text>
        <text class="benefit-desc">发布成功24小时内，曝光量提升50%</text>
      </view>

      <view class="publish-btn" @click="handleSecond">
        <text>发布职位</text>
      </view>
    </view> -->


  </view>
</template>

<script>
import { addApi } from "@/utils/api"
export default {
  data() {
    return {
      scrollTop: 0,
      showHeader: false,
      tools: [
        {
          icon: '@/static/app/home/<USER>',
          name: '置顶职位',
          desc: '列表置顶岗位',
          url: '/pages/user/recruitment/topPosition'
        },
        {
          icon: '/static/tool-invite.png',
          name: '急招岗位',
          desc: '亮眼急招标识',
          url: '/pages/user/recruitment/urgentPositin'
        },
        {
          icon: '/static/tool-traffic.png',
          name: '在线畅聊',
          desc: '邀请投递人',
          url: '/pages/user/recruitment/chatOnline'
        },
        {
          icon: '/static/tool-ai.png',
          name: '电话直拨',
          desc: '直接对接牛人',
          url: '/pages/user/recruitment/directDial'
        },
        {
          icon: '/static/tool-ai.png',
          name: '职位刷新',
          desc: '增加职位曝光',
          url: '/pages/user/recruitment/jobUpdate'
        }
      ],
      otherFunctions: [
        { icon: '↗', name: '招聘数据', url: '/pages/user/function/recruitmen' },
        { icon: '☰', name: '积分中心', url: '/pages/user/function/integration' },
        { icon: '?', name: '公司主页', url: '/pages/user/function/company' },
        { icon: '!', name: '发票服务', url: '/pages/user/function/invoice' },
        { icon: '⚙', name: '我的收藏', url: '/pages/user/function/collection' },
        { icon: '📄', name: '面试记录', url: '/pages/user/usercontont/InterviewList' },
        { icon: '⚪', name: '我的客服' ,url:'/pages/smart/serviceDemo'},
        { icon: '⚪', name: '隐私政策', url: '/pages/user/function/settings' },
        { icon: '⚪', name: '建议反馈', url: '/pages/user/function/feedback' }
      ],
      is_auth: null,
      userInfo: false,
      usermei: {},
      prises: {},
      banners: [
        { imageUrl: '/static/app/audit/illustration.png' },
        { imageUrl: '/static/app/position/detale.png' },
        { imageUrl: '/static/app/audit/illustration.png' }
      ],
      residuallist: {}
    }
  },
  async onShow() {

    const auth = uni.getStorageSync('is_auth')
    this.is_auth = auth.is_auth
    this.prises = auth
    this.userInfo = uni.getStorageSync('usinfo')
    this.usermei = this.userInfo
    // let res = await priseApi.getUserInfo()
    // if (res.code == 200) {
    //   if (res.data.identity) {
    //     this.userInfo = true
    //     this.usermei = res.data
    //   } else {

    //     this.userInfo = false
    //   }
    // } else {
    //   this.userInfo = false
    // }
    uni.hideLoading()

    this.residual()
  },
  methods: {
    async residual() {

      const res = await addApi.getUserAddserviceNum()
      this.residuallist = res.data
      uni.hideLoading()
    },
    handtoolClick(item) {
      console.log(item.url);
      uni.navigateTo({
        url: item.url
      });
    },
    personal() {
      uni.navigateTo({
        url: './personal/index'
      })

    },
    RealName() {
      uni.navigateTo({
        url: './realName'
      })
    },
    settings() {
      uni.navigateTo({
        url: './settings/settings'
      })
    },
    handleItemClick(item) {
      console.log(item);
      // if (item.click == "navigateToSettings") {
      //   uni.navigateTo({
      //     url: './settings/settings'
      //   });
      // } else if (item.click == 'HelpCenter') {
      //   uni.navigateTo({
      //     url: './settings/helpCenter'
      //   });
      // }
      uni.navigateTo({
        url: item.url
      });
    },
    handonline() {
      console.log('handonline');
      uni.navigateTo({
        url: './interests/positions'
      });
    },
    handleScroll(e) {
      this.scrollTop = e.detail.scrollTop;
      this.showHeader = e.detail.scrollTop > 30;
    },
    handjobUpdate() {
      console.log('handjobUpdate');
      uni.navigateTo({
        url: './interests/jobUpdate'
      });
    },
    handinvita() {
      console.log('handinvita');
      uni.navigateTo({
        url: './interests/invitation'
      });
    },
    handleSecond() {
      console.log('handlePublish');
      uni.navigateTo({
        url: '../second/second'
      });
    }
  }
}
</script>

<style scoped lang="scss">
.title-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background-color: #02bdc4;
  display: flex;
  align-items: center;
  padding: 0 32rpx;
  padding-top: var(--status-bar-height);
  /* 适配状态栏高度 */
  transform: translateY(-100%);
  transition: all 0.1s ease-out;
  z-index: 999;
  opacity: 0;

  &.show-title-bar {
    transform: translateY(0);
    opacity: 1;
  }

  .title {
    font-size: 36rpx;
    color: #fff;
    font-weight: bold;
  }
}

page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.container {
  /* display: flex;
  flex-direction: column; */
  /* 减去 tabbar 高度(100rpx) */
  height: calc(100vh - 100rpx);
  /* position: relative; */
  // background-color: #02bdc4;
  padding-top: 1px;
}

/* 状态栏样式 */
.status-bar {
  background-color: #02bdc4;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  color: white;
  font-weight: bold;
}

.time {
  font-size: 36rpx;
}

.status-center {
  display: flex;
  align-items: center;
  background-color: #000;
  border-radius: 40rpx;
  padding: 10rpx 30rpx;
  width: 300rpx;
  justify-content: space-between;
}

.avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
}

.sound-wave {
  width: 48rpx;
  height: 48rpx;
}

.status-right {
  display: flex;
  align-items: center;
}

.signal {
  margin-right: 10rpx;
}

.battery {
  background-color: white;
  color: #02bdc4;
  border-radius: 20rpx;
  padding: 0 10rpx;
  font-size: 24rpx;
}

/* 我的标题样式 */
.my-header {
  position: fixed;
  top: 88rpx;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #02bdc4;
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  padding-left: 32rpx;
  z-index: 100;
  transform: translateY(-100%);
  transition: transform 0.3s;
}

.my-header.show {
  transform: translateY(0);
}

.scroll-container {
  flex: 1;
  height: calc(100vh - 100rpx);
  /* 减去tabbar高度 */
  overflow-y: auto;
  /* 允许垂直滚动 */
  -webkit-overflow-scrolling: touch;
  /* iOS流畅滚动 */
}

/* 自定义滚动条样式 */
.scroll-container ::-webkit-scrollbar {
  width: 0rpx;
  background-color: transparent;
}

.scroll-container ::-webkit-scrollbar-thumb {
  border-radius: 10rpx;
  background-color: rgba(241, 233, 233, 0.1);
  background: none;
}

.scroll-container ::-webkit-scrollbar-track {
  background-color: transparent;
}

.scroll-content {
  min-height: 100%;
  padding-bottom: 240rpx;
  /* 为底部预留空间 */
}

.horizontal-bar {
  width: 100%;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 70rpx;

  image {
    width: 40rpx;
    height: 40rpx;
    margin-right: 60rpx;
  }
}

/* 认证提醒区域 */
.auth-section {
  background-color: #0a2057;

  color: white;
  width: 95%;
  margin: 0 auto;
  // margin-top: 80rpx;
  border-radius: 20rpx 20rpx 0 0;
}

.auth-item {
  /* display: flex;
  justify-content: space-between; */
  align-items: center;
  padding: 30rpx 16rpx 10rpx 16rpx;
  position: relative;
}

.auth-item:last-child {
  /* display: flex;
  justify-content: space-between; */
  align-items: center;
  padding: 5rpx 16rpx 75rpx 16rpx;
  position: relative;
}

.auth-left {
  display: flex;
  /* align-items: center; */
}

.auth-icon {
  width: 30rpx;
  height: 30rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10rpx 30rpx 20rpx 20rpx;
  font-size: 36rpx;
  font-weight: bold;

}

.warning {
  background-color: #ff9500;
}

.doc {
  background-color: white;
  color: #0a2057;
}

.auth-info {
  display: flex;
  flex-direction: column;
}

.auth-title {
  font-size: 27rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.auth-desc {
  font-size: 24rpx;
  opacity: 0.8;
}

.auth-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background-color: #02bdc4;
  color: white;
  padding: 10rpx 32rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.auth-btn.outline {
  margin-top: -20rpx;
  background-color: transparent;
  border: 1rpx solid rgba(255, 255, 255, 0.5);
}

.divider {
  height: 2rpx;
  background-color: #283757;
  margin: 20rpx 40rpx;
}

.mation {
  width: 100%;
  margin-bottom: 50rpx;
  display: flex;
  align-items: center;
  // color: white;

  .mation_name {
    // display: flex;
    // align-items: center;
    margin-left: 30rpx;

    image {
      width: 100rpx;
      height: 100rpx;
    }
  }

  .mation_right {
    font-size: 30rpx;
    margin-right: 30rpx;
  }
}

/* 招聘特权 */
.privilege-section {
  width: 95.1%;
  margin: 0 auto;
  background-color: white;
  /* padding: 32rpx; */
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: -45rpx;
  background-color: #dddfea;
  border-radius: 30rpx 30rpx 0 0;
}

.privilege-left {
  display: flex;
  align-items: center;
  padding: 40rpx 16rpx 40rpx 16rpx;

}

.privilege-icon {
  width: 30rpx;
  height: 30rpx;
  background-color: #ff9500;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0rpx 26rpx 0rpx 26rpx;
}

.vip-icon {
  width: 40rpx;
  height: 40rpx;
}

.privilege-text {
  font-size: 32rpx;
  font-weight: bold;
}

.buy-btn {
  background-color: #ebd5cb;
  color: #683d2a;
  padding: 10rpx 30rpx;
  margin-right: 20rpx;
  border-radius: 10rpx;
  font-size: 24rpx;
  border: 1rpx solid white;
}

.rights-sectionlate {
  background: rgb(246, 247, 249);
  border-radius: 30rpx 30rpx 0 0;
  width: 100%;
  min-height: calc(100vh - 100rpx) !important;
  /* 减去 tabbar 和顶部区域的高度 */
  margin-top: -5rpx;
  background-color: #f6f7fb;
}

/* 我的权益 */
.rights-section {
  width: 95%;
  margin: 0 auto;
  background-color: white;
  padding: 0 0 32rpx 0;
  border-radius: 30rpx;
  /* margin-top: -5rpx; */
  // height: 400rpx;
  // padding: 20 0 40rpx 0;
}

.banner-section {
  width: 100%;
  margin: 0 auto;
  margin-top: 40rpx;
  border-radius: 20rpx;
  height: 200rpx;
  overflow: hidden;
}

.banner-swiper {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.rights-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  padding: 32rpx 32rpx 0 32rpx;
}

.rights-title {
  font-size: 36rpx;
  font-weight: bold;
}

.my-orders {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 28rpx;
}

.arrow {
  margin-left: 10rpx;
}

.rights-data {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;

}

.rights-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
}

.rights-num {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.rights-label {
  font-size: 24rpx;
  color: #999;
}

/* 招聘道具 */
.tools-section {
  background-color: white;
  /* padding: 32rpx; */
  /* margin-top: 20rpx; */
  width: 95%;
  margin: 20rpx auto;
  background-color: white;
  /* padding: 32rpx; */
  border-radius: 30rpx;
  /* margin-top: -5rpx; */
  height: 280rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  /* margin-bottom: 40rpx; */
  padding: 32rpx 32rpx 0 32rpx;
}

.tools-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.tool-item {
  // width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.tool-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 16rpx;
}

.tool-name {
  font-size: 25rpx;
  margin-bottom: 8rpx;
}

.tool-desc {
  font-size: 20rpx;
  color: #999;
  text-align: center;
}

/* 其他功能 */
.other-section {
  /* padding: 32rpx; */
  margin-top: 20rpx;
  width: 95%;
  margin: 20rpx auto;
  background-color: white;
  /* padding: 32rpx; */
  border-radius: 30rpx;
  /* margin-top: -5rpx; */
  // height: 420rpx; 
}

.other-grid {
  display: flex;
  flex-wrap: wrap;
}

.other-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
  margin-top: 20rpx;
}

.other-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.other-name {
  font-size: 24rpx;
  color: #333;
}

.service-info {
  /* padding: 32rpx 0; */
  display: flex;
  flex-direction: column;
  align-items: center;
  /* padding-bottom: 400rpx; */
}

.service-text {
  font-size: 28rpx;
  color: #c0c1c5;
  margin-bottom: 10rpx;
  text-align: center;
}

/* 新人福利 */
.benefit-section {
  position: fixed;
  bottom: 0rpx;
  left: 0;
  right: 0;
  z-index: 99;
  background-color: #0ec7d2;
  padding: 32rpx;
  color: white;
  border-radius: 30rpx 30rpx 0 0;
}

.benefit-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.check-icon {
  margin-right: 10rpx;
  font-weight: bold;
}

.benefit-title {
  font-size: 36rpx;
  font-weight: bold;
}

.benefit-content {
  margin-bottom: 40rpx;
}

.benefit-text {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.benefit-desc {
  font-size: 28rpx;
  opacity: 0.8;
}

.publish-btn {
  background-color: white;
  color: #333;
  padding: 24rpx;
  border-radius: 8rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
}

/* 底部导航栏 */
.tab-bar {
  display: flex;
  height: 100rpx;
  background-color: white;
  border-top: 1px solid #eee;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #999;
}

.tab-item.active {
  color: #333;
}

.tab-item.publish {
  color: white;
}

.tab-icon {
  font-size: 40rpx;
  margin-bottom: 4rpx;
}

.tab-item.publish .tab-icon {
  background-color: #00c2b3;
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: -30rpx;
  margin-bottom: -4rpx;
}

.safe-area-bottom {
  height: 40rpx;
  background-color: white;
}
</style>