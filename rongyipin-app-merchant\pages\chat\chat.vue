<template>
	<view class="chat-container">
		<!-- 顶部导航栏 -->
		<view class="chat-header" :style="{ paddingTop: statusBarHeight + 'px' }">
			<!-- 顶部标题栏 -->
			<view class="header-top">
				<view class="back-btn" @click="goBack">
					<text class="back-icon">‹</text>
				</view>
				<view class="header-center">
					<text class="user-name">{{ userInfo.username || '王女士' }}</text>
					<text class="user-status">{{ userInfo.job_name }}</text>
				</view>
				<view class="header-right">
					<view class="star-btn" @click="toggleFavorite">

						<image class="star-icon"
							:src="isFavorited ? '/static/star-filled.svg' : '/static/star-empty.svg'"
							mode="aspectFit" />
					</view>
					<view class="more-btn" @click="showMoreOptions">
						<text class="more-icon">⋯</text>
					</view>
				</view>
			</view>

			<!-- 功能按钮区域 -->
			<view class="function-buttons">
				<view class="function-btn" @click="requestResume">
					<view class="btn-icon resume-icon">📄</view>
					<text class="btn-text">邀投简历</text>
				</view>
				<view class="function-btn" @click="exchangeContact">
					<view class="btn-icon contact-icon">💬</view>
					<text class="btn-text">换电话/微信</text>
				</view>
				<view class="function-btn" @click="showPopup = true">
					<view class="btn-icon interview-icon">📋</view>
					<text class="btn-text">约面试</text>
				</view>
				<view class="function-btn" @click="markUnsuitable">
					<view class="btn-icon unsuitable-icon">✕</view>
					<text class="btn-text">不合适</text>
				</view>
			</view>
		</view>

		<!-- 聊天消息区域 -->
		<scroll-view class="chat-messages" scroll-y="true" :scroll-top="scrollTop"
			:style="{ height: chatHeight + 'px' }" scroll-with-animation="true" @scrolltoupper="onLoadMore"
			:upper-threshold="100" @click="hideAllPanels">

			<!-- 上拉加载状态 -->
			<view class="loading-status" v-if="loading">
				<view class="loading-more">
					<text class="loading-text"><u-loading-icon></u-loading-icon></text>
				</view>
			</view>

			<view class="message-list">
				<view class="message-item" v-for="(message, index) in messageList" :key="index"
					v-if="message && message.type">
					<!-- 时间分割线 -->
					<view class="time-divider" v-if="message.showTime">
						<text class="time-text">{{ message.time }}</text>
					</view>

					<!-- msgTag 类型消息 - 居中灰色文字，无气泡 -->
					<view v-if="message.type === 'exchanged'" class="msg-tag-wrapper">

						<!-- {{ message }} -->
						<!-- 简历相关 -->
						<!-- 微信交换请求卡片 - 动态内容 -->
						<view v-if="message.item === 'wechat' && message.from_type === 'job_seeker'">
							<!-- 待处理状态 -->
							<template v-if="message.exchanged_status === 'pending'">
								<view class="wechat-exchange-card">
									<view class="exchange-header">
										<view class="wechat-icon">💬</view>
										<text class="exchange-title">求职者想要与您交换微信，方便沟通</text>
									</view>
									<view class="exchange-subtitle">
										<text class="subtitle-text">同意后，将会共享您的微信信息</text>
									</view>
									<view class="exchange-actions">
										<view class="action-btn reject-btn"
											@click="handleWechatExchange(message, 'rejected')">
											<text class="btn-text">拒绝</text>
										</view>
										<view class="action-btn accept-btn"
											@click="handleWechatExchange(message, 'accepted')">
											<text class="btn-text">同意</text>
										</view>
									</view>
								</view>
							</template>
							<template v-if="message.exchanged_status === 'accepted'">
								<view class="wechat-exchange-card">
									<view class="phone-info">
										<text class="phone-label">微信号：</text>
										<text class="phone-number">{{ message.talent_contact.wechat || '暂无微信号' }}</text>
									</view>
									<view class="exchange-actions">
										<view class="action-btn copy-btn"
											@click="copyPhoneNumber(message.talent_contact.wechat)">
											<text class="btn-text">复制微信号</text>
										</view>
									</view>
								</view>
							</template>
							<!-- 已同意状态 - 显示手机号 -->
							<template v-else-if="message.exchanged_status === 'rejecte'">
								<view class="wechat-exchange-card">
									<view class="rejected-info">
										<text class="rejected-text">对方已拒绝此微信交换请求</text>
									</view>
								</view>
							</template>
							<template v-else-if="message.exchanged_status === 'approve'">
								<view class="msg-tag-text">
									您已同意交换微信
								</view>
							</template>

							<template v-else-if="message.exchanged_status === 'refuse'">
								<view class="msg-tag-text">
									您已拒绝交换微信
								</view>
							</template>
						</view>
						<view v-else-if="message.item === 'wechat' && message.from_type === 'recruiter'">
							<template v-if="message.exchanged_status === 'accepted'">
								<view class="wechat-exchange-card">
									<view class="phone-info">
										<text class="phone-label">微信号：</text>
										<text class="phone-number">{{ message.talent_contact.wechat || '暂无微信号' }}</text>
									</view>
									<view class="exchange-actions">
										<view class="action-btn copy-btn"
											@click="copyPhoneNumber(message.talent_contact.wechat)">
											<text class="btn-text">复制微信号</text>
										</view>
									</view>
								</view>
							</template>

							<!-- 已拒绝状态 -->
							<template v-else-if="message.exchanged_status === 'rejecte'">
								<view class="wechat-exchange-card">
									<view class="rejected-info">
										<text class="rejected-text">您已拒绝此微信交换请求</text>
									</view>
								</view>
							</template>
							<template v-else-if="message.exchanged_status === 'approve'">
								<view class="msg-tag-text">
									您已同意交换微信
								</view>
							</template>
							<template v-else-if="message.exchanged_status === 'refuse'">
								<view class="msg-tag-text">
									您已拒绝交换微信
								</view>
							</template>
						</view>
						<view v-else-if="message.item === 'phone' && message.from_type === 'job_seeker'">
							<template v-if="message.exchanged_status === 'pending'">
								<view class="wechat-exchange-card">
									<view class="exchange-header">
										<view class="wechat-icon">💬</view>
										<text class="exchange-title">求职者想要与您交换手机号，方便沟通</text>
									</view>
									<view class="exchange-subtitle">
										<text class="subtitle-text">同意后，将会共享您的手机号</text>
									</view>
									<view class="exchange-actions">
										<view class="action-btn reject-btn"
											@click="handleWechatExchange(message, 'rejected')">
											<text class="btn-text">拒绝</text>
										</view>
										<view class="action-btn accept-btn"
											@click="handleWechatExchange(message, 'accepted')">
											<text class="btn-text">同意</text>
										</view>
									</view>
								</view>
							</template>
							<template v-if="message.exchanged_status === 'accepted'">
								<view class="wechat-exchange-card">
									<view class="phone-info">
										<text class="phone-label">手机号：</text>
										<text class="phone-number">{{ message.talent_contact.phone || '暂无手机号' }}</text>
									</view>
									<view class="exchange-actions">
										<view class="action-btn copy-btn"
											@click="copyPhoneNumber(message.talent_contact.phone)">
											<text class="btn-text">复制手机号</text>
										</view>
									</view>
								</view>
							</template>
							<!-- 已同意状态 - 显示手机号 -->
							<template v-else-if="message.exchanged_status === 'rejecte'">
								<view class="wechat-exchange-card">
									<view class="rejected-info">
										<text class="rejected-text">对方已拒绝此手机号交换请求</text>
									</view>
								</view>
							</template>
							<template v-else-if="message.exchanged_status === 'approve'">
								<view class="msg-tag-text">
									您已同意交换手机号
								</view>
							</template>

							<template v-else-if="message.exchanged_status === 'refuse'">
								<view class="msg-tag-text">
									您已拒绝交换手机号
								</view>
							</template>
						</view>
						<view v-else-if="message.item === 'phone' && message.from_type === 'recruiter'">
							<template v-if="message.exchanged_status === 'accepted'">
								<view class="wechat-exchange-card">
									<view class="phone-info">
										<text class="phone-label">手机号：</text>
										<text class="phone-number">{{ message.talent_contact.phone || '暂无手机号' }}</text>
									</view>
									<view class="exchange-actions">
										<view class="action-btn copy-btn"
											@click="copyPhoneNumber(message.talent_contact.phone)">
											<text class="btn-text">复制手机号</text>
										</view>
									</view>
								</view>
							</template>

							<!-- 已拒绝状态 -->
							<template v-else-if="message.exchanged_status === 'rejecte'">
								<view class="wechat-exchange-card">
									<view class="rejected-info">
										<text class="rejected-text">您已拒绝此手机号交换请求</text>
									</view>
								</view>
							</template>
							<template v-else-if="message.exchanged_status === 'approve'">
								<view class="msg-tag-text">
									对方已同意交换手机号
								</view>
							</template>
							<template v-else-if="message.exchanged_status === 'refuse'">
								<view class="msg-tag-text">
									对方拒绝交换手机号
								</view>
							</template>
						</view>
						<view v-else-if="message.item === 'vitae' && message.from_type === 'job_seeker'">
							<template v-if="message.exchanged_status === 'pending'">
								<view class="wechat-exchange-card">
									<view class="exchange-header">
										<view class="wechat-icon">💬</view>
										<text class="exchange-title">求职者邀您查看附件简历</text>
									</view>
									<view class="exchange-subtitle">
										<text class="subtitle-text">同意后将会受到求职者附件简历</text>
									</view>
									<view class="exchange-actions">
										<view class="action-btn reject-btn"
											@click="handleWechatExchange(message, 'rejected')">
											<text class="btn-text">拒绝</text>
										</view>
										<view class="action-btn accept-btn"
											@click="handleWechatExchange(message, 'accepted')">
											<text class="btn-text">同意</text>
										</view>
									</view>
								</view>
							</template>
							<template v-if="message.exchanged_status === 'accepted'">
								<view class="resume-file-card">
									<view class="file-info">
										<view class="file-icon">
											<image src="/static/images/pdf-icon.png" class="icon-image"
												mode="aspectFit"></image>
										</view>
										<text class="file-name">{{ message.talent_contact.vitae ?
											message.talent_contact.vitae.attachment_name : '' }}</text>
									</view>
									<view class="preview-action">
										<view class="preview-btn" @click="previewResume(message.talent_contact.vitae)">
											<text class="preview-text">点击预览附件简历</text>
										</view>
									</view>
								</view>
							</template>
							<!-- 已同意状态 - 显示手机号 -->
							<template v-else-if="message.exchanged_status === 'rejecte'">
								<view class="resume-file-card">
									<text class="file-name">对方已拒绝发送简历</text>
								</view>
							</template>
							<template v-else-if="message.exchanged_status === 'approve'">
								<view class="msg-tag-text">
									您已同意对方发送简历
								</view>
							</template>

							<template v-else-if="message.exchanged_status === 'refuse'">
								<view class="msg-tag-text">
									您已拒绝对方发送简历
								</view>
							</template>
						</view>
						<view v-else-if="message.item === 'vitae' && message.from_type === 'recruiter'">
							<template v-if="message.exchanged_status === 'accepted'">
								<view class="resume-file-card">
									<view class="file-info">
										<view class="file-icon">
											<image src="/static/images/pdf-icon.png" class="icon-image"
												mode="aspectFit"></image>
										</view>
										<text class="file-name">{{ message.talent_contact.vitae ?
											message.talent_contact.vitae.attachment_name : '' }}</text>
									</view>
									<view class="preview-action">
										<view class="preview-btn" @click="previewResume(message.talent_contact.vitae)">
											<text class="preview-text">点击预览附件简历</text>
										</view>
									</view>
								</view>
							</template>
							<!-- 已同意状态 - 显示手机号 -->
							<template v-else-if="message.exchanged_status === 'rejecte'">
								<view class="resume-file-card">
									<text class="file-name">对方已拒绝发送简历</text>
								</view>
							</template>
							<template v-else-if="message.exchanged_status === 'approve'">
								<view class="msg-tag-text">
									您已同意对方发送简历
								</view>
							</template>

							<template v-else-if="message.exchanged_status === 'refuse'">
								<view class="msg-tag-text">
									您已拒绝对方发送简历
								</view>
							</template>
						</view>
						<view v-else class="msg-tag-text">
							{{ message.content }}
						</view>
					</view>
					<view v-else-if="message.type === 'invitation'" class="msg-tag-wrapper">

						<!-- {{ message }} -->
						<!-- 简历相关 -->
						<text v-if="message.invitation_status === 'pending'" class="msg-tag-text">
							<view class="msg-tag-text">
								{{ message.content }}
							</view>
						</text>
						<text v-else-if="message.invitation_status === 'accepted'" class="msg-tag-text">
							<view class="interview-invitation-card">
								<view class="invitation-header">
									<view class="invitation-icon">�</view>
									<text class="invitation-title">现场面试邀请</text>
								</view>

								<view class="invitation-content">
									<view class="content-row">
										<view class="row-icon">💼</view>
										<text class="row-text">{{ infoname || '做月入过万网红 10-15K 日结 招小...'
										}}</text>
									</view>
									<view class="content-row">
										<view class="row-icon">🕐</view>
										<text class="row-text">{{ message.interview_time || '2025-07-14 星期一 15:00'
										}}</text>
									</view>
									<view class="content-row">
										<view class="row-icon">📍</view>
										<text class="row-text">{{ message.address || '邯郸从台区国际商务中心' }}</text>
									</view>
								</view>

								<view class="invitation-actions">
									<view class="action-btn detail-btn" @click="onClick(message)">
										<text class="btn-text">查看详情</text>
									</view>
									<view class="action-btn accept-btn">
										<text class="btn-text">对方已接受</text>
									</view>
								</view>
							</view>
						</text>
						<text v-else-if="message.invitation_status === 'rejecte'" class="msg-tag-text">
							<view class="interview-invitation-card">
								<view class="invitation-header">
									<view class="invitation-icon">�</view>
									<text class="invitation-title">现场面试邀请</text>
								</view>

								<view class="invitation-content">
									<view class="content-row">
										<view class="row-icon">💼</view>
										<text class="row-text">{{ infoname || '做月入过万网红 10-15K 日结 招小...'
										}}</text>
									</view>
									<view class="content-row">
										<view class="row-icon">🕐</view>
										<text class="row-text">{{ message.interview_time || '2025-07-14 星期一 15:00'
										}}</text>
									</view>
									<view class="content-row">
										<view class="row-icon">📍</view>
										<text class="row-text">{{ message.address || '邯郸从台区国际商务中心' }}</text>
									</view>
								</view>

								<view class="invitation-actions">
									<view class="action-btn detail-btn" @click="onClick(message)">
										<text class="btn-text">查看详情</text>
									</view>
									<view class="action-btn accept-btn">
										<text class="btn-text">对方已拒绝</text>
									</view>
								</view>
							</view>
						</text>
						<!-- <text v-else class="msg-tag-text">
							{{ message.content }}
						</text> -->
					</view>

					<!-- 普通消息内容 -->
					<view v-else class="message-wrapper" :class="{ 'message-right': isMessageRight(message) }">
						<image v-if="!isMessageRight(message)" class="message-avatar" :src="options.avatarUrl"
							mode="aspectFill" @click="previewImage(message)">
						</image>

						<view class="message-bubble" :class="{ 'bubble-self': isMessageRight(message) }">
							<u-tooltip :text="message.content" v-if="message.type === 'text'">
								<text class="message-text">{{ message.content }}</text>
							</u-tooltip>
							<!-- 图片消息 -->
							<image v-if="message.type === 'image'" class="message-image" :src="message.content"
								mode="aspectFill" @click="previewImage(message)"></image>

							<!-- 位置消息 -->
							<view v-if="message.type === 'location'" class="message-location"
								@click="openLocation(message)">
								<view class="location-icon">📍</view>
								<view class="location-info">
									<text class="location-name">{{ message.content.name }}</text>
									<text class="location-address">{{ message.content.address }}</text>
								</view>
							</view>

							<!-- 消息状态（仅右侧消息显示） -->
							<view v-if="isMessageRight(message) && message.status" class="message-status"
								:class="'status-' + message.status">
								<text v-if="message.status === 'sending'" class="status-icon sending">...</text>
								<text v-else-if="message.status === 'sent'" class="status-icon sent">✓</text>
								<text v-else-if="message.status === 'failed'" class="status-icon failed">✗</text>
							</view>
						</view>

						<!-- <image v-if="isMessageRight(message)" class="message-avatar" src="/static/my-avatar.png"
							mode="aspectFill"></image> -->
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 底部输入区域 -->
		<view class="chat-input-area" :style="{ paddingBottom: keyboardHeight + 'px' }" @click.stop>
			<!-- 输入框容器 -->
			<view class="input-container">
				<view class="input-left">
					<input class="message-input" v-model="inputText" placeholder="请输入消息..." :focus="inputFocus"
						@focus="onInputFocus" @blur="onInputBlur" @confirm="sendTextMessage" confirm-type="send" />
				</view>

				<view class="input-actions">
					<view class="action-btn" @click="toggleEmoji" :class="{ active: showEmojiPanel }">
						<text class="emoji-icon">😊</text>
					</view>
					<view class="action-btn" @click="toggleMoreMenu" :class="{ active: showMoreMenu }">
						<text class="plus-icon">+</text>
					</view>
					<view class="send-btn" @click="sendTextMessage" v-if="inputText.trim()">
						<text class="send-text">发送</text>
					</view>
				</view>
			</view>

			<!-- Emoji表情面板 -->
			<view class="emoji-panel" v-if="showEmojiPanel" @click.stop>
				<view class="emoji-grid">
					<text class="emoji-item" v-for="emoji in emojiList" :key="emoji" @click="insertEmoji(emoji)">
						{{ emoji }}
					</text>
				</view>
			</view>

			<!-- 加号菜单 -->
			<view class="more-menu" v-if="showMoreMenu" @click.stop>
				<view class="menu-grid">
					<view class="menu-item" @click="chooseFromAlbum">
						<view class="menu-icon album-icon">📷</view>
						<text class="menu-text">相册</text>
					</view>
					<view class="menu-item" @click="takePhoto">
						<view class="menu-icon camera-icon">📸</view>
						<text class="menu-text">相机</text>
					</view>
					<!-- <view class="menu-item" @click="chooseLocation">
						<view class="menu-icon location-icon">📍</view>
						<text class="menu-text">位置</text>
					</view> -->
				</view>
			</view>


		</view>

		<!-- 自定义求简历弹窗 -->
		<view class="custom-modal-overlay" v-if="showResumeModal" @click="closeResumeModal">
			<view class="custom-modal" @click.stop>
				<view class="modal-header">
					<text class="modal-title">确定向人才请求简历吗？</text>
				</view>

				<view class="modal-content">
					<view class="modal-message">
						<text class="message-text">确定后，简历请求将发送至对方，对方同意后将会看到附件简历</text>
					</view>
				</view>

				<view class="modal-actions">
					<view class="action-btn cancel-btn" @click="closeResumeModal">
						<text class="btn-text">取消</text>
					</view>
					<view class="action-btn confirm-btn" @click="sendResumeRequest">
						<text class="btn-text">确定</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 换电话/微信底部弹出层 -->
		<view class="bottom-popup-overlay" v-if="showContactModal" @click="closeContactModal"
			:class="{ 'show': contactModalVisible }">
			<view class="bottom-popup" @click.stop :class="{ 'show': contactModalVisible }">
				<view class="popup-header">
					<text class="popup-title">换电话/微信</text>
					<view class="close-btn" @click="closeContactModal">
						<text class="close-icon">✕</text>
					</view>
				</view>

				<view class="popup-content">
					<view class="contact-options">
						<view class="option-item" @click="requestPhone">
							<view class="option-icon phone-icon">📞</view>
							<view class="option-info">
								<text class="option-title">请求电话号码</text>
								<text class="option-desc">向候选人请求电话联系方式</text>
							</view>
						</view>

						<view class="option-item" @click="requestWechat">
							<view class="option-icon wechat-icon">💬</view>
							<view class="option-info">
								<text class="option-title">请求微信号</text>
								<text class="option-desc">向候选人请求微信联系方式</text>
							</view>
						</view>

						<!-- <view class="option-item" @click="exchangeBoth">
							<view class="option-icon exchange-icon">🔄</view>
							<view class="option-info">
								<text class="option-title">互换联系方式</text>
								<text class="option-desc">同时交换电话和微信</text>
							</view>
						</view> -->
					</view>
				</view>
			</view>
		</view>
		<u-popup :show="showPopup" :round="20" @close="close" @open="open">
			<view class="u-popup-content">
				<view class="interview-card">
					<view class="interview-title">邀请{{ userInfo.username }}参加线下面试</view>
					<view class="interview-row">
						<text class="row-label">面试类型</text>
						<button class="row-btn">线下面试</button>
					</view>
					<view class="interview-row">
						<text class="row-label">面试职位</text>
						<text class="row-value" v-if="userInfo.job_info && userInfo.job_info.type == 1">全职 {{
							userInfo.job_info.name }} {{
								userInfo.job_info.min_salary }}-{{ userInfo.job_info.max_salary }}k</text>
						<text class="row-value" v-else-if="userInfo.job_info && userInfo.job_info.type == 2">兼职 {{
							userInfo.job_info.name }} {{
								userInfo.job_info.salary }}k</text>
					</view>
					<view class="interview-row" @click="toggleMapPicker">
						<text class="row-label">面试地址 <text class="required-star">*</text></text>
						<view>
							<text class="row-value" style="margin-right: 20rpx; width: 300rpx;overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;">{{ city.name || '请选择面试地址' }}</text>
							<uni-icons type="right" size="20"></uni-icons>
						</view>
					</view>
					<view class="interview-row">
						<text class="row-label">联系人</text>
						<text class="row-value">{{ userinfo.name }} - {{ userinfo.telephone }}</text>
					</view>
					<view class="interview-tip">
						为保证牛人联系到企业，牛人接受面试后，可查看职位发布人和联系人的电话
					</view>
				</view>

				<view class="interview-date">
					<text class="row-label">面试时间 <text class="required-star">*</text></text>
					<uni-datetime-picker type="time" v-model="time" :hide-second="true" :clear-icon="false"
						placeholder="请选择时间" />
				</view>
				<view class="notice-card">
					<view class="notice-title">注意事项 <text class="required-star">*</text></view>
					<view class="notice-textarea-box">
						<textarea class="notice-textarea" v-model="notice" maxlength="100"
							placeholder="请输入告诉牛人的其他内容，例如具体路线、需要材料" auto-height />
						<view class="notice-bottom">
							<text class="clear-btn" @click="notice = ''"><uni-icons type="trash" size="15"></uni-icons>
								清空</text>
							<text class="count">{{ notice.length }}/100</text>
						</view>
					</view>

				</view>
				<button class="send-btn" @click="interview()">发送面试邀请</button>
			</view>
		</u-popup>
	</view>
</template>

<script>
import { chatApi, chat, home } from '@/utils/api.js'
export default {
	data() {
		return {
			statusBarHeight: 0,
			chatHeight: 0,
			keyboardHeight: 0,
			scrollTop: 0,
			inputFocus: false,
			inputText: '',
			showEmojiPanel: false,
			showMoreMenu: false,
			userInfo: {},
			messageList: [],
			socketTask: null, // WebSocket连接任务
			isFavorited: false, // 收藏状态
			// 求简历弹窗相关
			showResumeModal: false,

			// 换电话/微信弹出层相关
			showContactModal: false,
			contactModalVisible: false,
			emojiList: [
				'😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
				'😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
				'😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
				'🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
				'😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
				'😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠',
				'😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨',
				'😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥',
				'😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧',
				'😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐',
				'🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑',
				'🤠', '😈', '👿', '👹', '👺', '🤡', '💩', '👻',
				'💀', '☠️', '👽', '👾', '🤖', '🎃', '😺', '😸',
				'😹', '😻', '😼', '😽', '🙀', '😿', '😾'
			],
			type: '',
			page: 1,
			size: 10000000,
			total: 0,
			loading: false, // 上拉加载状态
			hasMore: true, // 是否还有更多数据
			show: false,
			imageConver: '',
			showPopup: false,
			showCalendar: true, // 控制日历弹窗的显示
			showTimePicker: false, // 控制时间选择器弹窗的显示
			time: '',
			notice: '',
			userinfo: {},
			city: {},
			options: {},
			infoname: ''
		}
	},
	watch: {
		'$store.state.city'(newVal) {
			console.log(newVal, 'city');
			this.city = newVal;
			// // 兼容新的地图选址和原有的地址选择
			// if (newVal && typeof newVal === 'object') {
			// 	this.selectedAddress = newVal.name || newVal.title || newVal.address;
			// } else {
			// 	this.selectedAddress = newVal;
			// }
		},
	},
	async onLoad(options) {
		console.log(options, '%%%')
		try {
			if (options && options.userInfo) {
				this.options = JSON.parse(decodeURIComponent(options.userInfo))
				console.log(this.options, 'options');
			} else {
				console.error('缺少必要的用户信息参数')
				uni.showToast({
					title: '参数错误',
					icon: 'none'
				})
				return
			}
		} catch (error) {
			console.error('解析用户信息失败:', error)
			uni.showToast({
				title: '数据解析失败',
				icon: 'none'
			})
			return
		}

		uni.showLoading({
			title: '加载中'
		});
		this.initPage()
		this.getUserInfo(options)

		this.loadChatHistory()

		// 智能连接测试 - 注释掉，使用全局连接
		// this.smartConnect()

		// 监听全局 WebSocket 消息（通过事件总线）
		console.log('🎯 Chat页面 onLoad: 注册全局WebSocket消息监听器')
		uni.$on('websocket-message', this.handleGlobalWebSocketMessage)
		this.scrollToBottom(true) // 使用强制重置模式确保页面初始化时滚动生效
	},
	onShow() {
		this.userinfo = uni.getStorageSync('usinfo')

		this.scrollToBottom()
		this.send(this.options)
	},
	onHide() {
	},
	onUnload() {
		this.isPageUnloading = true

		// 移除全局 WebSocket 消息监听
		uni.$off('websocket-message', this.handleGlobalWebSocketMessage)

		// 停止心跳和重连
		this.stopHeartbeat()
		this.stopReconnect()

		// 断开WebSocket连接（现在不需要了，因为使用全局连接）
		// if (this.socketTask) {
		// 	this.socketTask.close()
		// 	this.socketTask = null
		// }

		this.isConnected = false
	},
	methods: {
		// 处理全局 WebSocket 消息
		handleGlobalWebSocketMessage(message) {
			console.log('Chat页面收到全局WebSocket消息:', message)
			console.log('当前用户信息:', this.userInfo)
			console.log('当前options:', this.options)

			// 先暂时处理所有消息，用于调试
			if (this.shouldHandleMessage(message)) {
				console.log('消息通过过滤，开始处理')
				this.handleReceivedMessage(message)
			} else {
				console.log('消息被过滤，不处理')
			}
		},

		// 判断是否应该处理这条消息
		shouldHandleMessage(message) {
			console.log('开始判断消息是否应该处理:', message)

			// 暂时返回 true，让所有消息都通过，用于调试
			// TODO: 后续根据实际消息结构优化过滤逻辑
			return true

			// 原来的逻辑（暂时注释）
			/*
			if (this.userInfo && message.data && message.data[0]) {
				const messageData = message.data[0]
				return messageData.user_id === this.userInfo.user_id &&
					   messageData.job_id === this.userInfo.job_id
			}
			return false
			*/
		},

		//更新已读未读状态
		async send(options) {
			try {
				if (options) {
					// this.userInfo = JSON.parse(decodeURIComponent(options.userInfo))
					const res = await chat.update({
						user_id: options.user_id,
						job_id: options.job_id,
					})

				} else {
					// 默认用户信息
					uni.showToast({
						title: '系统出错了，请重试',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('获取用户信息失败:', error)
				uni.showToast({
					title: '获取用户信息失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},
		onClick(item) {
			console.log('点击了:', item)
			uni.navigateTo({
				url: '/pages/user/usercontont/InterviewDetail?id=' + item.invition_id
			})
		},
		toggleMapPicker() {
			uni.navigateTo({
				url: "./mapPicker"
			})
		},
		close() {
			this.showPopup = false
		},
		open() {

		},
		// 判断消息是否应该显示在右边
		isMessageRight(message) {
			// 如果消息有 from_type 字段，根据 from_type 判断
			if (message.from_type !== undefined) {
				return message.from_type === 'recruiter'
			}
			// 如果没有 from_type 字段，使用原来的 isSelf 逻辑
			return message.isSelf || false
		},
		// 智能连接（自动尝试多种方式）
		async smartConnect() {

			// 检查token
			const token = uni.getStorageSync('token')
			if (!token) {
				uni.showToast({
					title: '未找到登录token',
					icon: 'none',
					duration: 2000
				})
			} else {
				console.log('🔑 Token已获取，长度:', token.length)
			}

			// 连接配置列表（按优先级排序）
			// 注意：H5平台WebSocket不支持自定义headers，只能使用URL参数或连接后认证
			const configs = [
				{
					name: 'WebSocket 3000 (URL Token)',
					url: 'ws://8.130.152.121:82/ws',
					authType: 'url'
				},

			]

			// 逐个尝试连接
			for (let i = 0; i < configs.length; i++) {
				const config = configs[i]

				try {
					const success = await this.tryConnect(config)
					if (success) {
						// uni.showToast({
						// 	title: `连接成功`,
						// 	icon: 'success',
						// 	duration: 2000
						// })
						return // 连接成功，退出循环
					}
				} catch (error) {
					console.error(`❌ ${config.name} 连接失败:`, error)
				}

				// 等待一下再尝试下一个
				await this.sleep(1000)
			}

			// 所有连接都失败
			uni.showToast({
				title: '连接失败，请检查服务器',
				icon: 'none',
				duration: 3000
			})
		},

		// 尝试单个连接
		tryConnect(config) {
			return new Promise((resolve, reject) => {
				let socket = null
				let isResolved = false

				// 设置超时
				const timeout = setTimeout(() => {
					if (!isResolved) {
						isResolved = true
						if (socket) {
							socket.close()
						}
						reject(new Error('连接超时'))
					}
				}, 3000)

				// 获取token
				const token = uni.getStorageSync('token')

				// 根据认证类型准备URL和连接参数
				let finalUrl = config.url
				let needAuthAfterConnect = false

				if (token) {
					if (config.authType === 'url') {
						// URL参数认证方式（推荐，H5兼容）
						if (config.url.indexOf('?') === -1) {
							finalUrl = `${config.url}?token=${encodeURIComponent(token)}`
						} else {
							finalUrl = `${config.url}&token=${encodeURIComponent(token)}`
						}
					} else if (config.authType === 'message') {
						// 连接后通过消息认证
						needAuthAfterConnect = true
					}
				} else {
					console.log(`🚫 无token，尝试匿名连接`)
				}


				// 原生WebSocket连接（不设置header，因为H5不支持）
				socket = uni.connectSocket({
					url: finalUrl,
					success: () => {
						console.log(`📞 ${config.name} 连接请求已发送`)
					},
					fail: (error) => {
						if (!isResolved) {
							isResolved = true
							clearTimeout(timeout)
							reject(new Error(`连接请求失败: ${error.errMsg || 'Unknown error'}`))
						}
					}
				})

				socket.onOpen(() => {
					if (!isResolved) {
						isResolved = true
						clearTimeout(timeout)

						// 保存成功的连接
						this.socketTask = socket
						this.setupWebSocketEvents()

						// 如果需要连接后认证，发送认证消息
						if (needAuthAfterConnect && token) {
							socket.send({
								data: JSON.stringify({
									type: 'auth',
									token: token,
									timestamp: Date.now()
								}),
								success: () => {
									console.log(`✅ 认证消息发送成功`)
								},
								fail: (error) => {
									console.error(`❌ 认证消息发送失败:`, error)
								}
							})
						}

						resolve(true)
					}
				})

				socket.onError((error) => {
					if (!isResolved) {
						isResolved = true
						clearTimeout(timeout)
						reject(new Error(`WebSocket错误: ${error.errMsg || 'Connection failed'}`))
					}
				})

				socket.onClose(() => {
					if (!isResolved) {
						isResolved = true
						clearTimeout(timeout)
						reject(new Error('连接被关闭'))
					}
				})
			})
		},

		// 设置WebSocket事件
		setupWebSocketEvents() {
			if (!this.socketTask) return

			this.isConnected = true

			this.socketTask.onMessage((event) => {
				try {
					const data = JSON.parse(event.data)

					// 处理心跳响应
					if (data.type === 'pong' || data.event === 'pong') {
						return
					}
					this.handleReceivedMessage(data)
				} catch (error) {
					console.log('📝 纯文本消息:', event.data)
					this.handleReceivedMessage({
						type: 'text',
						message: event.data
					})
				}
			})

			this.socketTask.onClose(() => {
				this.isConnected = false
				this.socketTask = null
				this.stopHeartbeat()
				// 如果不是主动断开，尝试重连
				if (!this.isPageUnloading) {
					this.startReconnect()
				}
			})

			this.socketTask.onError((error) => {
				this.isConnected = false
			})

			// 启动心跳
			this.startHeartbeat()
		},

		// 启动心跳
		startHeartbeat() {
			this.stopHeartbeat() // 先清除之前的定时器

			this.heartbeatTimer = setInterval(() => {
				if (this.socketTask && this.isConnected) {
					this.socketTask.send({
						data: JSON.stringify({
							type: 'ping',
							timestamp: Date.now()
						}),
						success: () => {
							console.log('💓 心跳发送成功')
						},
						fail: (error) => {
							console.error('💔 心跳发送失败:', error)
						}
					})
				}
			}, 30000) // 30秒心跳
		},

		// 停止心跳
		stopHeartbeat() {
			if (this.heartbeatTimer) {
				clearInterval(this.heartbeatTimer)
				this.heartbeatTimer = null
			}
		},

		// 开始重连
		startReconnect() {
			if (this.reconnectTimer) return // 避免重复重连
			this.reconnectTimer = setTimeout(() => {
				this.reconnectTimer = null
				this.smartConnect()
			}, 3000)
		},

		// 停止重连
		stopReconnect() {
			if (this.reconnectTimer) {
				clearTimeout(this.reconnectTimer)
				this.reconnectTimer = null
			}
		},

		// 发送WebSocket消息（按照后端要求的格式）- 使用全局连接
		sendWebSocketMessage(content, type = 'text', locationData = null, localMessage) {
			// 检查全局 WebSocket 连接状态
			const app = getApp()
			if (!app.globalData.isWebSocketConnected) {
				console.error('❌ 全局WebSocket未连接，无法发送消息')
				uni.showToast({
					title: 'WebSocket未连接',
					icon: 'none'
				})
				return false
			}
			console.log(localMessage, 'localMessage')
			// 根据消息类型设置不同的event值
			let eventType = 'text'; // 默认为文字
			let messageContent = content;

			switch (type) {
				case 'text':
					eventType = 'text';
					messageContent = content;
					break;
				case 'image':
					eventType = 'image';
					messageContent = content; // 图片URL或base64
					break;
				case 'location':
					eventType = 'location';
					messageContent = locationData || content; // 位置信息对象
					break;
				case 'exchanged':
					eventType = 'exchanged';
					messageContent = content; // 位置信息对象
					break;
				case 'invitation':
					eventType = 'invitation';
					messageContent = content; // 位置信息对象
					break;
				default:
					eventType = 'text';
					messageContent = content;
			}

			// 构建基础消息数据
			const messageData = {
				to: this.userInfo.user_id, // 用户ID
				job_id: this.userInfo.job_id,
				content: messageContent, // 发送的信息
				type: type, // 消息类型
				timestamp: Math.floor(Date.now() / 1000), // 当前时间戳（秒）
			};
			console.log(messageData, '###')
			// 只有在 eventType 为 'exchanged' 时才添加 exchanged_status 和 item 字段
			if (eventType === 'exchanged' && localMessage?.exchanged_status === 'accepted' && localMessage?.item == 'wechat') {
				messageData.exchanged_status = "accepted";
				messageData.item = 'wechat',
					messageData.message_id = localMessage.message_id
			} else if (eventType === 'exchanged' && localMessage?.exchanged_status === 'pending' && localMessage?.item == 'wechat') {
				messageData.exchanged_status = "pending";
				messageData.item = 'wechat'
			} else if (eventType === 'exchanged' && localMessage?.exchanged_status === 'rejecte' && localMessage?.item == 'wechat') {
				messageData.exchanged_status = "rejecte";
				messageData.item = 'wechat',
					messageData.message_id = localMessage.message_id
			} else if (eventType === 'exchanged' && localMessage?.exchanged_status === 'accepted' && localMessage?.item == 'phone') {
				messageData.exchanged_status = "accepted";
				messageData.item = 'phone',
					messageData.message_id = localMessage.message_id
			} else if (eventType === 'exchanged' && localMessage?.exchanged_status === 'pending' && localMessage?.item == 'phone') {
				messageData.exchanged_status = "pending";
				messageData.item = 'phone'
			} else if (eventType === 'exchanged' && localMessage?.exchanged_status === 'rejecte' && localMessage?.item == 'phone') {
				messageData.exchanged_status = "rejecte";
				messageData.item = 'phone',
					messageData.message_id = localMessage.message_id
			} else if (eventType === 'exchanged' && localMessage?.exchanged_status === 'pending' && localMessage?.item == 'vitae') {
				messageData.exchanged_status = "pending";
				messageData.item = 'vitae',
					messageData.message_id = localMessage.message_id
			} else if (eventType === 'exchanged' && localMessage?.exchanged_status === 'accepted' && localMessage?.item == 'vitae') {
				messageData.exchanged_status = "accepted";
				messageData.item = 'vitae',
					messageData.message_id = localMessage.message_id,
					messageData.vitae_id = localMessage.vitae_id
			} else if (eventType === 'exchanged' && localMessage?.exchanged_status === 'rejecte' && localMessage?.item == 'vitae') {
				messageData.exchanged_status = "rejecte";
				messageData.item = 'vitae',
					messageData.message_id = localMessage.message_id
			} else if (eventType === 'exchanged' && localMessage?.exchanged_status === 'pending' && localMessage?.item == 'vitae') {
				messageData.exchanged_status = "pending";
				messageData.item = 'vitae',
					messageData.message_id = localMessage.message_id,
					messageData.vitae_id = localMessage.vitae_id
			} else if (eventType === 'invitation') {
				messageData.invitation_status = "pending";
				messageData.address = this.city.name,
					messageData.lon = this.city.longitude,
					messageData.lat = this.city.latitude,
					messageData.remark = this.notice,
					messageData.interview_time = this.time,
					messageData.telephone = this.userinfo.telephone,
					messageData.name = this.userinfo.name
			}

			const message = {
				event: eventType, // 根据类型动态设置：text/image/location
				data: messageData
			}
			console.log(message, '###')
			// 使用全局 SocketTask 连接发送消息
			if (app.globalData.socketTask) {
				app.globalData.socketTask.send({
					data: JSON.stringify(message),
					success: () => {
						console.log('✅ 全局WebSocket消息发送成功')
					},
					fail: (error) => {
						console.error('❌ 全局WebSocket消息发送失败:', error)
						uni.showToast({
							title: '消息发送失败',
							icon: 'none'
						})
					}
				})
			} else {
				console.error('❌ SocketTask 不存在')
				uni.showToast({
					title: 'WebSocket连接异常',
					icon: 'none'
				})
			}

			return true
		},

		// 延迟函数
		sleep(ms) {
			return new Promise(resolve => setTimeout(resolve, ms))
		},

		// 初始化页面
		initPage() {
			const systemInfo = uni.getSystemInfoSync()
			this.statusBarHeight = systemInfo.statusBarHeight || 0
			this.calculateChatHeight()

			// 监听键盘高度变化
			uni.onKeyboardHeightChange((res) => {
				this.keyboardHeight = res.height
				this.calculateChatHeight()
				if (res.height > 0) {
					this.scrollToBottom()
				}
			})
		},

		// 获取用户信息
		async getUserInfo(options) {
			try {
				if (options && options.userInfo) {
					this.userInfo = JSON.parse(decodeURIComponent(options.userInfo))
					console.log(this.userInfo, '%%%')

					// 确保必要的字段存在
					if (!this.userInfo.user_id || !this.userInfo.job_id) {
						console.error('用户信息缺少必要字段:', this.userInfo)
						uni.showToast({
							title: '用户信息不完整',
							icon: 'none'
						})
						return
					}

					const params = {
						user_id: this.userInfo.user_id,
						job_id: this.userInfo.job_id,
						page: this.page,
						limit: this.size
					}
					let res = await chat.chatInfo(params)
					if (res.code == 200) {
						// 确保消息数据的完整性
						const messages = res.data.data || []
						this.infoname = res.data.job_info.name
						this.isFavorited = res.data.collect == 1 ? true : false
						this.messageList = messages.filter(msg => msg && typeof msg === 'object').reverse()
						this.total = res.data.total || 0
						// 判断是否还有更多数据
						this.hasMore = this.messageList.length >= this.size && this.messageList.length < this.total

						// 数据加载完成后，确保滚动到底部显示最新消息
						this.$nextTick(() => {
							setTimeout(() => {
								this.scrollToBottom(true) // 使用强制重置模式确保初始化时滚动生效
							}, 200) // 增加延迟时间让DOM完全渲染
						})

						uni.hideLoading();
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
						uni.hideLoading();
					}

				} else {
					// 默认用户信息
					uni.showToast({
						title: '系统出错了，请重试',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('获取用户信息失败:', error)
				uni.showToast({
					title: '获取用户信息失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},

		// 上拉加载更多聊天记录
		async onLoadMore() {
			if (this.loading || !this.hasMore) {
				return
			}

			this.loading = true
			this.page++

			try {
				const params = {
					user_id: this.userInfo.id,
					page: this.page,
					limit: this.size
				}
				let res = await chat.chatInfo(params)

				if (res.code === 200) {
					const newMessages = (res.data.data || []).reverse()
					this.total = res.data.total || 0

					// 将新消息添加到列表开头（因为聊天记录是倒序的）
					this.messageList = [...newMessages, ...this.messageList]

					// 判断是否还有更多数据
					this.hasMore = newMessages.length >= this.size && this.messageList.length < this.total

				} else {
					throw new Error(res.msg || '获取数据失败')
				}
				uni.hideLoading()
			} catch (error) {
				console.error('上拉加载失败:', error)
				this.page-- // 加载失败时回退页码
				uni.showToast({
					title: '加载失败',
					icon: 'none',
					duration: 1500
				})
			} finally {
				this.loading = false
			}
		},

		// 计算聊天区域高度
		calculateChatHeight() {
			const systemInfo = uni.getSystemInfoSync()
			const windowHeight = systemInfo.windowHeight
			const headerHeight = 88 + this.statusBarHeight + 80 // 增加功能按钮区域高度
			let inputAreaHeight = 80 // 减少基础输入区域高度

			// 如果显示emoji面板，增加高度
			if (this.showEmojiPanel) {
				inputAreaHeight += 400
			}

			// 如果显示更多菜单，增加高度
			if (this.showMoreMenu) {
				inputAreaHeight += 300
			}

			// 当键盘弹起时，减少额外的间距
			let keyboardOffset = this.keyboardHeight
			if (this.keyboardHeight > 0) {
				keyboardOffset = this.keyboardHeight - 20 // 减少20rpx的间距
			}

			this.chatHeight = windowHeight - headerHeight - inputAreaHeight - keyboardOffset
		},

		// 返回上一页
		goBack() {
			uni.navigateBack()
		},

		// 切换收藏状态
		async toggleFavorite() {
			this.isFavorited = !this.isFavorited
			if (this.isFavorited) {
				const params = {
					seeker_id: this.options.user_id,
					job_id: this.options.job_id,
					type: 1
				}
				await home.colletTalent(params)
			} else {
				const params = {
					seeker_id: this.options.user_id,
					job_id: this.options.job_id,
					type: 2
				}
				await home.colletTalent(params)
			}
			uni.showToast({
				title: this.isFavorited ? '已收藏' : '已取消收藏',
				icon: 'success',
				duration: 1500
			})
		},


		// 显示更多选项
		showMoreOptions() {
			uni.showActionSheet({
				itemList: ['举报', '拉黑'],
				success: (res) => {
					if (res.tapIndex === 0) {
						// 举报
						uni.showToast({
							title: '举报功能开发中',
							icon: 'none'
						})
					} else if (res.tapIndex === 1) {
						// 拉黑
						uni.showModal({
							title: '确认拉黑',
							content: '确定要拉黑该用户吗？',
							success: (modalRes) => {
								if (modalRes.confirm) {
									uni.showToast({
										title: '已拉黑',
										icon: 'success'
									})
								}
							}
						})
					}
				}
			})
		},

		// 求简历
		requestResume() {
			this.showResumeModal = true
		},

		// 关闭求简历弹窗
		closeResumeModal() {
			this.showResumeModal = false
		},

		// 发送求简历请求
		sendResumeRequest() {
			// 发送求简历消息
			const message = '简历请求已发送'
			this.informationMsgTag(message)

			this.closeResumeModal()

			uni.showToast({
				title: '求简历请求已发送',
				icon: 'success'
			})
		},
		//简历预览
		previewResume(item) {
			
			uni.navigateTo({
				url: `./vaResume?id=${item.id}`,
			})
		},
		// 发送消息的通用方法
		sendDirectMessage(messageContent) {
			const messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

			// 添加到本地消息列表
			const localMessage = {
				type: 'text',
				content: messageContent,
				isSelf: true,
				time: this.getCurrentTime(),
				showTime: this.shouldShowTime(),
				messageId: messageId,
				status: 'sending' // 设置为发送中
			}

			this.messageList.push(localMessage)
			this.scrollToBottom()

			// 通过WebSocket发送消息
			const success = this.sendWebSocketMessage(messageContent)

			if (success) {
				// WebSocket发送成功
				setTimeout(() => {
					localMessage.status = 'sent'
				}, 500)
			} else {
				// WebSocket发送失败，标记为失败状态
				setTimeout(() => {
					localMessage.status = 'failed'
				}, 500)
			}
		},

		// 换电话/微信
		exchangeContact() {
			this.showContactModal = true
			// 延迟显示动画，确保DOM已渲染
			this.$nextTick(() => {
				setTimeout(() => {
					this.contactModalVisible = true
				}, 50)
			})
		},

		// 关闭换电话/微信弹出层
		closeContactModal() {
			this.contactModalVisible = false
			// 等待动画完成后隐藏弹出层
			setTimeout(() => {
				this.showContactModal = false
			}, 300)
		},

		// 处理微信交换请求
		async handleWechatExchange(message, action) {
			try {
				// 这里可以调用API接口处理同意/拒绝逻辑
				// 例如：await chat.handleWechatExchange({ messageId: message.messageId, action: action })
				console.log('处理微信交换请求：', message, action)
				// 更新消息状态
				// const messageIndex = this.messageList.findIndex(msg => msg.messageId === message.messageId)
				// if (messageIndex !== -1) {
				// 	this.messageList[messageIndex].exchanged_status = action
				// }
				if (message.item == 'wechat') {
					if (action === 'accepted') {
						const actionText = '已同意交换微信'
						this.infoRequestMsgTag(actionText, message.id)
					} else if (action === 'rejected') {
						const actionText = '已拒绝交换微信'
						this.infoRejecteMsgTag(actionText, message.id)
					}
				} else if (message.item == 'phone') {
					if (action === 'accepted') {
						const actionText = '已同意交换电话'
						this.infoRequestMsgTag(actionText, message.id)
					} else if (action === 'rejected') {
						const actionText = '已拒绝交换电话'
						this.infoRejecteMsgTag(actionText, message.id)
					}
				} else if (message.item == 'vitae') {
					if (action === 'accepted') {
						const actionText = '已同意接收简历'
						this.infoRequestMsgTag(actionText, message.id, message.vitae_id)
					} else if (action === 'rejected') {
						const actionText = '已拒绝接收简历'
						this.infoRejecteMsgTag(actionText, message.id)
					}
				}

				// uni.showToast({
				// 	title: `${actionText}微信交换`,
				// 	icon: 'success'
				// })
			} catch (error) {
				console.error('处理微信交换失败:', error)
				uni.showToast({
					title: '操作失败',
					icon: 'none'
				})
			}
		},

		// 复制手机号
		copyPhoneNumber(number) {
			const phoneNumber = number
			if (phoneNumber) {
				// 使用uni-app的复制到剪贴板功能
				uni.setClipboardData({
					data: phoneNumber,
					success: () => {
						uni.showToast({
							title: '已复制',
							icon: 'success'
						})
					},
					fail: () => {
						uni.showToast({
							title: '复制失败',
							icon: 'none'
						})
					}
				})
			} else {
				uni.showToast({
					title: '暂无手机号',
					icon: 'none'
				})
			}
		},


		// 请求电话号码
		async requestPhone() {
			this.closeContactModal()
			let res = await chat.checkExchange({
				type: 'phone',
				user_id: this.options.user_id,
				job_id: this.options.job_id
			})

			if (res.code == 200) {
				const message = '请求交换电话已发送'
				this.informationMsgTag(message)
				uni.showToast({
					title: '电话请求已发送',
					icon: 'success'
				})
			} else {

				uni.showToast({
					title: res.msg,
					icon: 'error'
				})
			}
		},
		//邀约面试
		interview() {
			console.log(this.city, this.userinfo, this.time, this.notice)
			if (this.city == '' || !this.userinfo || !this.time || !this.notice) {
				this.showPopup = false
				uni.showToast({
					title: '请填写完整面试信息',
					icon: 'none'
				})
				return
			}
			const logo = uni.getStorageSync('usinfo')
			console.log(logo)
			if (logo.logo == null) {
				this.showPopup = false
				uni.showToast({
					title: '请先在公司主页上传公司logo',
					icon: 'none'
				})
				return
			}


			const message = "邀约面试已发送"
			this.resumetionMsgTag(message)

			this.showPopup = false
		},

		// 请求微信号
		async requestWechat() {
			this.closeContactModal()
			let res = await chat.checkExchange({
				type: 'wechat',
				user_id: this.options.user_id,
				job_id: this.options.job_id
			})
			if (res.code == 200) {
				const message = '请求交换微信已发送'
				this.informationMsgTag(message)
				uni.showToast({
					title: '微信请求已发送',
					icon: 'success'
				})
			} else {
				uni.showToast({
					title: res.msg,
					icon: 'error'
				})
			}

		},

		// 互换联系方式
		exchangeBoth() {
			const message = '您好，我们可以互换一下联系方式吗？包括电话和微信，方便后续沟通。谢谢！'
			this.informationMsgTag(message)
			this.closeContactModal()

			uni.showToast({
				title: '联系方式交换请求已发送',
				icon: 'success'
			})
		},

		// 标记不合适
		markUnsuitable() {
			uni.showModal({
				title: '确认操作',
				content: '确定标记该候选人为不合适吗？',
				success: (res) => {
					if (res.confirm) {
						uni.showToast({
							title: '已标记为不合适',
							icon: 'success'
						})
					}
				}
			})
		},

		// 输入框聚焦
		onInputFocus() {
			this.inputFocus = true
			this.hideAllPanels()

			// 延迟滚动，等待键盘弹起和高度计算完成
			setTimeout(() => {
				this.scrollToBottom()
			}, 500) // 增加延迟时间，确保键盘完全弹起
		},

		// 输入框失焦
		onInputBlur() {
			this.inputFocus = false
		},



		// 处理接收到的WebSocket消息
		handleReceivedMessage(data) {
			console.log('🔥 handleReceivedMessage 被调用，数据:', data)
			this.type = data.type
			// 添加到消息列表
			console.log('接收到的原始数据:', data)

			const messageData = data.data && data.data[0] ? data.data[0] : data

			const message = {
				img: messageData.img,
				type: data.type || 'text',
				content: messageData.content || data.content,
				isSelf: false,
				time: this.getCurrentTime(),
				showTime: this.shouldShowTime(),
				messageId: `received_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
				// 添加缺失的字段
				exchanged_status: messageData.exchanged_status,
				item: messageData.item,
				from_type: messageData.from_type,
				boss_contact: messageData.boss_contact || '',
				talent_contact: messageData.talent_contact || '',
				id: messageData.id,
				vitae_id: messageData.vitae_id,
			}

			// 确保消息有必要的字段
			if (!message.type) {
				message.type = 'text'
				console.log('⚠️ 消息缺少type字段，设置为默认值text')
			}
			if (!message.content) {
				message.content = '收到一条消息'
				console.log('⚠️ 消息缺少content字段，设置为默认值')
			}
			if (messageData.event == 'invitation') {
				message.address = messageData.address
				message.event = messageData.event
				message.interview_time = messageData.interview_time,
					message.invitation_status = messageData.invitation_status,
					message.lat = messageData.lat,
					message.lon = messageData.lon,
					message.name = messageData.user.name,
					message.telephone = messageData.user.telephone
				message.invition_id = messageData.invition_id
			}
			this.messageList.push(message)

			// 强制更新 Vue 组件
			this.$forceUpdate()
			// 滚动到底部
			this.$nextTick(() => {
				this.scrollToBottom()
			})
			this.send(this.options)
		},

		// 接收聊天消息
		onReceiveChatMessage(message) {

			// 添加到消息列表
			const chatMessage = {
				type: message.messageType || 'text',
				content: message.content,
				isSelf: false,
				time: this.formatTime(message.timestamp),
				showTime: this.shouldShowTime(message.timestamp),
				messageId: message.messageId,
				senderId: message.senderId
			}

			this.messageList.push(chatMessage)
			this.scrollToBottom()

			// 标记消息已读
			this.markMessageAsRead(message.messageId)
		},

		// 加载聊天历史记录
		async loadChatHistory() {
			try {
				if (!this.userInfo.id) return

				// 生成会话ID（可以是用户ID的组合）
				this.conversationId = `chat_${this.userInfo.id}_${Date.now()}`

			} catch (error) {
				console.error('加载聊天历史失败:', error)
				// 如果加载失败，显示一些默认消息
				// this.messageList = [
				// 	{
				// 		type: 'text',
				// 		content: '您好，我对这个岗位特别感兴趣！',
				// 		isSelf: false,
				// 		time: this.getCurrentTime(),
				// 		showTime: true
				// 	}
				// ]
			}
		},

		// 发送文字消息
		sendTextMessage() {
			if (!this.inputText.trim()) return

			const messageContent = this.inputText.trim()
			const messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

			// 添加到本地消息列表
			const localMessage = {
				type: 'text',
				content: messageContent,
				isSelf: true,
				time: this.getCurrentTime(),
				showTime: this.shouldShowTime(),
				messageId: messageId,
				status: 'sending' // 设置为发送中
			}
			console.log(this.messageList)
			this.messageList.push(localMessage)
			this.inputText = ''
			this.scrollToBottom()

			// 通过WebSocket发送消息
			const success = this.sendWebSocketMessage(messageContent)

			if (success) {
				// WebSocket发送成功
				setTimeout(() => {
					localMessage.status = 'sent'
				}, 500)
			} else {
				// WebSocket发送失败，标记为失败状态
				setTimeout(() => {
					localMessage.status = 'failed'
				}, 500)
			}
		},

		// 切换emoji面板
		toggleEmoji() {
			this.showEmojiPanel = !this.showEmojiPanel
			this.showMoreMenu = false
			this.inputFocus = false
			this.$nextTick(() => {
				this.calculateChatHeight()
				this.scrollToBottom()
			})
		},

		// 切换更多菜单
		toggleMoreMenu() {
			this.showMoreMenu = !this.showMoreMenu
			this.showEmojiPanel = false
			this.inputFocus = false
			this.$nextTick(() => {
				this.calculateChatHeight()
				this.scrollToBottom()
			})
		},

		// 隐藏所有面板
		hideAllPanels() {
			this.showEmojiPanel = false
			this.showMoreMenu = false
			this.$nextTick(() => {
				this.calculateChatHeight()
			})
		},

		// 插入emoji
		insertEmoji(emoji) {
			this.inputText += emoji
		},

		// 从相册选择
		chooseFromAlbum() {
			uni.chooseImage({
				count: 1,
				sourceType: ['album'],
				success: (res) => {
					console.log(res, '^^^')
					this.sendImageMessage(res.tempFilePaths[0])
				}
			})
			this.hideAllPanels()
		},

		// 拍照
		takePhoto() {
			uni.chooseImage({
				count: 1,
				sourceType: ['camera'],
				success: (res) => {
					this.sendImageMessage(res.tempFilePaths[0])
				}
			})
			this.hideAllPanels()
		},

		// 跳转到自定义地图选择页面
		navigateToMapPicker() {
			uni.navigateTo({
				url: '/pages/chat/mapPicker',
				success: () => {
					console.log('跳转到地图选择页面成功')
				},
				fail: (err) => {
					console.error('跳转到地图选择页面失败:', err)
					uni.showToast({
						title: '地图功能暂不可用',
						icon: 'none'
					})
				}
			})
		},

		// 发送图片消息
		sendImageMessage(imagePath) {
			const messageId = `img_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
			uni.showLoading()
			// #ifdef APP-PLUS
			uni.uploadFile({
				url: 'http://8.130.152.121:82/api/common/upload', // 替换为你自己的上传地址
				filePath: imagePath,
				name: 'file', // 根据后端字段设定
				formData: {
					type: 2
				},
				success: (uploadFileRes) => {
					console.log(uploadFileRes)
					const url = JSON.parse(uploadFileRes.data)
					console.log(url, '&&&')
					if (url.code == 200) {
						uni.hideLoading()
						uni.showToast({ title: '上传成功', icon: 'success' })
						this.imageConver = url.data.url

						const localMessage = {
							type: 'image',
							content: this.imageConver,
							isSelf: true,
							time: this.getCurrentTime(),
							showTime: this.shouldShowTime(),
							messageId: messageId,
							status: 'sending' // 设置为发送中
						}

						this.messageList.push(localMessage)
						this.scrollToBottom()

						// 通过WebSocket发送图片消息
						const success = this.sendWebSocketMessage(this.imageConver, 'image')

						if (success) {
							// WebSocket发送成功
							setTimeout(() => {
								localMessage.status = 'sent'
							}, 500)
						} else {
							// WebSocket发送失败，标记为失败状态
							setTimeout(() => {
								localMessage.status = 'failed'
							}, 500)
						}
					} else {
						uni.showToast({ title: url.msg, icon: 'none' })
						uni.hideLoading()
					}

				},
				fail: (err) => {
					uni.showToast({ title: '上传失败', icon: 'none' })
					console.error('上传失败:', err)
					// uni.hideLoading()
				}
			})
			// #endif

			// #ifdef H5
			uni.uploadFile({
				url: '/api/common/upload', // 替换为你自己的上传地址
				filePath: imagePath,
				name: 'file', // 根据后端字段设定
				formData: {
					type: 2
				},
				success: (uploadFileRes) => {
					console.log(uploadFileRes)
					const url = JSON.parse(uploadFileRes.data)
					console.log(url, '&&&')
					if (url.code == 200) {
						uni.hideLoading()
						uni.showToast({ title: '上传成功', icon: 'success' })
						this.imageConver = url.data.url

						const localMessage = {
							type: 'image',
							content: this.imageConver,
							isSelf: true,
							time: this.getCurrentTime(),
							showTime: this.shouldShowTime(),
							messageId: messageId,
							status: 'sending' // 设置为发送中
						}

						this.messageList.push(localMessage)
						this.scrollToBottom()

						// 通过WebSocket发送图片消息
						const success = this.sendWebSocketMessage(this.imageConver, 'image')

						if (success) {
							// WebSocket发送成功
							setTimeout(() => {
								localMessage.status = 'sent'
							}, 500)
						} else {
							// WebSocket发送失败，标记为失败状态
							setTimeout(() => {
								localMessage.status = 'failed'
							}, 500)
						}
					} else {
						uni.showToast({ title: url.msg, icon: 'none' })
						uni.hideLoading()
					}

				},
				fail: (err) => {
					uni.showToast({ title: '上传失败', icon: 'none' })
					console.error('上传失败:', err)
					// uni.hideLoading()
				}
			})
			// #endif
			console.log(this.imageConver)
			// 添加到本地消息列表

		},

		// 请求信息接口
		informationMsgTag(exchanged) {
			console.log(exchanged, '###')
			const messageId = `loc_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

			// 添加到本地消息列表
			const localMessage = {
				type: 'exchanged',
				content: exchanged, // 用于显示的内容
				isSelf: true,
				time: this.getCurrentTime(),
				showTime: this.shouldShowTime(),
				messageId: messageId,
				status: 'sending', // 设置为发送中
				exchanged_status: "pending",
				item: exchanged === '请求交换微信已发送' ? 'wechat' : exchanged === '请求交换电话已发送' ? 'phone' : exchanged === '简历请求已发送' ? 'vitae' : ''
			}
			console.log(localMessage, '&&&&&&')
			this.messageList.push(localMessage)
			this.scrollToBottom()

			// 通过WebSocket发送位置消息
			const success = this.sendWebSocketMessage(exchanged, 'exchanged', '', localMessage)

			if (success) {
				// WebSocket发送成功
				setTimeout(() => {
					localMessage.status = 'sent'
				}, 500)
			} else {
				// WebSocket发送失败，标记为失败状态
				setTimeout(() => {
					localMessage.status = 'failed'
				}, 500)
			}
		},
		//同意请求微信或电话
		infoRequestMsgTag(exchanged, id, vitae_id) {
			console.log(exchanged, id, '###')
			const messageId = `loc_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

			// 添加到本地消息列表
			const localMessage = {
				type: 'exchanged',
				content: exchanged, // 用于显示的内容
				isSelf: true,
				time: this.getCurrentTime(),
				showTime: this.shouldShowTime(),
				messageId: messageId,
				status: 'sending', // 设置为发送中
				exchanged_status: "accepted",
				message_id: id,
				item: exchanged === '已同意交换微信' ? 'wechat' : exchanged === '已同意交换电话' ? 'phone' : exchanged === '已同意接收简历' ? 'vitae' : ''
			}
			if (vitae_id) {
				localMessage.vitae_id = vitae_id
			}
			console.log(localMessage, '&&&&&&')
			this.messageList.push(localMessage)
			console.log(this.messageList)
			this.scrollToBottom()
			this.messageList = this.messageList.filter(message => message.id !== id)
			// 通过WebSocket发送位置消息
			const success = this.sendWebSocketMessage(exchanged, 'exchanged', '', localMessage)

			if (success) {
				// WebSocket发送成功
				setTimeout(() => {
					localMessage.status = 'sent'
				}, 500)
			} else {
				// WebSocket发送失败，标记为失败状态
				setTimeout(() => {
					localMessage.status = 'failed'
				}, 500)
			}
		},
		//拒绝请求微信或电话
		infoRejecteMsgTag(exchanged, id) {
			console.log(exchanged, '###')
			const messageId = `loc_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

			// 添加到本地消息列表
			const localMessage = {
				type: 'exchanged',
				content: exchanged, // 用于显示的内容
				isSelf: true,
				time: this.getCurrentTime(),
				showTime: this.shouldShowTime(),
				messageId: messageId,
				status: 'sending', // 设置为发送中
				exchanged_status: "rejecte",
				message_id: id,
				item: exchanged === '已拒绝交换微信' ? 'wechat' : exchanged === '已拒绝交换电话' ? 'phone' : exchanged === '已拒绝附件简历' ? 'vitae' : ''
			}
			console.log(localMessage, '&&&&&&')
			this.messageList.push(localMessage)
			this.scrollToBottom()
			this.messageList = this.messageList.filter(message => message.id !== id)
			// 通过WebSocket发送位置消息
			const success = this.sendWebSocketMessage(exchanged, 'exchanged', '', localMessage)

			if (success) {
				// WebSocket发送成功
				setTimeout(() => {
					localMessage.status = 'sent'
				}, 500)
			} else {
				// WebSocket发送失败，标记为失败状态
				setTimeout(() => {
					localMessage.status = 'failed'
				}, 500)
			}
		},
		//请求简历接口
		resumetionMsgTag(exchanged) {
			console.log(exchanged, '###')
			const messageId = `loc_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

			// 添加到本地消息列表
			const localMessage = {
				type: 'invitation',
				content: exchanged, // 用于显示的内容
				isSelf: true,
				time: this.getCurrentTime(),
				showTime: this.shouldShowTime(),
				messageId: messageId,
				status: 'sending', // 设置为发送中
				invitation_status: "pending",
			}
			console.log(localMessage, '&&&&&&')
			this.messageList.push(localMessage)
			console.log(this.messageList, '&&&&&&')
			this.scrollToBottom()

			// 通过WebSocket发送位置消息
			const success = this.sendWebSocketMessage(exchanged, localMessage.type)

			if (success) {
				// WebSocket发送成功
				setTimeout(() => {
					localMessage.status = 'sent'
				}, 500)
			} else {
				// WebSocket发送失败，标记为失败状态
				setTimeout(() => {
					localMessage.status = 'failed'
				}, 500)
			}
		},

		// 个人信息
		previewImage(item) {
			console.log(this.options, '###')
			uni.navigateTo({
				url: `/pages/chat/userSetting?userInfo=${encodeURIComponent(JSON.stringify(this.options))}`,
			})
		},

		// 打开位置
		openLocation(location) {
			console.log(location, 'location')
			uni.openLocation({
				latitude: location.content.latitude,
				longitude: location.content.longitude,
				name: location.content.name,
				address: location.content.address
			})
		},

		// 滚动到底部
		scrollToBottom(forceReset = false) {
			this.$nextTick(() => {
				if (forceReset) {
					// 强制重置模式：用于页面初始化时确保滚动生效
					this.scrollTop = 0
					this.$nextTick(() => {
						this.scrollTop = 999999
					})
				} else {
					// 普通模式：直接滚动，避免跳动
					this.scrollTop = 999999
				}
			})
		},

		// 获取当前时间
		getCurrentTime() {
			const now = new Date()
			const hours = now.getHours().toString().padStart(2, '0')
			const minutes = now.getMinutes().toString().padStart(2, '0')
			return `${hours}:${minutes}`
		},

		// 格式化时间戳
		formatTime(timestamp) {
			const date = new Date(timestamp)
			const hours = date.getHours().toString().padStart(2, '0')
			const minutes = date.getMinutes().toString().padStart(2, '0')
			return `${hours}:${minutes}`
		},

		// 判断是否显示时间
		shouldShowTime(timestamp) {
			if (this.messageList.length === 0) return true

			const lastMessage = this.messageList[this.messageList.length - 1]
			if (!lastMessage.time) return true

			// 如果距离上一条消息超过5分钟，显示时间
			const timeDiff = timestamp - (lastMessage.timestamp || Date.now())
			return timeDiff > 5 * 60 * 1000
		},

		// 标记消息已读
		async markMessageAsRead(messageId) {
			try {
				await chatApi.markMessageRead({
					messageId: messageId,
					conversationId: this.conversationId
				})
			} catch (error) {
				console.error('标记消息已读失败:', error)
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.chat-container {
	height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
	position: relative;
}

/* 顶部导航栏 */
.chat-header {
	background-color: #ffffff;
	border-bottom: 1rpx solid #f0f0f0;

	.header-top {
		height: 88rpx;
		display: flex;
		align-items: center;
		padding: 0 32rpx;

		.back-btn {
			margin-right: 24rpx;

			.back-icon {
				font-size: 48rpx;
				color: #333333;
				font-weight: bold;
			}
		}

		.header-center {
			flex: 1;
			text-align: center;

			.user-name {
				font-size: 32rpx;
				font-weight: bold;
				color: #333333;
				display: block;
				margin-bottom: 4rpx;
			}

			.user-status {
				font-size: 24rpx;
				color: #999999;
				display: block;
			}
		}

		.header-right {
			display: flex;
			align-items: center;

			.star-btn {
				margin-right: 24rpx;

				.star-icon {
					width: 40rpx;
					height: 40rpx;
				}
			}

			.more-btn {
				.more-icon {
					font-size: 48rpx;
					color: #333333;
				}
			}
		}
	}

	.function-buttons {
		display: flex;
		padding: 10rpx 32rpx;

		.function-btn {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 16rpx 8rpx;

			.btn-icon {
				width: 60rpx;
				height: 60rpx;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 32rpx;
				margin-bottom: 8rpx;
				background-color: #ffffff;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

				&.resume-icon {
					background-color: #e8f4fd;
					color: #1890ff;
				}

				&.contact-icon {
					background-color: #f0f9ff;
					color: #52c41a;
				}

				&.interview-icon {
					background-color: #fff7e6;
					color: #fa8c16;
				}

				&.unsuitable-icon {
					background-color: #fff1f0;
					color: #ff4d4f;
				}
			}

			.btn-text {
				font-size: 22rpx;
				color: #666666;
				text-align: center;
				line-height: 1.2;
				margin-top: 20rpx;
			}

			&:active {
				opacity: 0.7;
			}
		}
	}
}

/* 聊天消息区域 */
.chat-messages {
	flex: 1;
	background-color: #f5f5f5;
}

.message-list {
	// height: 199%;
	padding: 20rpx 32rpx;
}

.message-item {
	margin-bottom: 32rpx;
}

/* 时间分割线 */
.time-divider {
	text-align: center;
	margin-bottom: 24rpx;

	.time-text {
		font-size: 24rpx;
		color: #999999;
		background-color: rgba(255, 255, 255, 0.8);
		padding: 8rpx 16rpx;
		border-radius: 12rpx;
	}
}

/* msgTag 消息样式 - 居中灰色文字，无气泡 */
.msg-tag-wrapper {
	text-align: center;
	margin: 16rpx 0;

	.msg-tag-text {
		font-size: 26rpx;
		color: #999999;
		line-height: 1.4;
	}

	/* 微信交换请求卡片样式 */
	.wechat-exchange-card {
		background: #ffffff;
		border: 2rpx solid #e0e0e0;
		border-radius: 16rpx;
		padding: 32rpx;
		margin: 16rpx 40rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

		.exchange-header {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 16rpx;


		}

		.wechat-icon {
			font-size: 40rpx;
			margin-right: 16rpx;
		}

		.exchange-title {
			font-size: 30rpx;
			font-weight: 500;
			color: #333333;
		}

		.exchange-subtitle {
			text-align: center;
			margin-bottom: 32rpx;


		}

		.subtitle-text {
			font-size: 26rpx;
			color: #666666;
		}

		.exchange-actions {
			display: flex;
			gap: 24rpx;
			justify-content: center;



			.action-btn {
				flex: 1;
				max-width: 160rpx;
				height: 55rpx;
				border-radius: 36rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				transition: all 0.3s ease;
				margin-top: 20rpx;

				.btn-text {
					font-size: 28rpx;
					font-weight: 500;
				}

				&.reject-btn {
					background: #f5f5f5;
					border: 2rpx solid #d9d9d9;

					.btn-text {
						color: #666666;
					}

					&:active {
						background: #e8e8e8;
					}
				}

				&.accept-btn {
					background: #07c160;
					border: 2rpx solid #07c160;

					.btn-text {
						color: #ffffff;
					}

					&:active {
						background: #06ad56;
					}


				}
			}

			/* 手机号信息区域 */
			.phone-info {
				background: #f8f8f8;
				border-radius: 12rpx;
				padding: 24rpx;
				margin-bottom: 24rpx;
				text-align: center;

				.phone-label {
					font-size: 26rpx;
					color: #666666;
					margin-right: 8rpx;
				}

				.phone-number {
					font-size: 32rpx;
					font-weight: 600;
					color: #333333;
					letter-spacing: 2rpx;
				}
			}

			/* 拒绝状态信息区域 */
			.rejected-info {
				text-align: center;
				padding: 24rpx 0;

				.rejected-text {
					font-size: 28rpx;
					color: #999999;
				}
			}
		}

		.copy-btn {
			background: #07c160;
			border: 2rpx solid #07c160;
			height: 30rpx;
			width: 100%;
			max-width: none;

			.btn-text {
				color: #ffffff;
			}

			&:active {
				background: #06ad56;
			}
		}
	}

	/* 简历文件卡片样式 */
	.resume-file-card {
		background: #EDF9F9;
		border: 2rpx solid #DDFFFA;
		border-radius: 20rpx;
		padding: 40rpx;
		margin: 16rpx 40rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

		.file-info {
			display: flex;
			align-items: center;
			margin-bottom: 32rpx;

			.file-icon {
				width: 80rpx;
				height: 80rpx;
				background: #20b2aa;
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 24rpx;
				position: relative;

				.icon-image {
					width: 48rpx;
					height: 48rpx;
				}

				/* 如果没有图片，显示默认图标 */
				&::before {
					content: "📄";
					font-size: 40rpx;
					color: #ffffff;
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
				}
			}

			.file-name {
				font-size: 32rpx;
				font-weight: 500;
				color: #333333;
				flex: 1;
			}
		}

		.preview-action {
			.preview-btn {
				background: #E2F6F7;
				border: 2rpx solid #E7F2F3;
				border-radius: 16rpx;
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				transition: all 0.3s ease;

				.preview-text {
					font-size: 30rpx;
					color: #556366;
					font-weight: 500;
				}

				&:active {
					background: #d6ebfa;
					transform: scale(0.98);
				}
			}
		}
	}
}

/* 消息包装器 */
.message-wrapper {
	display: flex;
	align-items: flex-end;

	&.message-right {
		flex-direction: row-reverse;
	}

	.message-avatar {
		width: 72rpx;
		height: 72rpx;
		border-radius: 50%;
		margin: 0 16rpx;
	}
}

/* 消息气泡 */
.message-bubble {
	max-width: 480rpx;
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 20rpx;
	position: relative;

	&.bubble-self {
		background-color: #1CBBB4;

		.message-text {
			color: #ffffff;
		}
	}

	.message-text {
		font-size: 28rpx !important;
		color: #333333;
		line-height: 1.5;
		word-wrap: break-word;
	}

	.message-image {
		width: 200rpx;
		height: 200rpx;
		border-radius: 12rpx;
	}
}

::v-deep .u-tooltip__wrapper__text {
	font-size: 28rpx !important;
	color: #333333 !important;
	line-height: 1.5;
	word-wrap: break-word;
}

/* 位置消息 */
.message-location {
	display: flex;
	align-items: center;

	.location-icon {
		font-size: 32rpx;
		margin-right: 16rpx;
	}

	.location-info {
		.location-name {
			font-size: 28rpx;
			color: #333333;
			font-weight: bold;
			display: block;
			margin-bottom: 8rpx;
		}

		.location-address {
			font-size: 24rpx;
			color: #666666;
			display: block;
		}
	}
}

/* 底部输入区域 */
.chat-input-area {
	background-color: #ffffff;
	border-top: 1rpx solid #f0f0f0;

	.input-container {
		padding: 12rpx 32rpx;
		display: flex;
		align-items: center;

		.input-left {
			flex: 1;
			margin-right: 20rpx;

			.message-input {
				background-color: #f8f8f8;
				border-radius: 24rpx;
				padding: 16rpx 24rpx;
				font-size: 28rpx;
				color: #333333;
				border: none;
				outline: none;
			}
		}

		.input-actions {
			display: flex;
			align-items: center;

			.action-btn {
				width: 64rpx;
				height: 64rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-left: 16rpx;
				border-radius: 50%;
				transition: all 0.3s ease;

				&.active {
					background-color: #1CBBB4;

					.emoji-icon,
					.plus-icon {
						color: #ffffff;
					}
				}

				.emoji-icon,
				.plus-icon {
					font-size: 32rpx;
					color: #666666;
				}
			}

			.send-btn {
				background-color: #1CBBB4;
				border-radius: 24rpx;
				padding: 12rpx 24rpx;
				margin-left: 16rpx;

				.send-text {
					font-size: 28rpx;
					color: #ffffff;
				}
			}
		}
	}

	/* 连接状态动画 */
	@keyframes pulse {
		0% {
			transform: scale(1);
			opacity: 1;
		}

		50% {
			transform: scale(1.2);
			opacity: 0.7;
		}

		100% {
			transform: scale(1);
			opacity: 1;
		}
	}

	/* 消息状态样式 */
	.message-status {
		margin-top: 8rpx;
		text-align: right;
		display: flex;
		justify-content: flex-end;
		align-items: center;

		.status-icon {
			font-size: 28rpx;
			font-weight: bold;
			line-height: 1;

			&.sending {
				color: #999999;
				animation: statusPulse 1.5s infinite;
			}

			&.sent {
				color: #1CBBB4;
				font-size: 32rpx;
			}

			&.failed {
				color: #ff4757;
				font-size: 32rpx;
			}
		}
	}

	/* 发送中动画效果 */
	@keyframes statusPulse {
		0% {
			opacity: 0.3;
		}

		50% {
			opacity: 1;
		}

		100% {
			opacity: 0.3;
		}
	}



	/* Emoji面板 */
	.emoji-panel {
		background-color: #f8f8f8;
		border-top: 1rpx solid #e0e0e0;

		.emoji-grid {
			padding: 32rpx;
			display: flex;
			flex-wrap: wrap;
			max-height: 400rpx;
			overflow-y: auto;

			.emoji-item {
				width: 80rpx;
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 48rpx;
				margin: 8rpx;
				border-radius: 12rpx;

				&:active {
					background-color: #e0e0e0;
				}
			}
		}
	}

	/* 更多菜单 */
	.more-menu {
		background-color: #f8f8f8;
		border-top: 1rpx solid #e0e0e0;

		.menu-grid {
			padding: 40rpx;
			display: flex;
			justify-content: space-around;

			.menu-item {
				display: flex;
				flex-direction: column;
				align-items: center;

				.menu-icon {
					width: 120rpx;
					height: 120rpx;
					border-radius: 24rpx;
					background-color: #ffffff;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 48rpx;
					margin-bottom: 16rpx;
					box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

					&:active {
						background-color: #f0f0f0;
						transform: scale(0.95);
					}
				}

				.menu-text {
					font-size: 24rpx;
					color: #666666;
				}
			}
		}
	}

	/* 动画效果 */
	@keyframes pulse {
		0% {
			opacity: 1;
			transform: scale(1);
		}

		50% {
			opacity: 0.5;
			transform: scale(1.2);
		}

		100% {
			opacity: 1;
			transform: scale(1);
		}
	}

	/* 上拉加载状态样式 */
	.loading-status {
		padding: 20rpx 0;
		text-align: center;

		.loading-more {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;

			.loading-text {
				font-size: 28rpx;
				color: #999;
				animation: statusPulse 1.5s infinite;
			}
		}
	}

	/* 没有更多数据样式 */
	.no-more-data {
		padding: 20rpx 0;
		text-align: center;

		.no-more-text {
			font-size: 28rpx;
			color: #8f94a3;
		}
	}
}

/* 自定义弹窗样式 */
.custom-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.custom-modal {
	background-color: #ffffff;
	border-radius: 16rpx;
	width: 600rpx;
	max-width: 90%;
	overflow: hidden;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
	padding: 40rpx 40rpx 20rpx 40rpx;
	text-align: center;

	.modal-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		line-height: 1.4;
	}
}

.modal-content {
	padding: 0 40rpx 40rpx 40rpx;

	.modal-message {
		.message-text {
			font-size: 28rpx;
			color: #666666;
			line-height: 1.6;
			text-align: center;
		}
	}
}

.modal-actions {
	display: flex;
	border-top: 1rpx solid #f0f0f0;

	.action-btn {
		flex: 1;
		padding: 32rpx 0;
		text-align: center;

		.btn-text {
			font-size: 32rpx;
		}

		&.cancel-btn {
			border-right: 1rpx solid #f0f0f0;

			.btn-text {
				color: #999999;
			}
		}

		&.confirm-btn {
			.btn-text {
				color: #1CBBB4;
				font-weight: bold;
			}
		}

		&:active {
			background-color: #f8f8f8;
		}
	}
}

/* 底部弹出层样式 */
.bottom-popup-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0);
	z-index: 9999;
	transition: background-color 0.3s ease;

	&.show {
		background-color: rgba(0, 0, 0, 0.5);
	}
}

.bottom-popup {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #ffffff;
	border-radius: 32rpx 32rpx 0 0;
	transform: translateY(100%);
	transition: transform 0.3s ease;

	&.show {
		transform: translateY(0);
	}
}

.popup-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx 40rpx 20rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;

	.popup-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
	}

	.close-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		background-color: #f5f5f5;

		.close-icon {
			font-size: 28rpx;
			color: #999999;
		}

		&:active {
			background-color: #e0e0e0;
		}
	}
}

.popup-content {
	padding: 20rpx 0 60rpx 0;
}

.contact-options {
	.option-item {
		display: flex;
		align-items: center;
		padding: 32rpx 40rpx;
		border-bottom: 1rpx solid #f8f8f8;

		&:last-child {
			border-bottom: none;
		}

		&:active {
			background-color: #f8f8f8;
		}

		.option-icon {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 40rpx;
			margin-right: 24rpx;

			&.phone-icon {
				background-color: #e8f4fd;
			}

			&.wechat-icon {
				background-color: #f0f9ff;
			}

			&.exchange-icon {
				background-color: #fff7e6;
			}
		}

		.option-info {
			flex: 1;

			.option-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333333;
				display: block;
				margin-bottom: 8rpx;
			}

			.option-desc {
				font-size: 26rpx;
				color: #999999;
				display: block;
				line-height: 1.4;
			}
		}
	}
}


::v-deep .u-popup {
	flex: none !important;
}

.u-popup-content {
	background: #d2f9f3;
	border-radius: 30rpx 30rpx 0rpx 0;

	.send-btn {
		width: 96%;
		background: #19c7a1;
		color: #fff;
		font-size: 28rpx;
		border-radius: 8rpx;
		margin-top: 16rpx;
		height: 80rpx;
		line-height: 80rpx;
		border: none;
		margin-bottom: 30rpx;
	}
}

.interview-card {
	background-color: white;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
	padding: 32rpx 24rpx 24rpx 24rpx;
	margin: 24rpx;
}

.interview-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #222;
	margin-bottom: 32rpx;
	text-align: center;
}

.interview-row {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 18rpx 0;
	border-bottom: 1rpx solid #e5e5e5;

	.required-star {
		color: #ff4444;
		font-size: 28rpx;
		margin-left: 8rpx;
	}
}

.interview-row:last-child {
	border-bottom: none;
}

.row-label {
	color: #666;
	font-size: 26rpx;
}

.row-value {
	color: #222;
	font-size: 26rpx;
	font-weight: 500;
}

.row-btn {
	background: #19c7a1;
	color: #fff;
	font-size: 22rpx;
	border-radius: 8rpx;
	padding: 8rpx 24rpx;
	border: none;
	// height: 40rpx;
	line-height: 40rpx;
	margin-right: 0rpx !important;
}

.interview-tip {
	color: #888;
	font-size: 22rpx;
	margin-top: 24rpx;
	line-height: 1.6;
}

.interview-date {
	width: 95%;
	margin: 0 auto;

	.row-label {
		font-size: 26rpx;
		color: #222;
		font-weight: bold;
		margin-bottom: 16rpx;
		padding: 12rpx;
	}

	.required-star {
		color: #ff4444;
		font-size: 28rpx;
		margin-left: 8rpx;
	}
}

.notice-card {
	width: 95%;
	//   background: #d2f9f3;
	background: white;
	border-radius: 12rpx;
	margin: 0 auto;
	margin-top: 30rpx;

}

.notice-title {
	font-size: 26rpx;
	color: #222;
	font-weight: bold;
	margin-bottom: 16rpx;
	padding: 12rpx;

	.required-star {
		color: #ff4444;
		font-size: 28rpx;
		margin-left: 8rpx;
	}
}

.notice-textarea-box {
	background: white;
	border-radius: 10rpx;
	padding: 16rpx;
	margin-bottom: 32rpx;
}

.notice-textarea {
	width: 100%;
	min-height: 100rpx;
	background: transparent;
	border: none;
	font-size: 24rpx;
	color: #333;
	resize: none;
}

.notice-bottom {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 8rpx;
}

.clear-btn {
	color: #888;
	font-size: 22rpx;
}

.count {
	color: #19c7a1;
	font-size: 22rpx;
}

.interview-invitation-card {
	// background: linear-gradient(135deg, #E8F8F5 0%, #D1F2EB 100%);
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin: 10rpx 0;
	border: 2rpx solid #B8E6D3;
	box-shadow: 0 4rpx 12rpx rgba(20, 177, 158, 0.15);
}

.invitation-header {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;

	.invitation-icon {
		font-size: 32rpx;
		margin-right: 12rpx;
	}

	.invitation-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #14B19E;
	}
}

.invitation-content {
	margin-bottom: 30rpx;

	.content-row {
		display: flex;
		align-items: flex-start;
		margin-bottom: 16rpx;

		&:last-child {
			margin-bottom: 0;
		}

		.row-icon {
			font-size: 28rpx;
			margin-right: 12rpx;
			margin-top: 2rpx;
			color: #666;
		}

		.row-text {
			font-size: 28rpx;
			color: #333;
			line-height: 1.4;
			flex: 1;
		}
	}
}

.invitation-actions {
	display: flex;
	gap: 20rpx;

	.action-btn {
		flex: 1;
		height: 72rpx;
		border-radius: 36rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.btn-text {
			font-size: 28rpx;
			font-weight: 500;
		}

		&.detail-btn {
			background: rgba(255, 255, 255, 0.8);
			border: 2rpx solid #E0E0E0;

			.btn-text {
				color: #666;
			}
		}

		&.accept-btn {
			background: #14B19E;

			.btn-text {
				color: white;
			}
		}

		&:active {
			opacity: 0.8;
			transform: scale(0.98);
		}
	}
}

/* 面试邀请已接受状态样式 */
.interview-accepted-card {
	background: linear-gradient(135deg, #E8F5E8 0%, #D4F1D4 100%);
	border-radius: 16rpx;
	padding: 24rpx;
	margin: 10rpx 0;
	border: 2rpx solid #B8E6B8;

	.accepted-header {
		display: flex;
		align-items: center;
		margin-bottom: 12rpx;

		.accepted-icon {
			font-size: 28rpx;
			margin-right: 10rpx;
		}

		.accepted-title {
			font-size: 28rpx;
			font-weight: 600;
			color: #4CAF50;
		}
	}

	.accepted-desc {
		font-size: 24rpx;
		color: #666;
		line-height: 1.4;
	}
}

/* 面试邀请已拒绝状态样式 */
.interview-rejected-card {
	background: linear-gradient(135deg, #FFF3F3 0%, #FFE8E8 100%);
	border-radius: 16rpx;
	padding: 24rpx;
	margin: 10rpx 0;
	border: 2rpx solid #FFB8B8;

	.rejected-header {
		display: flex;
		align-items: center;
		margin-bottom: 12rpx;

		.rejected-icon {
			font-size: 28rpx;
			margin-right: 10rpx;
		}

		.rejected-title {
			font-size: 28rpx;
			font-weight: 600;
			color: #F44336;
		}
	}

	.rejected-desc {
		font-size: 24rpx;
		color: #666;
		line-height: 1.4;
	}
}
</style>
