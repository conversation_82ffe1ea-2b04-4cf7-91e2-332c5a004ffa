
var isReady=false;var onReadyCallbacks=[];
var isServiceReady=false;var onServiceReadyCallbacks=[];
var __uniConfig = {"pages":["pages/login/login","pages/index/index","pages/user/user","pages/position/position","pages/publish/publish","pages/message/message","pages/chat/chat","pages/second/second","pages/second/positions/positions","pages/user/settings/settings","pages/user/interests/positions","pages/user/interests/jobUpdate","pages/user/interests/invitation","pages/user/function/helpCenter","pages/second/map/index","pages/second/map/address","pages/second/released/index","pages/authentication/index","pages/authentication/positioning","pages/authentication/adress","pages/audits/index","pages/position/detail","pages/position/selectedAddress","pages/position/serch","pages/position/viewDetails","pages/user/settings/emitpassword/index","pages/user/settings/emitpassword/modify","pages/user/settings/emitpassword/setpassword","pages/user/settings/emitpassword/information","pages/user/realName","pages/position/jobserch","pages/index/nearbydetail/index","pages/index/signeddetail/index","pages/demo/websocket-test","pages/second/map/mapPicker","pages/homeIndex/index","pages/user/personal/index","pages/homeIndex/detail/index","pages/user/personal/emitName","pages/user/personal/changeReal/emitRealname","pages/user/personal/changeReal/emitPhone","pages/user/personal/changeReal/emitCode","pages/user/personal/emitWechat","pages/user/personal/emitCompany","pages/user/personal/myPositions","pages/user/personal/emitPhone","pages/user/recruitment/topPosition","pages/user/recruitment/urgentPositin","pages/user/recruitment/chatOnline","pages/user/recruitment/directDial","pages/user/recruitment/jobUpdate","pages/user/function/integration","pages/user/function/invoice","pages/user/function/feedback","pages/user/function/FeedbackDetails","pages/user/function/application","pages/user/function/feedHistory","pages/user/function/collection","pages/user/function/company","pages/user/function/settings","pages/user/function/agreement","pages/chat/mapPicker","pages/chat/vaResume","pages/smart/chat","pages/smart/onlineService","pages/smart/serviceDemo","pages/chat/userSetting","pages/user/function/recharge","pages/user/function/pointsList","pages/user/usercontont/InterviewList","pages/user/usercontont/InterviewDetail","pages/audits/failed","pages/audits/failed","pages/user/function/invoicingDetail","pages/user/settings/emitpassword/cancellation","pages/user/settings/emitpassword/canceagr","pages/user/function/recruitmen","uni_modules/uni-feedback/pages/opendb-feedback/opendb-feedback","uni_modules/uni-id-pages/pages/userinfo/userinfo","uni_modules/uni-id-pages/pages/userinfo/realname-verify/realname-verify","uni_modules/uni-id-pages/pages/login/login-withoutpwd","uni_modules/uni-id-pages/pages/login/login-withpwd","uni_modules/uni-id-pages/pages/userinfo/deactivate/deactivate","uni_modules/uni-id-pages/pages/userinfo/bind-mobile/bind-mobile","uni_modules/uni-id-pages/pages/login/login-smscode","uni_modules/uni-id-pages/pages/register/register","uni_modules/uni-id-pages/pages/retrieve/retrieve","uni_modules/uni-id-pages/pages/common/webview/webview","uni_modules/uni-id-pages/pages/userinfo/change_pwd/change_pwd","uni_modules/uni-id-pages/pages/register/register-by-email","uni_modules/uni-id-pages/pages/retrieve/retrieve-by-email","uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd","uni_modules/uni-id-pages/pages/userinfo/cropImage/cropImage","uni_modules/uni-id-pages/pages/register/register-admin"],"window":{"navigationBarTextStyle":"black","navigationBarTitleText":"uni-starter","navigationBarBackgroundColor":"#FFFFFF","backgroundColor":"#F8F8F8","enablePullDownRefresh":false,"rpxCalcMaxDeviceWidth":375,"rpxCalcBaseDeviceWidth":375},"tabBar":{"color":"#969ca1","selectedColor":"#000000","borderStyle":"black","list":[{"pagePath":"pages/homeIndex/index","text":"候选人","iconPath":"static/tabbar/candidates.png","selectedIconPath":"static/tabbar/candidateschoi.png","selectedColor":"#000000"},{"pagePath":"pages/position/position","text":"职位","iconPath":"static/tabbar/posicc.png","selectedIconPath":"static/tabbar/posiblack.png","selectedColor":"#000000"},{"pagePath":"pages/publish/publish","text":"发布","iconPath":"static/tabbar/add.png","selectedIconPath":"static/tabbar/add.png","selectedColor":"#000000"},{"pagePath":"pages/message/message","text":"消息","iconPath":"static/tabbar/candidates.png","selectedIconPath":"static/tabbar/candidateschoi.png","selectedColor":"#000000"},{"pagePath":"pages/user/user","text":"我的","iconPath":"static/tabbar/my.png","selectedIconPath":"static/tabbar/mychoice.png","selectedColor":"#000000"}],"midButton":{"width":"80px","height":"50px","iconPath":"static/tabbar/add.png","iconWidth":"24px","backgroundImage":"static/tabbar/add.png"}},"darkmode":false,"nvueCompiler":"uni-app","nvueStyleCompiler":"uni-app","renderer":"auto","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":false},"appname":"容翼聘企业版","compilerVersion":"4.75","entryPagePath":"pages/index/index","entryPageQuery":"","realEntryPagePath":"pages/login/login","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000}};
var __uniRoutes = [{"path":"/pages/login/login","meta":{"isQuit":true},"window":{"navigationStyle":"custom"}},{"path":"/pages/index/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/user/user","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationStyle":"custom"}},{"path":"/pages/position/position","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationStyle":"custom"}},{"path":"/pages/publish/publish","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationStyle":"custom"}},{"path":"/pages/message/message","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationStyle":"custom"}},{"path":"/pages/chat/chat","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/second/second","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/second/positions/positions","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/settings/settings","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/interests/positions","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/interests/jobUpdate","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/interests/invitation","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/function/helpCenter","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/second/map/index","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/second/map/address","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/second/released/index","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/authentication/index","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/authentication/positioning","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/authentication/adress","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/audits/index","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/position/detail","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/position/selectedAddress","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/position/serch","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/position/viewDetails","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/settings/emitpassword/index","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/settings/emitpassword/modify","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/settings/emitpassword/setpassword","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/settings/emitpassword/information","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/realName","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/position/jobserch","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/index/nearbydetail/index","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/index/signeddetail/index","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/demo/websocket-test","meta":{},"window":{"navigationBarTitleText":"WebSocket测试","enablePullDownRefresh":false}},{"path":"/pages/second/map/mapPicker","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/homeIndex/index","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/personal/index","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/homeIndex/detail/index","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/personal/emitName","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/personal/changeReal/emitRealname","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/personal/changeReal/emitPhone","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/personal/changeReal/emitCode","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/personal/emitWechat","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/personal/emitCompany","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/personal/myPositions","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/personal/emitPhone","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/recruitment/topPosition","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/recruitment/urgentPositin","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/recruitment/chatOnline","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/recruitment/directDial","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/recruitment/jobUpdate","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/function/integration","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/function/invoice","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/function/feedback","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/function/FeedbackDetails","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/function/application","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/function/feedHistory","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/function/collection","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/function/company","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/function/settings","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/function/agreement","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/chat/mapPicker","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/chat/vaResume","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/smart/chat","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/smart/onlineService","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false,"navigationBarTitleText":"在线咨询"}},{"path":"/pages/smart/serviceDemo","meta":{},"window":{"navigationBarTitleText":"客服演示","enablePullDownRefresh":false}},{"path":"/pages/chat/userSetting","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/function/recharge","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/function/pointsList","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/usercontont/InterviewList","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/usercontont/InterviewDetail","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/audits/failed","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/function/invoicingDetail","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/settings/emitpassword/cancellation","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/settings/emitpassword/canceagr","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/function/recruitmen","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/uni_modules/uni-feedback/pages/opendb-feedback/opendb-feedback","meta":{},"window":{"navigationBarTitleText":"意见反馈","enablePullDownRefresh":false}},{"path":"/uni_modules/uni-id-pages/pages/userinfo/userinfo","meta":{},"window":{"navigationBarTitleText":"个人资料"}},{"path":"/uni_modules/uni-id-pages/pages/userinfo/realname-verify/realname-verify","meta":{},"window":{"enablePullDownRefresh":false,"navigationBarTitleText":"实名认证"}},{"path":"/uni_modules/uni-id-pages/pages/login/login-withoutpwd","meta":{},"window":{}},{"path":"/uni_modules/uni-id-pages/pages/login/login-withpwd","meta":{},"window":{}},{"path":"/uni_modules/uni-id-pages/pages/userinfo/deactivate/deactivate","meta":{},"window":{"navigationBarTitleText":"注销账号"}},{"path":"/uni_modules/uni-id-pages/pages/userinfo/bind-mobile/bind-mobile","meta":{},"window":{"navigationBarTitleText":"绑定手机号码"}},{"path":"/uni_modules/uni-id-pages/pages/login/login-smscode","meta":{},"window":{"navigationBarTitleText":"手机验证码登录"}},{"path":"/uni_modules/uni-id-pages/pages/register/register","meta":{},"window":{"navigationBarTitleText":"注册"}},{"path":"/uni_modules/uni-id-pages/pages/retrieve/retrieve","meta":{},"window":{"navigationBarTitleText":"重置密码"}},{"path":"/uni_modules/uni-id-pages/pages/common/webview/webview","meta":{},"window":{"enablePullDownRefresh":false,"navigationBarTitleText":""}},{"path":"/uni_modules/uni-id-pages/pages/userinfo/change_pwd/change_pwd","meta":{},"window":{"enablePullDownRefresh":false,"navigationBarTitleText":"修改密码"}},{"path":"/uni_modules/uni-id-pages/pages/register/register-by-email","meta":{},"window":{"navigationBarTitleText":"邮箱验证码注册"}},{"path":"/uni_modules/uni-id-pages/pages/retrieve/retrieve-by-email","meta":{},"window":{"navigationBarTitleText":"通过邮箱重置密码"}},{"path":"/uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd","meta":{},"window":{"enablePullDownRefresh":false,"navigationBarTitleText":"设置密码"}},{"path":"/uni_modules/uni-id-pages/pages/userinfo/cropImage/cropImage","meta":{},"window":{}},{"path":"/uni_modules/uni-id-pages/pages/register/register-admin","meta":{},"window":{"enablePullDownRefresh":false,"navigationBarTitleText":"注册管理员账号"}}];
__uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
__uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:Math.round(f/20)})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:void 0,window:void 0,document:void 0,frames:void 0,self:void 0,location:void 0,navigator:void 0,localStorage:void 0,history:void 0,Caches:void 0,screen:void 0,alert:void 0,confirm:void 0,prompt:void 0,fetch:void 0,XMLHttpRequest:void 0,WebSocket:void 0,webkit:void 0,print:void 0}}}});
