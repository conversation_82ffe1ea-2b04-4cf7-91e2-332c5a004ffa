<template>
    <view class="wechat-edit-container">
        <!-- 导航栏 -->
        <view class="navbar">
            <view class="nav-left" @click="goBack">
                <text class="nav-icon">‹</text>
            </view>
            <view class="nav-title">
                <text class="title-text">修改微信号</text>
            </view>
            <view class="nav-right"></view>
        </view>

        <!-- 内容区域 -->
        <view class="content">
            <!-- 提示信息 -->
            <view class="tip-section">
                <text class="tip-text">{{ tipText }}</text>
            </view>

            <!-- 输入区域 -->
            <view class="input-section">
                <view class="input-label">
                    <text class="label-text">输入微信号</text>
                </view>
                <view class="input-wrapper">
                    <input class="wechat-input" type="text" v-model="wechatId" placeholder="请输入微信号" :focus="true" />
                </view>
            </view>

            <!-- 底部按钮 -->
            <view class="bottom-section">
                <view class="save-btn" @click="saveWechatId">
                    <text class="btn-text">保存</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { realApi } from "@/utils/api"
export default {
    data() {
        return {
            wechatId: '', // 微信号
            originalWechatId: '', // 原始微信号，用于比较是否有变化
            tipText: '' // 提示文字
        }
    },
    onLoad() {
        // 获取用户信息
        const userInfo = uni.getStorageSync('usinfo');
        if (userInfo && userInfo.wechat) {
            this.wechatId = userInfo.wechat;
            this.originalWechatId = userInfo.wechat;
            // 格式化微信号显示（隐藏中间部分）
            const maskedWechat = this.maskWechatId(userInfo.wechat);
            this.tipText = `当前微信号为：${maskedWechat}，修改后，"交换微信"功能的微信号会被修改`;
        } else {
            this.tipText = '暂无绑定微信，请绑定微信号';
        }
    },
    methods: {
        // 返回上一页
        goBack() {
            // 检查是否有未保存的更改
            if (this.wechatId !== this.originalWechatId) {
                uni.showModal({
                    title: '提示',
                    content: '您有未保存的更改，确定要离开吗？',
                    success: (res) => {
                        if (res.confirm) {
                            uni.navigateBack();
                        }
                    }
                });
            } else {
                uni.navigateBack();
            }
        },

        // 保存微信号
        async saveWechatId() {
            if (!this.wechatId.trim()) {
                uni.showToast({
                    title: '请输入微信号',
                    icon: 'none'
                });
                return;
            }

            // 验证微信号格式（可以是字母、数字、下划线、减号，6-20位）
            // const wechatRegex = /^[a-zA-Z0-9_-]{6,20}$/;
            // if (!wechatRegex.test(this.wechatId)) {
            //     uni.showToast({
            //         title: '微信号格式不正确，请输入6-20位字母、数字、下划线或减号',
            //         icon: 'none',
            //         duration: 3000
            //     });
            //     return;
            // }

            // 这里可以添加调用API更新服务器数据的逻辑
            // 例如：this.updateWechatIdToServer(this.wechatId);
            const res = await realApi.wechatEdit({ wechat: this.wechatId.trim() })
            if (res.code == 200) {
                let userInfo = uni.getStorageSync('usinfo') || {};
                userInfo.wechat = this.wechatId.trim();
                uni.setStorageSync('usinfo', userInfo);
                uni.showToast({
                    title: '保存成功',
                    icon: 'success'
                });

                // 延迟返回上一页
                setTimeout(() => {
                    uni.navigateBack();
                }, 1500);
            } else {
                uni.showToast({
                    title: res.msg || '保存失败，请重试',
                    icon: 'none'
                });
            }
        },

        // 格式化微信号显示（隐藏中间部分）
        maskWechatId(wechatId) {
            if (!wechatId) return '';
            if (wechatId.length <= 6) return wechatId;

            const start = wechatId.substring(0, 3);
            const end = wechatId.substring(wechatId.length - 2);
            const middle = '*'.repeat(6);

            return `${start}${middle}${end}`;
        }
    }
}
</script>

<style lang="scss" scoped>
.wechat-edit-container {
    min-height: 100vh;
    background: #f8f9fa;
}

/* 导航栏样式 */
.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    padding: 0 30rpx;
    background: #ffffff;
    border-bottom: 1rpx solid #f0f0f0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-left,
.nav-right {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-icon {
    font-size: 40rpx;
    color: #333333;
    font-weight: 500;
}

.nav-title {
    flex: 1;
    text-align: center;
}

.title-text {
    font-size: 32rpx;
    color: #333333;
    font-weight: 500;
}

.save-text {
    font-size: 28rpx;
    color: #4ECDC4;
    font-weight: 500;
}

/* 内容区域 */
.content {
    flex: 1;
    padding: 40rpx 30rpx;
}

/* 提示信息区域 */
.tip-section {
    margin-bottom: 40rpx;
    padding: 0 10rpx;
}

.tip-text {
    font-size: 26rpx;
    color: #999999;
    line-height: 36rpx;
}

/* 输入区域 */
.input-section {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 40rpx 30rpx;
    margin-bottom: 40rpx;
}

.input-label {
    margin-bottom: 20rpx;
}

.label-text {
    font-size: 28rpx;
    color: #666666;
    line-height: 40rpx;
}

.input-wrapper {
    border-bottom: 1rpx solid #f0f0f0;
    padding-bottom: 20rpx;
}

.wechat-input {
    width: 100%;
    font-size: 32rpx;
    color: #333333;
    line-height: 44rpx;
    padding: 0;
    border: none;
    outline: none;
    background: transparent;
}

.wechat-input::placeholder {
    color: #cccccc;
}

/* 底部区域 */
.bottom-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 40rpx 30rpx;
    padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
    background: #f8f9fa;
}

.save-btn {
    width: 100%;
    height: 88rpx;
    background: #4ECDC4;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(78, 205, 196, 0.3);
}

.btn-text {
    font-size: 32rpx;
    color: #ffffff;
    font-weight: 500;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
    .navbar {
        height: 80rpx;
        padding: 0 24rpx;
    }

    .content {
        padding: 32rpx 24rpx;
    }

    .input-section {
        padding: 32rpx 24rpx;
    }

    .bottom-section {
        padding: 32rpx 24rpx;
        padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
    }
}
</style>