<template>
	<view class="home-container">
		<!-- 顶部筛选标签 -->
		<view class="filter-tabs-container">
			<view class="filter-tabs-wrapper">
				<scroll-view class="filter-tabs" scroll-x="true" show-scrollbar="false">
					<view class="filter-tab-item" v-for="(item, index) in filterTabs" :key="index"
						:class="{ active: currentFilterIndex === index }" @click="switchFilterTab(index, item)">
						{{ item.name }}
					</view>
				</scroll-view>
				<view class="add-filter-btn" @click="addFilterTab">
					<text class="iconfont icon-add">+</text>
				</view>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- 推荐/附近/最新 tab切换 和 地区筛选 -->
			<view class="content-header">
				<!-- 左侧：推荐/附近/最新 -->
				<view class="content-tabs">
					<view class="content-tab-item" v-for="(tab, index) in contentTabs" :key="index"
						:class="{ active: currentContentIndex === index }" @click="switchContentTab(index)">
						{{ tab.name }}
					</view>
				</view>

				<!-- 右侧：地区和筛选 -->
				<view class="header-actions">
					<view class="location-selector" @click="show = true">
						<text class="location-text">{{ currentLocation }}</text>
						<text class="location-icon">▼</text>
					</view>
					<view class="filter-btn" @click="showFilter">
						<text class="filter-icon">筛选</text>
					</view>
				</view>
			</view>

			<!-- 列表内容 -->
			<scroll-view class="list-container" scroll-y="true" refresher-enabled="true"
				:refresher-triggered="refreshing" @refresherrefresh="onRefresh" @scrolltolower="onLoadMore"
				:lower-threshold="50">

				<view class="list-item" v-if="currentList.length > 0" v-for="(item, index) in currentList" :key="index">
					<view class="item-content">
						<!-- 上：头像和基本信息 -->
						<view class="top-section" @click="gotodetail(item)">
							<view class="avatar-container">
								<image class="avatar" :src="item.avatarUrl" mode="aspectFill"></image>
							</view>
							<view class="basic-info">
								<view class="user-name">{{ item.username }}</view>
								<view class="user-details">
									<text class="experience" v-if="item.work_year">{{ item.work_year }}年</text>
									<text class="divider">|</text>
									<text class="education" v-if="item.degree_name">{{ item.degree_name }}</text>
									<text class="divider">|</text>
									<text class="age" v-if="item.age">{{ item.age }}岁</text>
									<text class="divider">|</text>
									<text class="location">{{ item.location }}</text>
								</view>
							</view>
						</view>

						<!-- 中：技能和标签 -->
						<view class="middle-section">
							<view class="skills">
								<text class="skill-label">求职期望:</text>
								<text class="skill-text">{{ item.job_name }}</text>
								<text class="skill-text" style="margin-left: 30rpx;">{{ item.minsalary }} - {{
									item.maxsalary }}</text>
							</view>
							<view class="tags">
								<view class="tag" v-for="(tag, tagIndex) in item.skills_title" :key="tagIndex">
									{{ tag }}
								</view>
							</view>
						</view>

						<!-- 下：描述和按钮 -->
						<view class="bottom-section">
							<view class="description">{{ item.advantage }}</view>
							<view class="action-btn" @click="contactUser(item)">
								<text class="btn-text">打招呼</text>
							</view>
						</view>
					</view>
				</view>



				<!-- 暂无数据提示 -->
				<view class="no-data" v-if="currentList.length === 0 && !loading">
					<text class="no-data-text">暂无数据</text>
				</view>

				<!-- 加载更多提示 -->
				<view class="load-more" v-if="loading">
					<text class="loading-text">加载中...</text>
				</view>

				<!-- 没有更多数据提示 -->
				<view class="no-more" v-if="noMore && currentList.length > 0 && !loading">
					<text class="no-more-text">暂无更多数据了</text>
				</view>

			</scroll-view>
		</view>

		<!-- 地区选择弹窗 -->
		<u-popup :show="show" @close="close" @open="open">
			<view class="location-popup">
				<!-- 弹窗标题 -->
				<view class="popup-header">
					<text class="popup-title">选择城市</text>
					<view class="close-btn" @click="close">
						<text class="close-icon">×</text>
					</view>
				</view>

				<!-- 主要内容区域 -->
				<view class="popup-content">
					<!-- 左侧：当前位置 -->
					<view class="left-section">
						<view class="current-location-header">
							<text class="section-title">当前位置</text>
						</view>
						<view class="current-location-item">
							<text class="location-name">{{ loact }}</text>
						</view>
					</view>

					<!-- 右侧：城市选择 -->
					<view class="right-section">
						<view class="cities-header">
							<text class="section-title">区域选择</text>
						</view>
						<scroll-view class="cities-list" scroll-y="true">
							<view class="city-item" v-for="(city, index) in randomCities" :key="index"
								:class="{ selected: selectedCity === city.name }" @click="selectRandomCity(city)">
								<text class="city-name">{{ city.name }}</text>
								<view class="check-icon" v-if="selectedCity === city.name">
									<text class="check-text">✓</text>
								</view>
							</view>
						</scroll-view>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 筛选弹窗 -->
		<u-popup :show="showFilterPopup" @close="closeFilter" @open="openFilter">
			<view class="filter-popup">
				<!-- 弹窗标题 -->
				<view class="popup-header">
					<view class="close-btn" @click="closeFilter">
						<text class="close-icon">×</text>
					</view>
					<text class="popup-title">筛选</text>
					<view class="placeholder"></view>
				</view>

				<!-- 筛选内容 -->
				<scroll-view class="filter-content" scroll-y="true">
					<!-- 性别 -->
					<view class="filter-section">
						<view class="filter-section-title">
							<text class="section-title">性别</text>
						</view>
						<view class="filter-options-grid">
							<view class="filter-option-item" v-for="(option, index) in filterOptions.gender"
								:key="index" :class="{ selected: option.selected }"
								@click="selectFilterOption('gender', option)">
								<text class="option-text">{{ option.name }}</text>
							</view>
						</view>
					</view>

					<!-- 经验要求 -->
					<view class="filter-section">
						<view class="filter-section-title">
							<text class="section-title">经验要求</text>
						</view>
						<view class="filter-options-grid">
							<view class="filter-option-item" v-for="(option, index) in filterOptions.experience"
								:key="index" :class="{ selected: option.selected }"
								@click="selectFilterOption('experience', option)">
								<text class="option-text">{{ option.name }}</text>
							</view>
						</view>
					</view>

					<!-- 学历要求 -->
					<view class="filter-section">
						<view class="filter-section-title">
							<text class="section-title">学历要求</text>
						</view>
						<view class="filter-options-grid">
							<view class="filter-option-item" v-for="(option, index) in filterOptions.education"
								:key="index" :class="{ selected: option.selected }"
								@click="selectFilterOption('education', option)">
								<text class="option-text">{{ option.name }}</text>
							</view>
						</view>
					</view>

					<!-- 求职意向 -->
					<view class="filter-section">
						<view class="filter-section-title">
							<text class="section-title">求职意向</text>
						</view>
						<view class="filter-options-grid">
							<view class="filter-option-item" v-for="(option, index) in filterOptions.jobStatus"
								:key="index" :class="{ selected: option.selected }"
								@click="selectFilterOption('jobStatus', option)">
								<text class="option-text">{{ option.name }}</text>
							</view>
						</view>
					</view>

					<!-- 薪资待遇 -->
					<view class="filter-section">
						<view class="filter-section-title">
							<text class="section-title">薪资待遇</text>
						</view>
						<view class="filter-options-grid">
							<view class="filter-option-item" v-for="(option, index) in filterOptions.salary"
								:key="index" :class="{ selected: option.selected }"
								@click="selectFilterOption('salary', option)">
								<text class="option-text">{{ option.name }}</text>
							</view>
						</view>
					</view>

					<!-- 底部占位，确保内容可以完全滚动 -->
					<view style="height: 40rpx;"></view>
				</scroll-view>

				<!-- 底部按钮 -->
				<view class="filter-footer">
					<view class="confirm-btn" @click="confirmFilter">
						<text class="confirm-text">确定</text>
					</view>
				</view>
			</view>
		</u-popup>
		<!-- 自定义弹窗 -->
		<view class="custom-modal-overlay" v-if="showCustomModal" @click="closeCustomModal">
			<view class="custom-modal" @click.stop>
				<view class="modal-header">
					<view class="modal-icon">
						<text class="icon-warning">⚠️</text>
					</view>
					<view class="modal-title">{{ modalData.title }}</view>
				</view>
				<view class="modal-content">
					<text class="modal-text">{{ modalData.content }}</text>
				</view>
				<view class="modal-footer">
					<button class="modal-btn confirm-btn" @click="handleConfirm">
						{{ modalData.confirmText }}
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { home, positionsApi, dictApi, addApi } from "@/utils/api.js"
export default {
	data() {
		return {
			// 筛选标签
			filterTabs: [
				// { name: 'UI设计师', id: 1 },
				// { name: '平面设计师', id: 2 },
				// { name: 'JAVA工程师', id: 3 },
				// { name: '前端工程师', id: 4 },
				// { name: '产品经理', id: 5 },
				// { name: '运营专员', id: 6 },
				// { name: '数据分析师', id: 7 },
				// { name: '测试工程师', id: 8 }
			],
			currentFilterIndex: 0,
			DistrictCode: '',
			// 内容标签
			contentTabs: [
				{ name: '推荐', id: 'recommend' },
				{ name: '附近', id: 'nearby' },
				{ name: '最新', id: 'latest' }
			],
			currentContentIndex: 0,
			// 地区选择
			currentLocation: '',
			loact: '',
			show: false,
			selectedCity: '', // 当前选中的城市
			randomCities: [], // 随机生成的城市列表

			// 筛选弹窗相关
			showFilterPopup: false,
			filterOptions: {
				gender: [],
				experience: [],
				education: [],
				jobStatus: [],
				salary: []
			},

			// 列表数据
			// 状态
			refreshing: false,
			loading: false,
			noMore: false,
			page: 1,
			pageSize: 10,
			currentList: [],
			total: 0,
			pagelist: {},

			// 当前选中的筛选条件
			currentFilterParams: {
				gender: null,
				experience: null,
				education: null,
				jobStatus: null,
				salary: null
			},

			// 自定义弹窗相关数据
			showCustomModal: false,
			modalData: {
				title: '提示',
				content: '您还没有发布职位，请去发布职位',
				confirmText: '去发布'
			}
		}
	},

	computed: {
	},

	async onShow() {
		// 获取筛选选项数据
		const nary = await dictApi.getDict()

		// 处理筛选选项数据，添加selected属性并设置默认选中
		this.filterOptions.gender = this.processFilterOptions(nary.data.user_sex.data)
		this.filterOptions.experience = this.processFilterOptions(nary.data.job_exp.data)
		this.filterOptions.education = this.processFilterOptions(nary.data.job_edu.data)
		this.filterOptions.jobStatus = this.processFilterOptions(nary.data.where_jobstatus.data)
		this.filterOptions.salary = this.processFilterOptions(nary.data.where_salary.data)

		// 设置默认筛选条件（选中每个分类的第一个选项）
		this.setDefaultFilterParams()

		// 获取职位列表数据
		let res = await positionsApi.postJobList({
			page: 1,
			size: 10000,
			status: 1,
			order: 0
		})
		if (res.code == 200) {
			this.filterTabs = res.data.data || []
			if (this.filterTabs.length > 0) {
				this.closeCustomModal()
				uni.hideLoading()
				// return
			} else {
				this.currentList = []
				uni.hideLoading()
				this.showCustomModalDialog()

			}
			this.currentLocation = this.filterTabs[0].city_name
			this.loact = this.filterTabs[0].city_name
			this.selectedCity = this.currentLocation; // 同步选中的城市
			// this.DistrictCode = this.filterTabs[0].city_id
			this.getcity(this.filterTabs[0])
			this.loadCurrentTabData()
			uni.hideLoading()

		} else {
			uni.showToast({
				title: res.msg,
				icon: 'none'
			});
		}
		uni.hideLoading()
	},
	// onShow() {
	// 	console.log('234',this.filterTabs.length)
	// 	if (this.filterTabs.length > 0) {
	// 		this.closeCustomModal()
	// 	}else{
	// 		this.showCustomModalDialog()
	// 	}
	// },
	methods: {
		// 显示自定义弹窗
		showCustomModalDialog() {
			this.showCustomModal = true;
		},
		// 关闭自定义弹窗
		closeCustomModal() {
			this.showCustomModal = false;
		},

		// 处理确认按钮点击
		handleConfirm() {
			this.closeCustomModal();
			// 跳转到发布职位页面
			uni.navigateTo({
				url: '/pages/second/second'
			});
		},

		gotodetail(item) {
			console.log(item.id)
			uni.navigateTo({
				url: `/pages/homeIndex/detail/index?seeker_id=${item.id}&job_id=${item.job_id}`
			})
		},
		// 处理筛选选项数据，添加selected属性
		processFilterOptions(data) {
			if (!data || !Array.isArray(data)) return []

			return data.map((item, index) => ({
				...item, // 保留原有的所有属性（如id, name等）
				selected: index === 0 // 默认选中第一个选项
			}))
		},

		// 设置默认筛选条件
		setDefaultFilterParams() {
			// 获取每个分类中默认选中的选项（第一个选项）
			Object.keys(this.filterOptions).forEach(category => {
				const options = this.filterOptions[category]
				if (options && options.length > 0) {
					const defaultOption = options.find(item => item.selected)
					if (defaultOption) {
						this.currentFilterParams[category] = defaultOption
					}
				}
			})
			console.log('设置默认筛选条件:', this.currentFilterParams)
		},

		async getcity(item) {
			const city = await home.getAreaByCityId({
				city_id: item.city_id
			})
			this.randomCities = city.data || []
			uni.hideLoading()
		},

		open() {
			// console.log('open');
		},
		close() {
			this.show = false
			// console.log('close');
		},

		// 筛选弹窗打开
		openFilter() {
			// console.log('filter open');
		},
		// 切换筛选标签
		switchFilterTab(index, item) {
			console.log(index)
			this.currentFilterIndex = index;
			this.currentLocation = item.city_name
			this.selectedCity = this.currentLocation; // 同步选中的城市
			this.loact = item.city_name
			this.DistrictCode = 0

			// 重置数据并重新加载（保持当前筛选条件）
			this.resetData();
			this.loadCurrentTabData();
		},

		// 添加筛选标签
		addFilterTab() {
			uni.navigateTo({ url: '/pages/second/second' })
		},

		// 切换内容标签
		switchContentTab(index) {
			this.currentContentIndex = index;
			this.resetData();
			this.loadCurrentTabData(); // 保持当前筛选条件
		},

		// 下拉刷新
		async onRefresh() {
			this.refreshing = true;
			this.resetData();
			await this.loadCurrentTabData(); // 保持当前筛选条件
			this.refreshing = false;
		},

		// 上拉加载更多
		async onLoadMore() {
			if (this.loading || this.noMore) return;
			this.page++;
			await this.loadCurrentTabData(); // 保持当前筛选条件
		},

		// 重置数据
		resetData() {
			this.page = 1;
			this.noMore = false;
			this.currentList = [];
		},

		// 加载当前标签页数据
		async loadCurrentTabData(selectedFilters = null) {
			if (this.loading) return;
			this.loading = true;

			try {
				const currentFilter = this.filterTabs[this.currentFilterIndex];

				// 构建请求参数
				const params = {
					area_id: this.DistrictCode || 0,
					job_id: currentFilter.id,
					order: this.currentContentIndex,
				};

				// 添加筛选条件参数
				if (selectedFilters) {
					// 如果传入了新的筛选条件，使用新的筛选条件并更新当前筛选条件
					this.currentFilterParams = { ...selectedFilters };

					if (selectedFilters.gender && selectedFilters.gender.id) {
						params.sex = selectedFilters.gender.id;
					}
					if (selectedFilters.experience && selectedFilters.experience.id) {
						params.experience = selectedFilters.experience.id;
					}
					if (selectedFilters.education && selectedFilters.education.id) {
						params.education = selectedFilters.education.id;
					}
					if (selectedFilters.jobStatus && selectedFilters.jobStatus.id) {
						params.jobStatus = selectedFilters.jobStatus.id;
					}
					if (selectedFilters.salary && selectedFilters.salary.id) {
						params.jobsalary = selectedFilters.salary.id;
					}
				} else {
					// 使用当前存储的筛选条件
					if (this.currentFilterParams.gender && this.currentFilterParams.gender.id) {
						params.sex = this.currentFilterParams.gender.id;
					}
					if (this.currentFilterParams.experience && this.currentFilterParams.experience.id) {
						params.experience = this.currentFilterParams.experience.id;
					}
					if (this.currentFilterParams.education && this.currentFilterParams.education.id) {
						params.education = this.currentFilterParams.education.id;
					}
					if (this.currentFilterParams.jobStatus && this.currentFilterParams.jobStatus.id) {
						params.jobStatus = this.currentFilterParams.jobStatus.id;
					}
					if (this.currentFilterParams.salary && this.currentFilterParams.salary.id) {
						params.jobsalary = this.currentFilterParams.salary.id;
					}
				}

				// 如果不是第一页，添加 ids_page 参数
				if (this.page > 1 && this.pagelist && this.pagelist[this.page]) {
					params.search_ids = this.pagelist[this.page];
				}

				console.log('API请求参数:', params);
				const res = await home.getRecommendTalentList(params);

				if (res.code === 200) {
					const newData = res.data.data || [];

					if (this.page === 1) {
						// 第一页，直接替换数据
						this.currentList = newData;
						this.total = res.data.total;
						this.pagelist = res.data.ids_page;

					} else {
						// 后续页，追加数据
						this.currentList.push(...newData);
						// 更新 pagelist（如果后端返回了新的分页信息）
						if (res.data.ids_page) {
							this.pagelist = { ...this.pagelist, ...res.data.ids_page };
						}
					}

					// 判断是否还有更多数据
					if (newData.length === 0 || this.currentList.length >= this.total) {
						this.noMore = true;
					}

					console.log('加载成功，当前数据量:', this.currentList.length);
					console.log('总数据量:', this.total);
					console.log('分页信息:', this.pagelist);
					uni.hideLoading()
				} else {
					uni.showToast({
						title: res.msg || '加载失败',
						icon: 'none'
					});

					// 如果加载失败，回退页码
					if (this.page > 1) {
						this.page--;
					}
					uni.hideLoading()
				}

			} catch (error) {
				console.error('加载数据失败:', error);
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				});

				// 如果加载失败，回退页码
				if (this.page > 1) {
					this.page--;
				}
				uni.hideLoading()
			} finally {
				this.loading = false;
			}
		},

		// 联系用户
		async contactUser(item) {
			console.log(item)
			const params = {
				job_id: item.job_id,
				seeker_id: item.id
			}
			let res = await addApi.activeInvite(params)
			if (res.code == 200) {
				let user = {
					...item,
					user_id: item.id
				}
				const userInfo = encodeURIComponent(JSON.stringify(user))
				uni.navigateTo({
					url: `/pages/chat/chat?userInfo=${userInfo}`
				})
			} else {
				uni.showToast({
					title: res.msg,
					icon: 'none'
				})

				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/user/recruitment/chatOnline'
					});
				}, 1500)
			}

		},


		// 打开地区弹窗
		openLocationPopup() {
			console.log('地区弹窗打开');
		},


		// 显示筛选
		showFilter() {
			this.showFilterPopup = true;
		},

		// 关闭筛选弹窗
		closeFilter() {
			this.showFilterPopup = false;
		},

		// 选择筛选选项
		selectFilterOption(category, option) {
			// 先取消该分类下所有选项的选中状态
			this.filterOptions[category].forEach(item => {
				item.selected = false;
			});
			// 选中当前选项
			option.selected = true;
		},



		// 确认筛选
		confirmFilter() {
			// 获取所有选中的筛选条件
			const selectedFilters = {};
			Object.keys(this.filterOptions).forEach(category => {
				const selected = this.filterOptions[category].find(item => item.selected);
				selectedFilters[category] = selected;
			});

			console.log('选中的筛选条件:', selectedFilters);

			// 关闭弹窗
			this.closeFilter();

			// 重置数据并重新加载
			this.resetData();
			this.loadCurrentTabData(selectedFilters);

			// 显示筛选结果提示
			const filterText = Object.values(selectedFilters)
				.filter(item => item && item.id && item.id !== 0 && !['不限', '全部'].includes(item.name))
				.map(item => item.name)
				.join('、');

			if (filterText) {
				uni.showToast({
					title: `已应用筛选: ${filterText}`,
					icon: 'none',
					duration: 2000
				});
			} else {
				uni.showToast({
					title: '已应用筛选条件',
					icon: 'none'
				});
			}
		},

		// 选择随机城市
		selectRandomCity(city) {
			this.selectedCity = city.name;
			this.currentLocation = city.name;
			this.DistrictCode = city.id
			this.close();

			// 重新加载数据（保持当前筛选条件）
			this.resetData();
			this.loadCurrentTabData()
			uni.showToast({
				title: `已切换到${city.name}`,
				icon: 'none'
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.home-container {
	height: calc(100vh - 100rpx);
	background: #f8f9fa;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	/* 防止整体容器溢出 */
	position: relative;
	font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 顶部筛选标签样式 */
.filter-tabs-container {
	background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
	padding: 20rpx 0;
	border-bottom: 1rpx solid #e9ecef;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	position: relative;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 2rpx;
		background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
	}
}

.filter-tabs-wrapper {
	display: flex;
	align-items: center;
	padding: 0 30rpx;
	margin-top: 70rpx;
}

.filter-tabs {
	flex: 1;
	white-space: nowrap;
	overflow: hidden;

	/* 隐藏滚动条 */
	&::-webkit-scrollbar {
		display: none;
	}

	scrollbar-width: none;
	/* Firefox */
	-ms-overflow-style: none;
	/* IE 10+ */
}

.filter-tab-item {
	display: inline-block;
	padding: 16rpx 32rpx;
	margin-right: 20rpx;
	background: #f8f9fa;
	border-radius: 50rpx;
	font-size: 28rpx;
	color: #6c757d;
	transition: all 0.3s ease;
	border: 2rpx solid transparent;

	&.active {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #ffffff;
		transform: translateY(-2rpx);
		box-shadow: 0 8rpx 16rpx rgba(102, 126, 234, 0.3);
	}
}

.add-filter-btn {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50%;
	text-align: center;
	line-height: 60rpx;
	color: #ffffff;
	font-size: 32rpx;
	margin-left: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
	flex-shrink: 0;
	/* 防止加号按钮被压缩 */
}

/* 主要内容区域 */
.main-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

/* 内容头部区域 */
.content-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: #ffffff;
	padding: 0 30rpx;
	border-bottom: 1rpx solid #e9ecef;
}

/* 左侧内容标签样式 */
.content-tabs {
	display: flex;
}

.content-tab-item {
	padding: 30rpx 20rpx;
	font-size: 32rpx;
	color: #6c757d;
	position: relative;
	transition: all 0.3s ease;

	&.active {
		color: #667eea;
		font-weight: 600;

		&::after {
			content: '';
			position: absolute;
			bottom: 0;
			left: 50%;
			transform: translateX(-50%);
			width: 60rpx;
			height: 6rpx;
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			border-radius: 3rpx;
		}
	}
}

/* 右侧操作区域 */
.header-actions {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.location-selector {
	display: flex;
	align-items: center;
	padding: 12rpx 20rpx;
	background: #f8f9fa;
	border-radius: 30rpx;
	border: 1rpx solid #e9ecef;
}

.location-text {
	width: 80rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	font-size: 26rpx;
	color: #495057;
	margin-right: 8rpx;
}

.location-icon {
	font-size: 20rpx;
	color: #6c757d;
}

.filter-btn {
	padding: 12rpx 20rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 30rpx;
	box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.filter-icon {
	font-size: 26rpx;
	color: #ffffff;
	font-weight: 500;
}

/* 列表容器 */
.list-container {
	flex: 1;
	padding: 20rpx 30rpx;
	overflow-y: auto;
	/* 确保可以滚动 */
	box-sizing: border-box;
	/* 包含padding在内的盒模型 */
}

/* 列表项样式 */
.list-item {
	background: #ffffff;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;

	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);
	}
}

.item-content {
	padding: 30rpx;
}

/* 上部分：头像和基本信息 */
.top-section {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.avatar-container {
	margin-right: 20rpx;
}

.avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	border: 3rpx solid #f8f9fa;
}

.basic-info {
	flex: 1;
	min-width: 0;
}

.user-name {
	font-size: 36rpx;
	font-weight: 600;
	color: #212529;
	margin-bottom: 8rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.user-details {
	font-size: 26rpx;
	color: #6c757d;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;

	.divider {
		margin: 0 8rpx;
		color: #dee2e6;
	}
}

/* 中部分：技能和标签 */
.middle-section {
	margin-bottom: 20rpx;
}

.skills {
	margin-bottom: 16rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;

	.skill-label {
		font-size: 26rpx;
		color: #6c757d;
	}

	.skill-text {
		font-size: 26rpx;
		color: #667eea;
		font-weight: 500;
	}
}

.tags {
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;
}

.tag {
	padding: 8rpx 16rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	font-size: 24rpx;
	color: #6c757d;
	border: 1rpx solid #e9ecef;
}

/* 下部分：描述和按钮 */
.bottom-section {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.description {
	flex: 1;
	font-size: 26rpx;
	color: #6c757d;
	line-height: 1.5;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	margin-right: 20rpx;
}

/* 操作按钮样式 */
.action-btn {
	padding: 12rpx 24rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 30rpx;
	box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;
	flex-shrink: 0;

	&:active {
		transform: scale(0.95);
	}
}

.btn-text {
	color: #ffffff;
	font-size: 22rpx;
	font-weight: 500;
}

/* 加载状态样式 */
.load-more,
.no-more,
.no-data {
	text-align: center;
	padding: 40rpx 0;
}

.loading-text,
.no-more-text,
.no-data-text {
	font-size: 26rpx;
	color: #6c757d;
}

.no-data {
	padding: 80rpx 0;

	.no-data-text {
		color: #999;
		font-size: 28rpx;
	}
}

::v-deep .u-popup {
	flex: none;
}

/* 地区选择弹窗样式 */
.location-popup {
	// width: 90vw;
	// max-width: 800rpx;
	height: 70vh;
	// max-height: 1000rpx;
	background: #ffffff;
	border-radius: 20rpx;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}

.popup-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	border-bottom: 1rpx solid #e9ecef;
	background: #f8f9fa;
}

.popup-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #212529;
}

.close-btn {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background: #e9ecef;
}

.close-icon {
	font-size: 40rpx;
	color: #6c757d;
	line-height: 1;
}

.popup-content {
	flex: 1;
	display: flex;
	overflow: hidden;
}

/* 左侧当前位置样式 */
.left-section {
	width: 40%;
	background: #f8f9fa;
	border-right: 1rpx solid #e9ecef;
	display: flex;
	flex-direction: column;
}

.current-location-header {
	padding: 30rpx 20rpx 20rpx;
	border-bottom: 1rpx solid #e9ecef;
}

.section-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #495057;
}

.current-location-item {
	padding: 30rpx 20rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: #ffffff;
	// margin: 20rpx;
	border-radius: 12rpx;
	// box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.location-name {
	font-size: 30rpx;
	font-weight: 500;
	color: #667eea;
}

.location-icon {
	font-size: 32rpx;
}

/* 右侧城市选择样式 */
.right-section {
	flex: 1;
	display: flex;
	flex-direction: column;
	height: 100%;
}

.cities-header {
	padding: 30rpx 20rpx 20rpx;
	border-bottom: 1rpx solid #e9ecef;
	flex-shrink: 0;
	/* 防止头部被压缩 */
}

.cities-list {
	flex: 1;
	height: 0;
	/* 重要：配合flex使用，确保滚动区域有固定高度 */
	padding: 10rpx 0;
	box-sizing: border-box;
}

.city-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 25rpx 30rpx;
	border-bottom: 1rpx solid #f8f9fa;
	transition: all 0.3s ease;

	&:hover {
		background: #f8f9fa;
	}

	&.selected {
		background: #e3f2fd;
		border-left: 6rpx solid #667eea;
	}
}

.city-name {
	font-size: 28rpx;
	color: #495057;

	.city-item.selected & {
		color: #667eea;
		font-weight: 500;
	}
}

.check-icon {
	width: 40rpx;
	height: 40rpx;
	background: #667eea;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.check-text {
	color: #ffffff;
	font-size: 24rpx;
	font-weight: bold;
}

/* 筛选弹窗样式 */
.filter-popup {
	width: 100vw;
	height: 80vh;
	max-height: 1200rpx;
	background: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.popup-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background: #ffffff;
	flex-shrink: 0;
	/* 防止头部被压缩 */
}

.popup-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	text-align: center;
}

.close-btn {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.close-icon {
	font-size: 40rpx;
	color: #666666;
	line-height: 1;
}

.placeholder {
	width: 60rpx;
	height: 60rpx;
}

.filter-content {
	padding: 0 30rpx;
	box-sizing: border-box;
	overflow: auto;
}

.filter-section {
	margin-bottom: 50rpx;
}

.filter-section-title {
	padding: 30rpx 0 20rpx;
}

.section-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

.filter-options-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 30rpx;
}

.filter-option-item {
	min-width: 100rpx;
	padding: 16rpx 24rpx;
	background: #f5f5f5;
	border-radius: 8rpx;
	text-align: center;
	transition: all 0.3s ease;

	&.selected {
		background: #7dd3fc;
		color: #ffffff;
	}
}

.option-text {
	font-size: 26rpx;
	color: #666666;

	.filter-option-item.selected & {
		color: #ffffff;
	}
}

.filter-footer {
	padding: 30rpx;
	background: #ffffff;
	border-top: 1rpx solid #f0f0f0;
	flex-shrink: 0;
	/* 防止底部按钮被压缩 */
}

.confirm-btn {
	width: 100%;
	padding: 24rpx 0;
	background: #7dd3fc;
	border-radius: 8rpx;
	text-align: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
		background: #38bdf8;
	}
}

.confirm-text {
	color: #ffffff;
	font-size: 32rpx;
	font-weight: 600;
}

/* 响应式适配 */
// @media screen and (max-width: 750rpx) {
// 	.filter-tab-item {
// 		padding: 12rpx 24rpx;
// 		font-size: 26rpx;
// 	}

// 	.content-tab-item {
// 		font-size: 28rpx;
// 		padding: 24rpx 0;
// 	}

// 	.item-content {
// 		padding: 24rpx;
// 	}

// 	.user-name {
// 		font-size: 32rpx;
// 	}
// }

/* 自定义弹窗样式 */
.custom-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	animation: fadeIn 0.3s ease;
}

.custom-modal {
	width: 600rpx;
	background-color: #ffffff;
	border-radius: 24rpx;
	overflow: hidden;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
	animation: slideIn 0.3s ease;
}

.modal-header {
	padding: 40rpx 40rpx 20rpx;
	text-align: center;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-icon {
	margin-bottom: 20rpx;

	.icon-warning {
		font-size: 60rpx;
	}
}

.modal-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
	line-height: 1.4;
}

.modal-content {
	padding: 30rpx 40rpx;
	text-align: center;
}

.modal-text {
	font-size: 28rpx;
	color: #666666;
	line-height: 1.6;
}

.modal-footer {
	padding: 20rpx 40rpx 40rpx;
}

.modal-btn {
	width: 100%;
	height: 88rpx;
	border: none;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: 500;
	transition: all 0.3s ease;

	&::after {
		border: none;
	}
}

.confirm-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #ffffff;
	box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);

	&:active {
		transform: scale(0.98);
		box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
	}
}

/* 动画效果 */
@keyframes fadeIn {
	from {
		opacity: 0;
	}

	to {
		opacity: 1;
	}
}

@keyframes slideIn {
	from {
		transform: translateY(-50rpx);
		opacity: 0;
	}

	to {
		transform: translateY(0);
		opacity: 1;
	}
}
</style>