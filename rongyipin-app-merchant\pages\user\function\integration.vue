<template>
	<view class="points-center-page">
		<!-- 顶部导航占位 -->
		<!-- 在 uni-app 中，页面标题通常在 pages.json 中配置 -->
		<u-navbar :autoBack="true" title="选择工作地址" :titleStyle="{
			color: '#222',
			fontWeight: 'bold',
			fontSize: '36rpx'
		}" :leftIconSize="32" :leftIconColor="'#222'" rightText bgColor="#fff" fixed safeAreaInsetTop placeholder>
			<template #right>
				<text class="confirm-btn" @click="confirmLocation">明细</text>
			</template>
		</u-navbar>
		<!-- 我的积分 -->
		<view class="points-summary">
			<text class="title">我的积分</text>
			<view class="balance">
				<image class="coin-icon" src="/static/coin.png" mode="aspectFit"></image>
				<text class="amount">{{ points }}</text>
			</view>
		</view>

		<!-- 做任务赚积分 -->
		<view class="section-container">
			<view class="section-title">做任务赚积分</view>
			<view class="task-list">
				<view class="task-item" v-for="(task, index) in tasks" :key="index">
					<view class="task-info">
						<image class="task-icon" :src="task.icon" mode="aspectFit"></image>
						<text class="task-name">{{ task.name }}</text>
						<image class="coin-icon small" src="@/static/app/audit/goldCoins.png" mode="aspectFit"></image>
						<text class="task-points">{{ task.points }}</text>
					</view>
					<button class="task-button" :class="{ 'completed': task.status == 1 }"
						@click="handleTaskAction(task)">
						{{ task.status_text }}
					</button>
				</view>
			</view>
		</view>

		<!-- 积分充值 -->
		<view class="section-container">
			<view class="section-title">积分充值</view>
			<view class="topup-grid">
				<view class="topup-option" v-for="(option, index) in topupOptions" :key="index"
					:class="{ 'selected': selectedTopupIndex === index }" @click="selectTopup(option, index)">
					<view class="topup-points">
						<image class="coin-icon small" src="@/static/app/audit/goldCoins.png" mode="aspectFit"></image>
						<text class="amount">{{ option.score }}积分</text>
					</view>
					<text class="topup-price">{{ option.price }}元</text>
				</view>
			</view>
		</view>

		<!-- 充值方式 -->
		<view class="section-container">
			<view class="section-title">充值方式</view>
			<view class="payment-methods">
				<view class="payment-option" :class="{ 'selected': selectedPayment === 'alipay' }"
					@click="selectPayment('alipay')">
					<image class="payment-icon" src="/static/app/my/zfbpay.png" mode="aspectFit"></image>
					<text>支付宝支付</text>
				</view>
				<view class="payment-option" :class="{ 'selected': selectedPayment === 'wechat' }"
					@click="selectPayment('wechat')">
					<image class="payment-icon" src="/static/app/my/wxpay.png" mode="aspectFit"></image>
					<text>微信支付</text>
				</view>
			</view>
		</view>

		<!-- 服务协议 -->
		<view class="terms-agreement">
			充值即代表同意 <text class="link" @click="openUserAgreement">充值服务协议</text>
		</view>

		<!-- 确认充值按钮 -->
		<view class="footer">
			<button class="confirm-button" @click="confirmTopup">确认充值</button>
			<view class="customer-service">
				有问题请联系客服：11111111
			</view>
		</view>

	</view>
</template>

<script>
import { dictApi, addApi } from "@/utils/api.js"
export default {
	data() {
		return {
			// 当前积分
			points: 20,
			// 任务列表
			tasks: [],
			// 充值选项
			topupOptions: [],
			// 当前选中的充值档位索引
			selectedTopupIndex: 0,
			// 当前选中的支付方式
			selectedPayment: 'alipay',
			//选中当前的id
			selectdId: 0,
		};
	},
	async onShow() {
		const res = await dictApi.getUserInfo()
		console.log(res, '**')
		if (res.code == 200) {
			this.points = res.data.score
			console.log(res.data)
			uni.hideLoading()
		} else {
			uni.showToast({
				title: '获取数据失败',
				icon: 'none',
				duration: 2000
			});
			uni.hideLoading()
		}

		const reslist = await addApi.getPointsList()
		if (reslist.code == 200) {
			this.tasks = reslist.data
			console.log(reslist.data)
			uni.hideLoading()
		} else {
			uni.showToast({
				title: '获取数据失败',
				icon: 'none',
				duration: 2000
			});
			uni.hideLoading()
		}

		const relave = await addApi.getRechargeList()
		if (relave.code == 200) {
			this.topupOptions = relave.data
			this.selectdId = relave.data[0].id
			console.log(relave.data)
			uni.hideLoading()
		} else {
			uni.showToast({
				title: '余额充值获取数据失败',
				icon: 'none',
				duration: 2000
			});
			uni.hideLoading()
		}
	},
	methods: {
		confirmLocation() {
			uni.navigateTo({
				url: '/pages/user/function/pointsList'
			})
		},
		openUserAgreement() {
			uni.navigateTo({
				url: '/pages/user/function/recharge'
			})
		},
		// 任务按钮点击事件
		handleTaskAction(task) {
			if (task.completed) {
				console.log(`任务 [${task.name}] 已完成`);
			} else {
				console.log(`去执行任务: [${task.name}]`);
				// 这里可以写跳转到对应页面的逻辑
				// uni.navigateTo({ url: '/pages/...' });
			}
		},
		// 选择充值档位
		selectTopup(item, index) {
			console.log(index)
			this.selectedTopupIndex = index;
			this.selectdId = item.id
			console.log(`选择了充值档位: ${this.topupOptions[index].points} 积分`);
		},
		// 选择支付方式
		selectPayment(method) {
			this.selectedPayment = method;
			console.log(`选择了支付方式: ${method}`);
		},
		// 确认充值
		confirmTopup() {
			this.handleAlipayPayment()
		},
		//支付宝支付
		async handleAlipayPayment() {
			try {
				uni.showLoading({
					title: '正在调起支付...'
				});
				const params = {
					method: 'alipay',
					pay_id: this.selectdId
				}
				const paymentParams = await addApi.balanceRecharge(params);

				// 检查返回的数据结构
				if (!paymentParams || !paymentParams.data) {
					throw new Error('支付参数获取失败');
				}

				// 获取支付宝订单信息字符串 - 根据实际返回的字段名
				const orderinfo = paymentParams.data.formData
				console.log('支付宝订单信息:', orderinfo);
				if (!orderinfo) {
					throw new Error('支付宝订单信息为空');
				}
				// uni.getProvider({
				// 	service: 'payment',
				// 	success: function (res) {
				// 		console.log(res.provider);
				// 		if (res.provider == ["alipay"]) {
				// 			console.log('支持支付宝支付');
				uni.requestPayment({
					provider: 'alipay',
					orderInfo: orderinfo, // 支付宝的支付参数
					complete: (res) => {
						console.log('支付宝支付结果:', res);
						if (res.errMsg === 'requestPayment:ok') {
							// 支付成功
							console.log(res);
						} else {
							// 支付失败
							console.log(res);
						}
					}
				});

				uni.hideLoading();
				this.showPopup = false;

				uni.showToast({
					title: '支付成功',
					icon: 'success'
				});

				// this.handlePaymentSuccess();
				// }
				// },
				// fail: function (err) {
				// 	console.log('获取支付通道失败', err);
				// }
				// })

			} catch (error) {
				uni.hideLoading();
				console.error('支付宝支付失败:', error);

				if (error.errMsg && error.errMsg.includes('cancel')) {
					uni.showToast({
						title: '支付已取消',
						icon: 'none'
					});
				} else {
					uni.showToast({
						title: '支付失败',
						icon: 'none'
					});
				}
			}
		},
	}
}
</script>

<style lang="scss">
/* 全局背景色和基础字体 */
page {
	background-color: #f7f8fa;
	color: #333;
	font-size: 28rpx;
}

.points-center-page {
	padding: 30rpx;
	box-sizing: border-box;
}

/* 通用金币图标样式 */
.coin-icon {
	width: 40rpx;
	height: 40rpx;
	margin-right: 10rpx;

	&.small {
		width: 32rpx;
		height: 32rpx;
		margin: 0 4rpx;
	}
}

.confirm-btn {
	// color: #10d2c3;
	font-size: 32rpx;
	font-weight: 500;
}

/* 我的积分 */
.points-summary {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 40rpx 30rpx;
	background-color: #e0f8f0;
	border-radius: 24rpx;
	margin-bottom: 40rpx;

	.title {
		font-size: 32rpx;
		font-weight: bold;
	}

	.balance {
		display: flex;
		align-items: center;

		.amount {
			font-size: 36rpx;
			font-weight: bold;
			color: #f39c12;
		}
	}
}

/* 通用区块容器和标题 */
.section-container {
	margin-bottom: 40rpx;
}

.section-title {
	font-size: 30rpx;
	font-weight: bold;
	margin-bottom: 24rpx;
}

/* 任务列表 */
.task-list {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 0 30rpx;

	.task-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 0;

		// 非最后一个任务项，添加下边框
		&:not(:last-child) {
			border-bottom: 1rpx solid #f2f2f2;
		}

		.task-info {
			display: flex;
			align-items: center;

			.task-icon {
				width: 48rpx;
				height: 48rpx;
				margin-right: 20rpx;
			}

			.task-name {
				color: #333;
			}

			.task-points {
				color: #f39c12;
				font-weight: bold;
			}
		}

		.task-button {
			margin: 0;
			padding: 0 30rpx;
			height: 56rpx;
			line-height: 56rpx;
			font-size: 26rpx;
			color: #fff;
			background-color: #25c97c;
			border-radius: 28rpx;

			&::after {
				border: none;
			}

			// 已完成状态
			&.completed {
				background-color: #e0e0e0;
				color: #999;
			}
		}
	}
}

/* 积分充值 */
.topup-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;

	.topup-option {
		width: 48%;
		background-color: #fff;
		border-radius: 16rpx;
		padding: 24rpx;
		box-sizing: border-box;
		margin-bottom: 20rpx;
		border: 2rpx solid transparent; // 预留边框位置，防止选中时跳动
		transition: all 0.2s ease;

		.topup-points {
			display: flex;
			align-items: center;
			margin-bottom: 8rpx;

			.amount {
				font-size: 34rpx;
				font-weight: bold;
			}
		}

		.topup-price {
			font-size: 24rpx;
			color: #999;
		}

		// 选中状态
		&.selected {
			border-color: #25c97c;
			background-color: #e0f8f0;
		}
	}
}

/* 充值方式 */
.payment-methods {
	display: flex;
	gap: 20rpx; // flex间距

	.payment-option {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #fff;
		border-radius: 16rpx;
		padding: 20rpx;
		border: 2rpx solid #eee;
		transition: all 0.2s ease;

		.payment-icon {
			width: 40rpx;
			height: 40rpx;
			margin-right: 16rpx;
		}

		// 选中状态
		&.selected {
			border-color: #25c97c;
			background-color: #e0f8f0;
		}
	}
}

/* 服务协议 */
.terms-agreement {
	text-align: center;
	font-size: 24rpx;
	color: #999;
	margin-top: 40rpx;

	.link {
		color: #3c9cff;
	}
}

/* 底部确认按钮 */
.footer {
	margin-top: 20rpx;

	.confirm-button {
		height: 88rpx;
		line-height: 88rpx;
		font-size: 32rpx;
		color: #fff;
		background-color: #e54d42; // 红色按钮
		border-radius: 44rpx;
		border: none;

		&::after {
			border: none;
		}
	}

	.customer-service {
		margin-top: 30rpx;
		text-align: center;
		color: #aaa;
		font-size: 24rpx;
		padding-bottom: 20rpx; // 适配iPhone X等底部安全区域
	}
}
</style>