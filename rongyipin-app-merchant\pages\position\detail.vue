<template>
    <view class="position-container">
        <!-- 顶部导航 -->
        <view class="header">

            <u-navbar :autoBack="false" :titleStyle="{
                color: '#333',
                fontSize: '34rpx',
                fontWeight: '500'
            }" rightText="1" rightClick fixed safeAreaInsetTop placeholder bgColor="#ffffff">
                <view class="u-nav-slot" slot="left">
                    <u-icon name="arrow-left" size="30" @click="handleBack"></u-icon>
                    <!-- <u-line direction="column" :hairline="false" length="16" margin="0 8px"></u-line>
						<u-icon name="home" size="20"></u-icon> -->
                </view>
                <template #right>

                    <view class="navbar-right">
                        <view class="service" @click="handleService">
                            <view class="service-avatar">
                                <image src="/static/images/service-avatar.png" mode="aspectFill"></image>
                            </view>
                            <text>客服</text>
                        </view>
                        <view class="help" @click="handleHelp">
                            <uni-icons type="download" size="20" color="#333"></uni-icons>
                            <text>帮助</text>
                        </view>
                    </view>
                </template>
            </u-navbar>
        </view>

        <!-- 标题 -->
        <view style="display: flex;justify-content: space-between;">
            <view class="title">职位关键信息</view>
        </view>
        <view class="position-anchor">
            <view class="position-anchor-form">
                <uni-forms ref="formlate" :modelValue="formData">
                    <uni-forms-item label="职位标题" required>
                        <uni-easyinput :class="{ error: !formData.name }" v-model="formData.name" placeholder="请输入职位" />
                    </uni-forms-item>

                </uni-forms>
                <text style="color: red; font-size:24rpx;" v-if="!formData.name">*标题是必填项</text>
            </view>
            <view class="position-anchor-form">
                <uni-forms ref="formlate" :modelValue="formData">
                    <uni-forms-item label="详细地址" required>
                        <uni-easyinput :class="{ error: !formData.adress }" v-model="formData.adress"
                            placeholder="请输入职位" />
                    </uni-forms-item>

                </uni-forms>
                <text style="color: red; font-size:24rpx;" v-if="!formData.adress">*详细地址是必填项</text>
            </view>
            <view class="position-anchor-form">
                <view class="address-section">
                    <text class="label"><text class="required">*</text>工作地址</text>
                    <view class="address-picker" @click="showAddressPicker">
                        <text>{{ selectedAddress || '请选择工作地址' }}</text>
                        <u-icon name="arrow-right" color="black" size="28"></u-icon>
                    </view>
                </view>
            </view>
            <view class="tab-content" v-if="detailList.type == 1">
                <text class="label"><text class="required">*</text>全职薪资</text>
                <view class="tab-latemoney" :class="{ error: validationError || valiError }">
                    <view class="tab-moneyinterval">
                        <u--input placeholder="最小薪资" border="surround" v-model="minmoney"
                            @blur="validateInput"></u--input>
                        <p>至</p>
                        <u--input class="maxmoney" placeholder="最大薪资" border="surround" v-model="maxmoney"
                            @blur="validateInput"></u--input>
                    </view>
                    <view class="tab-settlement">
                        <view v-for="(item, index) in settlement" :key="index">
                            <p class="tab-settlement-item" :class="{ active: item.selected }"
                                @click="selectSettlement(index)">
                                {{
                                    item.name }}
                            </p>
                        </view>
                    </view>
                </view>
                <view v-if="validationError" class="error-message">请输入薪资范围</view>
                <view v-if="valiError" class="error-message">最高薪资必须大于最低薪资</view>
            </view>
            <view class="tab-content" v-if="detailList.type == 2">
                <text class="label"><text class="required">*</text>兼职薪资</text>
                <view class="tab-latemoneypart" :class="{ error: validationError || valiError }">
                    <view class="tab-moneyinterval">
                        <u--input placeholder="请输入薪资" border="surround" v-model="money"
                            @blur="validateInput"></u--input>
                        <p style="border-left: 1px solid #ccc;padding-left: 40rpx;display: flex;"
                            @click="showPicker('salary')">
                            <text>{{ salary.name }}</text>
                            <u-icon style="margin-top: 20rpx;width: 15rpx;height: 15rpx;"
                                name="arrow-down-fill"></u-icon>
                        </p>
                    </view>
                    <view class="tab-settlement">
                        <view v-for="(item, index) in billing" :key="index">
                            <p class="tab-settlement-item" :class="{ active: item.selected }"
                                @click="selectSettle(index)">{{
                                    item.name }}</p>
                        </view>
                    </view>
                </view>
                <view v-if="validationError" class="error-message">请输入薪资</view>
            </view>

            <!-- <view class="position-anchor-form">
                <uni-forms ref="formlate" :modelValue="formData">
                    <uni-forms-item label="联系方式" required>
                        <uni-easyinput :class="{ error: !formData.phone }" v-model="formData.phone"
                            placeholder="请输入职位" />
                    </uni-forms-item>

                </uni-forms>
                <text style="color: red; font-size:24rpx;" v-if="!formData.phone">*电话号码是必填项</text>
            </view> -->
            <uni-forms-item label="职位描述" required>
                <uni-easyinput :class="{ error: !formData.introduction }" type="textarea"
                    v-model="formData.introduction" placeholder="请输入工作内容与工作要求，如有需要求职者完成什么工作，对求职者的哪些要求，请详细说明。" />
            </uni-forms-item>
            <!-- <view class="position-anchor-form">
                <uni-forms-item class="position-description-phone" label="招聘人数" required>
                    <uni-easyinput :clearable="false" v-model="formData.number" placeholder="请输入联系方式"
                        :disabled="true" />

                </uni-forms-item>
                <view style="position: absolute; top: 0rpx;right: 0rpx;display: flex;align-items: center;"
                    @click="open('search')">
                    <img style="width: 30rpx;height: 30rpx;margin-top: 20rpx;padding-left: 30rpx;margin-right: 20rpx;"
                        class="img" src="@/static/app/second/modify.png" alt=""> <text
                        style="font-size: 30rpx;margin-top: 16rpx;">自定义</text>
                </view>
            </view> -->

            <view class="position-folding">
                <uni-collapse>
                    <uni-collapse-item :open="false">
                        <template #title>
                            <view class="custom-title">
                                <text class="custom-title-text">继续完善要求</text>
                                <p class="custom-title-p">+30%求职者精准度</p>
                            </view>

                        </template>
                        <view>
                            <view class="info-list">
                                <view class="info-item" @click="showPicker('age')">
                                    <text class="info-label">年龄</text>
                                    <view class="info-value">
                                        <text>{{ age.name }}</text>
                                        <u-icon name="arrow-right" size="20" color="#ccc"></u-icon>
                                    </view>
                                </view>
                                <view class="info-item" @click="showPicker('gender')">
                                    <text class="info-label">性别</text>
                                    <view class="info-value">
                                        <text>{{ gender.name }}</text>
                                        <u-icon name="arrow-right" size="20" color="#ccc"></u-icon>
                                    </view>
                                </view>
                                <view class="info-item" @click="showPicker('education')">
                                    <text class="info-label">学历</text>
                                    <view class="info-value">
                                        <text>{{ education.name }}</text>
                                        <u-icon name="arrow-right" size="20" color="#ccc"></u-icon>
                                    </view>
                                </view>
                                <view class="info-item" @click="showPicker('workTime')">
                                    <text class="info-label">工作经验 </text>
                                    <view class="info-value">
                                        <text>{{ workTime.name }}</text>
                                        <u-icon name="arrow-right" size="20" color="#ccc"></u-icon>
                                    </view>
                                </view>
                                <view v-if="detailList.type === 1" class="info-item" @click="open('recruit')">
                                    <text class="info-label">福利与提成</text>
                                    <view class="info-value">
                                        <text>{{ selectedIndexes.length || 0 }}项福利</text>
                                        <u-icon name="arrow-right" size="20" color="#ccc"></u-icon>
                                    </view>
                                </view>


                                <view v-if="detailList.type === 2" class="info-item" @click="open('period')">
                                    <text class="info-label">工作时段</text>
                                    <view class="info-value">
                                        <text
                                            style="width: 300rpx;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;"
                                            v-if="detailList.job_type == 1">
                                            <span v-if="timestart != '' && timeend != ''">{{ timestart
                                            }} ~ {{
                                                    timeend }},</span>
                                            {{
                                                workTimedata.name
                                            }},{{
                                                workPeriodOptions == 0 ? '不限' : `${workPeriodOptions.length}种工作时段`
                                            }}</text>
                                        <text
                                            style="width: 300rpx;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;"
                                            v-else>{{ workingday.name }}, {{ workTimedata.name }},{{
                                                workPeriodOptions == 0
                                                    ? '不限' : `${workPeriodOptions.length}种工作时段` }}</text>
                                        <u-icon name="arrow-right" size="20" color="#ccc"></u-icon>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </uni-collapse-item>
                </uni-collapse>

                <u-picker :show="show" :itemHeight="70" :columns="ageColumns" keyName="name" @confirm="onPickerConfirm"
                    @cancel="show = false" />
                <u-picker :show="show1" :itemHeight="60" :columns="genderColumns" keyName="name"
                    @confirm="onPickerConfirm" @cancel="show1 = false" />
                <u-picker :show="show2" :itemHeight="60" :columns="educationColumns" keyName="name"
                    @confirm="onPickerConfirm" @cancel="show2 = false" />
                <u-picker :show="show3" :itemHeight="60" :columns="workTimeColumns" keyName="name"
                    @confirm="onPickerConfirm" @cancel="show3 = false" />
                <!-- <u-picker :show="show4" :itemHeight="60" :columns="benefitsColumns" keyName="name"
                        @confirm="onPickerConfirm" @cancel="show4 = false" /> -->
                <u-picker :show="show5" :itemHeight="60" :columns="salaryColumns" keyName="name"
                    @confirm="onPickerConfirm" @cancel="show5 = false" />
            </view>

        </view>
        <view class="popup-button">
            <button class="uni-btn" @click="submit">确认</button>
        </view>
        <u-picker :show="show5" :itemHeight="60" :columns="salaryColumns" keyName="name" @confirm="onPickerConfirm"
            @cancel="show5 = false" />

        <u-popup :show="showpopup" mode="bottom" @close="close" @open="open">
            <template>
                <view class="popup-late" v-if="tablate === 0">
                    <view class="popup-title">
                        <text>
                            自定义招聘人数
                        </text>
                        <text @click="close">
                            <u-icon name="close"></u-icon>
                        </text>
                    </view>
                    <view class="popup-content">
                        招聘人数
                        <u--input type="number" :class="{ 'input-error': !recruitment }" placeholder="请输入内容"
                            v-model="recruitment"></u--input>
                    </view>
                    <view v-if="!recruitment" style="color: red;font-size: 25rpx;">*招聘人数不能为空</view>
                    <view class="popup-button">
                        <button class="uni-btn" @click="Confirmlate">确认</button>
                    </view>
                </view>
                <view class="popup-late" v-if="tablate === 1">
                    <view class="popup-title">
                        <text>
                            员工福利
                        </text>
                        <!-- <text @click="close">
                            <u-icon name="close"></u-icon>
                        </text> -->
                    </view>
                    <view
                        style="width: 100%;display: flex;flex-wrap: wrap;justify-content: space-around;align-items: center">
                        <view v-for="(item, index) in benefits" :key="item.id" class="popup-item"
                            :class="{ 'active': selectedIndexes.includes(index) }" @click="toggleBenefit(index)">
                            {{ item.name }}
                        </view>
                    </view>

                    <view class="popup-button">
                        <button class="uni-btn" @click="Confirmlatebtn">确认</button>
                    </view>
                </view>
            </template>
        </u-popup>

        <u-popup :show="partpopup" mode="bottom" @close="close" @open="open">
            <view class="tab-container">
                <view class="tab-header">
                    <view class="tab-item" :class="{ active: partTab === 0 }" @click="partchTab(0)">
                        长期兼职
                    </view>
                    <view class="tab-item" :class="{ active: partTab === 1 }" @click="partchTab(1)">

                        临时兼职
                    </view>
                    <text @click="close">
                        <u-icon name="close"></u-icon>
                    </text>
                </view>
                <view class="tab-content">
                    <view v-if="partTab === 1">
                        <!-- 临时兼职内容 -->
                        <view class="section">
                            <text class="section-title">工作日期</text>
                            <view class="section-item" :class="{ active: isDateSelected }"
                                style="color: black;width: 100%;text-align: center;height: 70rpx;line-height: 70rpx;"
                                @click="toggleDateSelection">
                                自定义日期
                            </view>
                            <view style="text-align: center;width: 100%;margin-top: 10rpx;"
                                v-if="timestart != '' && timeend != ''">{{ timestart }} ~ {{ timeend }}</view>
                        </view>
                        <view class="section">
                            <text class="section-title">工作时间</text>
                            <view class="section-item" v-for="(item, index) in workTimeOptions" :key="index"
                                :class="{ active: selectedWorkTime === index }" @click="selectWorkTime(item)">
                                {{ item.name }}
                            </view>
                        </view>
                        <view class="section">
                            <text class="section-title">工作时段 · 多选</text>
                            <view class="section-item" v-for="(item, index) in selectedWorkPeriods" :key="item.id"
                                :class="{ 'active': workPeriodOptions.includes(index) }"
                                @click="toggleWorkPeriod(index)">
                                {{ item.name }}
                            </view>
                        </view>

                        <uni-calendar ref="calendar" class="uni-calendar--hook" :clear-date="true" :date="info.date"
                            :insert="info.insert" :lunar="info.lunar" :startDate="info.startDate"
                            :endDate="info.endDate" :range="info.range" @confirm="confirm" @close="calenclose" />
                    </view>
                    <view v-else-if="partTab === 0">
                        <!-- 长期兼职内容 -->
                        <view class="section">
                            <text class="section-title">每周工作天数</text>
                            <view class="section-item" v-for="(item, index) in Workingdays" :key="index"
                                :class="{ active: selectedworkday === index }" @click="selectWorkday(item)">
                                {{ item.name }}
                            </view>
                        </view>
                        <view class="section">
                            <text class="section-title">工作时间</text>
                            <view class="section-item" v-for="(item, index) in workTimeOptions" :key="index"
                                :class="{ active: selectedWorkTime === index }" @click="selectWorkTime(item)">
                                {{ item.name }}
                            </view>
                        </view>
                        <view class="section">
                            <text class="section-title">工作时段 · 多选</text>
                            <view class="section-item" v-for="(item, index) in selectedWorkPeriods" :key="item.id"
                                :class="{ 'active': workPeriodOptions.includes(index) }"
                                @click="toggleWorkPeriod(index)">
                                {{ item.name }}
                            </view>
                        </view>
                    </view>
                </view>

                <view class="submit-btn">
                    <button class="uni-btn" @click="Confirmlatebtn">确认</button>
                </view>
            </view>

        </u-popup>
    </view>
</template>

<script>
import { positionsApi, dictApi } from '../../utils/api'
export default {
    data() {
        return {
            show: false,
            show1: false,
            show2: false,
            show3: false,
            show4: false,
            show5: false,
            detailList: {},
            formData: {
                name: '',
                introduction: '自我介绍内容',
                phone: '',
                number: '',
                adress: ''
            },
            selectedAddress: '',
            validationError: false, // 校验错误状态
            valiError: false,
            minmoney: 1000,
            maxmoney: 2000,
            settlement: [
                { name: '月结', type: 0, selected: true }, // 默认选中第一个
                { name: '面议', type: 1, selected: false }
            ],
            billing: [
                { name: '日结', type: 0, selected: true }, // 默认选中第一个
                { name: '周结', type: 1, selected: false },
                { name: '月结', type: 2, selected: false }, // 默认选中第一个
                { name: '完工结', type: 3, selected: false }
            ], // 结算方式
            money: '',
            selectedSettlementId: 0, // 保存选中的结算方式的 id
            showpopup: false,
            recruitment: '', // 招聘信息

            age: '',
            gender: '',
            education: '',
            workTime: '',
            benefits: '',
            other: '',
            salary: '', // 薪资
            ageColumns: [], // 年龄 
            genderColumns: [], // 性别
            educationColumns: [], // 学历
            workTimeColumns: [], // 工作年限
            benefitsColumns: [], // 福利与提成
            salaryColumns: [], // 薪资
            currentTab: 1,
            selectedIndexes: [], // 选中的福利
            tablate: 0,
            tyep: '', //判断全职还是兼职
            three_cityid: '',
            city: '',
            lon: '',
            lat: '',
            city: '',
            formatted_address: '',
            address_name: '',
            job_id: '',
            billing_cycle_name: '',//兼职结算类型

            workTimedata: '',
            workTimeOptions: [], //工作时间
            selectedWorkTime: 0, // 选中的工作时间索引

            workPeriodOptions: [0], //工作时段
            selectedWorkPeriods: '', // 选中的工作时段索引，

            Workingdays: [], //长期兼职 工作天数
            workingday: '',
            selectedworkday: 0,
            showCalendar: false,
            info: {
                lunar: true,
                range: true,
                insert: false,
                selected: []
            },
            timestart: '',
            timeend: '',
            isDateSelected: false,
            partpopup: false,
            partTab: 0,
        }
    },
    watch: {
        '$store.state.cationdetail'(newVal) {
            console.log(newVal, '这是选中的');
            this.selectedAddress = newVal.title;
            this.three_cityid = newVal.ad_info.adcode;
            // this.latitude = `${newVal.location.lat},${newVal.location.lng}`,
            this.lat = newVal.location.lat
            this.lon = newVal.location.lng
            this.formatted_address = newVal.address
            this.address_name = newVal.title
        },
    },
    computed: {
        canPublish() {
            if (this.selectedPosition === 11 || this.selectedPosition === 12) {
                return !!this.selectedPosition;
            }
            return !!this.selectedPosition && !!this.selectedAddress;
        },

    },
    async onLoad(options) {
        if (options) {

            uni.showLoading({
                title: "加载中",
            });
            const params = {
                job_id: options.job_id,
                type: options.type
            }
            let res = await positionsApi.postJobInfo(params)

            console.log(res, '%%')
            if (res.code == 200) {
                if (res.data.type == 1) {
                    this.detailList = res.data
                    this.formData.name = res.data.name
                    this.formData.phone = res.data.linktel
                    this.formData.number = res.data.number
                    this.formData.introduction = res.data.content
                    this.minmoney = res.data.min_salary
                    this.maxmoney = res.data.max_salary
                    this.formData.adress = res.data.address
                    this.selectedAddress = res.data.address_name
                    this.tyep = res.data.type
                    const welfare = res.data.welfare
                    console.log(welfare)
                    this.selectedIndexes = welfare.split(',').map(item => Number(item))
                    this.three_cityid = res.data.three_cityid
                    this.lon = res.data.lon
                    this.lat = res.data.lat
                    this.formatted_address = res.data.address
                    this.address_name = res.data.address_name
                    this.job_id = options.job_id
                    this.age = {
                        id: res.data.age,
                        name: res.data.age_name
                    };
                    this.gender = {
                        id: res.data.sex,
                        name: res.data.sex_name
                    };
                    this.education = {
                        id: res.data.education,
                        name: res.data.education_name
                    };
                    this.workTime = {
                        id: res.data.experience,
                        name: res.data.experience_name
                    };
                } else if (res.data.type == 2) {
                    this.detailList = res.data
                    this.formData.name = res.data.name
                    this.formData.phone = res.data.linktel
                    this.formData.number = res.data.number
                    this.formData.introduction = res.data.content
                    this.minmoney = res.data.min_salary
                    this.maxmoney = res.data.max_salary
                    this.formData.adress = res.data.address
                    this.selectedAddress = res.data.address_name
                    this.tyep = res.data.type
                    this.three_cityid = res.data.three_cityid
                    this.lon = res.data.lon
                    this.lat = res.data.lat
                    this.formatted_address = res.data.address
                    this.address_name = res.data.address_name
                    this.money = res.data.salary
                    this.job_id = options.job_id
                    this.selectedSettlementId = res.data.billing_cycle
                    this.timestart = res.data.start_date,
                        this.timeend = res.data.end_date,
                        this.workPeriodOptions = res.data.worktime.split(',').map(item => Number(item));
                    this.selectedworkday = res.data.workdate
                    this.selectedWorkTime = res.data.worktype
                    this.workingday = {
                        id: res.data.workdate,
                        name: res.data.workdate_name
                    }
                    console.log(this.workingday, '$$$')
                    this.workTimedata = {
                        id: res.data.worktype,
                        name: res.data.worktype_name
                    }
                    this.salary = {
                        id: res.data.salary_type,
                        name: res.data.salary_type_name
                    };
                    this.age = {
                        id: res.data.age,
                        name: res.data.age_name
                    };
                    this.gender = {
                        id: res.data.sex,
                        name: res.data.sex_name
                    };
                    this.education = {
                        id: res.data.education,
                        name: res.data.education_name
                    };
                    this.workTime = {
                        id: res.data.experience,
                        name: res.data.experience_name
                    };
                    this.partchTab(res.data.job_type)
                    this.selectSettle(res.data.billing_cycle)
                }


                uni.hideLoading();
            } else {
                uni.hideLoading();
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });

            }

        }

        let resdict = await dictApi.getDict();
        if (resdict.code == 200) {
            this.benefits = resdict.data.job_welfare.data;
            this.ageColumns = [resdict.data.job_age.data]
            this.genderColumns = [resdict.data.user_sex.data]
            console.log(this.genderColumns)
            this.educationColumns = [resdict.data.job_edu.data]
            this.workTimeColumns = [resdict.data.job_exp.data]
            this.benefitsColumns = [resdict.data.job_welfare.data]
            this.salaryColumns = [resdict.data.part_salary_type.data]

            this.workTimeOptions = resdict.data.part_work_type.data
            this.selectedWorkPeriods = resdict.data.part_work_time.data
            this.Workingdays = resdict.data.part_work_day.data
        }else{
            uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
        }
        uni.hideLoading();
    },
    methods: {
        selectWorkday(item) {
            this.selectedworkday = item.id;
            this.workingday = item
        },
        partchTab(index) {
            this.partTab = index;
            this.detailList.job_type = index
        },
        selectWorkTime(item) {
            this.selectedWorkTime = item.id;
            this.workTimedata = item
            console.log(this.workTimedata)
        },
        toggleWorkPeriod(index) {
            if (index === 0) {
                // 如果选中的是第一项，则清空其他选项并只选中第一项
                this.workPeriodOptions = [0];
            } else {

                // 切换当前选项的选中状态
                const firstIndex = this.workPeriodOptions.indexOf(0);
                if (firstIndex > -1) {
                    this.workPeriodOptions.splice(firstIndex, 1);
                }

                // 切换当前选项的选中状态
                const selectedIndex = this.workPeriodOptions.indexOf(index);
                if (selectedIndex > -1) {
                    // 如果已选中，则取消选中
                    this.workPeriodOptions.splice(selectedIndex, 1);
                } else {
                    // 如果未选中，则添加到选中列表
                    this.workPeriodOptions.push(index);
                }
            }

        },
        calenclose() {
            console.log(this.timestart)
            if (this.timestart != '' && this.timeend != '') {
                this.isDateSelected = true
            } else {
                this.isDateSelected = false
                console.log(111)
            }
        },
        confirm(e) {
            console.log(e)
            this.timestart = e.range.before
            this.timeend = e.range.after
        },
        toggleDateSelection() {
            // 切换高亮状态
            this.isDateSelected = !this.isDateSelected;
            console.log(this.isDateSelected)


            if (this.isDateSelected == false) {
                this.timestart = ''
                this.timeend = ''
            } else {
                this.calenopen();
            }
        },
        calenopen() {
            this.$refs.calendar.open()

        },
        Confirmlatebtn() {
            this.showpopup = false; // 关闭弹窗
            this.partpopup = false
        },
        toggleBenefit(index) {
            const selectedIndex = this.selectedIndexes.indexOf(index);
            if (selectedIndex > -1) {
                this.selectedIndexes.splice(selectedIndex, 1);
            } else {
                this.selectedIndexes.push(index);
            }
        },
        showAddressPicker() {
            uni.navigateTo({
                url: './selectedAddress'
            });
        },
        async submit() {
            if (this.tyep == 1) {
                if (this.formData.address == '' || this.formData.name == '' || this.formData.introduction == '' || this.formData.phone == '' || this.minmoney == '' || this.maxmoney == '' || this.selectedAddress == '') {
                    uni.showToast({
                        title: '请填写完整信息',
                        icon: 'none'
                    });
                    return;
                }
                console.log(this.three_cityid)
                let selectedSettlement = this.settlement.find(item => item.selected);
                let params = {
                    job_id: this.job_id,
                    type: this.tyep,
                    name: this.formData.name,
                    content: this.formData.introduction,
                    number: this.formData.number,
                    three_cityid: this.three_cityid,
                    formatted_address: this.formatted_address,
                    lon: this.lon,
                    lat: this.lat,
                    experience: this.workTime.id,
                    education: this.education.id,
                    linktel: this.formData.phone,
                    max_salary: this.maxmoney,
                    min_salary: this.minmoney,
                    is_negotiable: selectedSettlement.type,
                    welfare: this.selectedIndexes.join(','),
                    sex: this.gender.id,
                    address_name: this.address_name,
                    address: this.formData.adress,
                    age: this.age.id
                }
                let res = await positionsApi.postJobUpdate(params);
                if (res.code == 200) {
                    uni.showToast({
                        title: res.msg,
                        icon: 'none'
                    });
                    setTimeout(() => {
                        uni.switchTab({
                            url: '/pages/position/position'
                        });
                    }, 1000);
                } else {
                    uni.showToast({
                        title: res.msg,
                        icon: 'none'
                    });
                }
            } else {
                if (this.formData.address == '' || this.formData.name == '' || this.formData.introduction == '' || this.formData.phone == '' || this.money == '' || this.selectedAddress == '') {
                    uni.showToast({
                        title: '请填写完整信息',
                        icon: 'none'
                    });
                    return;
                }
                console.log(this.three_cityid, '这是区')
                let params = {
                    job_id: this.job_id,
                    type: this.tyep,
                    name: this.formData.name,
                    content: this.formData.introduction,
                    number: this.formData.number,
                    three_cityid: this.three_cityid,
                    formatted_address: this.formatted_address,
                    lon: this.lon,
                    lat: this.lat,
                    experience: this.workTime.id,
                    education: this.education.id,
                    linktel: this.formData.phone,
                    salary: this.money,
                    sex: this.gender.id,
                    address_name: this.address_name,
                    address: this.formData.adress,
                    age: this.age.id,
                    salary_type: this.salary.id,
                    billing_cycle: this.selectedSettlementId,
                    job_type: this.partTab,
                    worktype: this.workTimedata.id,
                    worktime: this.workPeriodOptions.join(','),
                }
                if (this.partTab == 0) {
                    params.workdate = this.workingday.id
                } else {
                    params.start_date = this.timestart
                    params.end_date = this.timeend
                }
                console.log(params)
                let res = await positionsApi.postJobUpdate(params);
                if (res.code == 200) {
                    uni.showToast({
                        title: res.msg,
                        icon: 'none'
                    });
                    setTimeout(() => {
                        uni.switchTab({
                            url: '/pages/position/position'
                        });
                    }, 1000);
                } else {
                    uni.showToast({
                        title: res.msg,
                        icon: 'none'
                    });
                }
            }
        },
        // close() {
        //     this.showpopup = false
        //     this.tempSelectedIndexes = [];
        //     // this.selectedIndexes = [];
        // },
        close() {
            this.showpopup = false
            this.tempSelectedIndexes = [];
            // this.selectedIndexes = [];

            this.partpopup = false
            // console.log('close');
        },
        Confirmlate() {
            if (!this.recruitment) {
                return;
            }
            this.formData.number = this.recruitment
            this.showpopup = false
        },
        open(value) {
            this.showpopup = true
            if (value === 'search') {
                this.tablate = 0
            } else if (value === 'recruit') {
                this.tablate = 1
                this.tempSelectedIndexes = [...this.selectedIndexes];
            } else if (value === 'period') {
                this.partpopup = true
            }
        },
        handleBack() {
            uni.switchTab({
                url: '/pages/position/position'
            })
        },
        handleService() {
            uni.showToast({
                title: '正在连接客服...',
                icon: 'none'
            });
        },
        handleHelp() {
            uni.showToast({
                title: '帮助中心',
                icon: 'none'
            });
        },
        showPicker(type) {
            // 根据类型设置不同的选项
            switch (type) {
                case 'age':
                    this.show = true; // 打开选择器
                    this.ageColumns = this.ageColumns;
                    break;
                case 'gender':
                    this.show1 = true; // 打开选择器
                    this.genderColumns = this.genderColumns;
                    break;
                case 'education':
                    this.show2 = true; // 打开选择器
                    this.educationColumns = this.educationColumns;
                    break;
                case 'workTime':
                    this.show3 = true; // 打开选择器
                    this.workTimeColumns = this.workTimeColumns;
                    break;
                case 'benefits':
                    this.show4 = true; // 打开选择器
                    this.benefitsColumns = this.benefitsColumns;
                    break;
                case 'salary':
                    this.show5 = true; // 打开选择器
                    this.salaryColumns = this.salaryColumns;
                    break;
            }
            this.currentPickerType = type; // 保存当前选择的类型
        },
        onPickerConfirm(value) {
            switch (this.currentPickerType) {
                case 'age':
                    this.age = value.value[0];
                    this.show = false; // 关闭选择器
                    console.log(this.age);
                    break;
                case 'gender':
                    this.gender = value.value[0];
                    console.log(this.gender);
                    this.show1 = false; // 关闭选择器
                    break;
                case 'education':
                    this.education = value.value[0];
                    this.show2 = false; // 关闭选择器
                    break;
                case 'workTime':
                    this.workTime = value.value[0];
                    this.show3 = false; // 关闭选择器
                    break;
                case 'benefits':
                    this.benefits = value.value[0];
                    this.show4 = false; // 关闭选择器
                    break;
                case 'salary':
                    this.salary = value.value[0];
                    this.show5 = false; // 关闭选择器
                    break;
            }

        },
        selectSettle(index) {
            // 更新选中状态
            this.billing.forEach((item, i) => {
                item.selected = i === index; // 仅选中当前点击的项
            });
            // 保存选中的 id
            this.selectedSettlementId = this.billing[index].type;
            console.log(this.selectedSettlementId, '###');
        },
        selectSettlement(index) {

            this.settlement.forEach((item, i) => {
                item.selected = i === index; // 仅选中当前点击的项
            });
        },
        //判断输入框是否有值
        validateInput() {
            if (this.detailList.type == 1) {
                if (!this.minmoney || !this.maxmoney) {
                    this.validationError = true; // 如果任一输入框为空，显示错误
                } else if (this.minmoney > this.maxmoney) {
                    this.validationError = false;
                    this.valiError = true;
                } else {
                    this.validationError = false; // 输入框有值时清除错误
                    this.valiError = false;
                }
            } else {
                if (!this.money) {
                    this.validationError = true; // 如果任一输入框为空，显示错误
                } else {
                    this.validationError = false; // 输入框有值时清除错误
                }
            }

        },
    },
}
</script>

<style lang="scss">
@font-face {
    font-family: CustomFont;
    src: url('../../static/app/second/sales.png');
}

.required {
    color: #f00;
    margin-right: 6rpx;
}

.position-container {
    min-height: 100vh;
    background-color: #fff;
    padding: 0 30rpx;
}

.header {
    height: 88rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .navbar-right {
        display: flex;
        align-items: center;
        height: 100%;
    }

    .avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        margin-right: 20rpx;
    }

    .help-text {
        font-size: 28rpx;
        color: #333;
    }

    .navbar-right .service,
    .navbar-right .help {
        display: flex;
        // flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-left: 40rpx;
        height: 100%;
    }

    .service-avatar {
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;
        overflow: hidden;
        margin-bottom: 4rpx;
    }

    .service-avatar image {
        width: 100%;
        height: 100%;
    }

    .navbar-right text {
        font-size: 24rpx;
        margin-top: 4rpx;
    }
}

.title {
    font-size: 60rpx;
    font-weight: bold;
    color: #333;
    margin: 30rpx 0;
    margin-top: 100rpx;
}

.titlelen {
    font-size: 36rpx;
    margin-top: 120rpx;
    color: #c3c2ca;
}

.position-anchor {
    background-color: #fff;
    border-radius: 20rpx;
    margin: 0 auto;
    margin-top: 20rpx;
    height: calc(100vh - 480rpx);
    overflow-y: auto;
    overflow-x: hidden;

    // padding: 0 20rpx;
    .position-anchor-form {
        position: relative;

        // padding: 0rpx 20rpx;
        .address-section {
            margin: 40rpx 0;

            .label {
                font-size: 28rpx;
            }

            .section-title {
                font-size: 32rpx;
                font-weight: bold;
                color: #333;
                margin-bottom: 20rpx;
            }

            .address-picker {
                height: 88rpx;
                background-color: #f8f8f8;
                border-radius: 12rpx;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0 30rpx;

                text {
                    font-size: 28rpx;
                    color: #333;
                }
            }
        }

        .position-folding {
            .custom-title {
                padding: 0 20rpx;
                display: flex;
                margin-bottom: 20rpx;

                .custom-title-text {
                    font-size: 30rpx;
                    color: black;
                }

                .custom-title-p {
                    font-size: 24rpx;
                    color: #555;
                    padding: 5rpx 20rpx;
                    background: #f7f6fb;
                    margin-left: 10rpx;
                }
            }

            .info-list {
                background-color: #fff;
                border-radius: 10rpx;
                padding: 20rpx;
            }

            .info-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20rpx 0;
                border-bottom: 1px solid #f0f0f0;
            }

            .info-item:last-child {
                border-bottom: none;
            }

            .info-label {
                font-size: 28rpx;
                color: #333;
            }

            .info-value {
                display: flex;
                align-items: center;
                font-size: 28rpx;
                color: #999;
            }

            .popup-content {
                padding: 20rpx;
                text-align: center;

            }
        }
    }

    .tab-content {
        .label {
            font-size: 28rpx;
        }
    }

    .position-description {
        padding: 0rpx 20rpx;
    }

    .position-description-phone {
        // display: flex !important;
        width: 100%;

        ::v-deep .uni-forms-item__label {
            width: 55% !important;
            color: black;
            // font-size: 24rpx;
        }

        ::v-deep .uni-forms-item__content {
            width: 35% !important;
            flex: none;
            display: flex !important;
        }

        ::v-deep .uni-easyinput__content {
            width: 150rpx !important;
        }

        ::v-deep .is-input-border {
            border: none;
        }

        ::v-deep .uni-easyinput__content-input {
            width: 20rpx !important;
            background-color: #e1f5f4;
            border: 1px solid #4bc5c9;
            border-radius: 10rpx;
            text-align: center;
            color: #333;
        }
    }

    .position-folding {
        margin-top: 20rpx;

        .custom-title {
            // padding: 0 20rpx;
            display: flex;
            margin-bottom: 20rpx;

            .custom-title-text {
                font-size: 30rpx;
                color: black;
            }

            .custom-title-p {
                font-size: 24rpx;
                color: #555;
                padding: 5rpx 20rpx;
                background: #f7f6fb;
                margin-left: 10rpx;
            }
        }

        .info-list {
            background-color: #fff;
            border-radius: 10rpx;
            padding: 20rpx;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20rpx 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-size: 28rpx;
            color: #333;
        }

        .info-value {
            display: flex;
            align-items: center;
            font-size: 28rpx;
            color: #999;
        }

        .popup-content {
            padding: 20rpx;
            text-align: center;

        }
    }

    .tab-latemoney {
        border: 1px solid #ccc;
        margin-top: 20rpx;
        width: 99%;
        border-radius: 15rpx;
        padding: 0rpx 0rpx 20rpx 0rpx;

        .tab-moneyinterval {
            display: flex;
            align-items: center;

            .maxmoney {
                margin-left: 40rpx;
            }
        }

        .tab-settlement {
            display: flex;
            width: 100%;
            margin-top: 10rpx;

            .tab-settlement-item {
                width: 100rpx;
                background-color: #f7f6fb;
                text-align: center;
                margin-left: 30rpx;
                border-radius: 10rpx;
                font-size: 28rpx;
            }

            .tab-settlement-item.active {
                background-color: #e1f5f4; // 选中背景色
                border: 1px solid #a2d8d6;
                color: #24aba7;
            }
        }
    }

    .tab-latemoney.error {
        border: 1px solid red; // 输入框边框变红
    }

    .tab-latemoneypart {
        border: 1px solid #ccc;
        margin-top: 20rpx;
        width: 100%;
        border-radius: 15rpx;
        padding: 0rpx 0rpx 20rpx 0rpx;

        .tab-moneyinterval {
            width: 90%;
            display: flex;
            align-items: center;

            .maxmoney {
                margin-left: 40rpx;
            }
        }

        .tab-settlement {
            display: flex;
            width: 100%;
            margin-top: 10rpx;

            .tab-settlement-item {
                width: 100rpx;
                background-color: #f7f6fb;
                text-align: center;
                margin-left: 30rpx;
                border-radius: 10rpx;
                font-size: 28rpx;
            }

            .tab-settlement-item.active {
                background-color: #e1f5f4; // 选中背景色
                border: 1px solid #a2d8d6;
                color: #24aba7;
            }
        }
    }

    .error-message {
        color: red;
        font-size: 24rpx;
        margin-top: 10rpx;
    }


}

.popup-button {
    width: 100%;
    padding-top: 30rpx;

    button {
        background-color: #06d1cf;
        color: white;
    }
}

::v-deep.uni-forms-item {
    display: block !important;
    margin-bottom: 0rpx !important;
}

::v-deep .uni-forms-item__label {
    width: 100% !important;
    color: black;

}

.popup-late {
    padding: 20rpx;

    .popup-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 40rpx;
        text-align: center;
        font-weight: 600;

        text {
            font-size: 30rpx;
            // color: #333;
            cursor: pointer;
        }
    }

    .popup-content {
        padding-top: 30rpx;

        .u-border {
            border: 1px solid #ccc;
            border-radius: 20rpx;
            padding: 0 20rpx;
            margin-top: 30rpx;
        }

        .input-error {
            border: 1px solid red;
        }
    }

    .popup-button {
        width: 100%;
        bottom: 40rpx;
        margin: 0 auto;
        margin-top: 30rpx;

        button {
            background-color: #06d1cf;
            color: white;
        }
    }

    .popup-item {
        width: 30%;
        height: 70rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 28rpx;
        // margin: 20rpx;
        margin-top: 20rpx;
        border-radius: 20rpx;
        background-color: #f0f1f6;
    }

    .popup-item.active {
        background-color: #e0f5f4;
        /* 选中时的背景色 */
        color: #189e98;
        /* 选中时的文字颜色 */
        border: 1px solid #a8e0dd;
        /* 选中时的边框颜色 */
    }
}

.error {
    border: 1px solid red;
}


.tab-container {
    height: 1400rpx;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .submit-btn {
        width: 100%;
        height: 200rpx;
        border-top: 1px solid #f2f2f2;

        .uni-btn {
            width: 96%;
            margin: 0 auto;
            background-color: #06d1cf;
            color: white;
            margin-top: 30rpx;
        }
    }
}

.tab-header {
    display: flex;
    justify-content: space-around;
    background-color: #f6f7f9;
    padding: 20rpx 0;
    border-bottom: 1px solid #e0e0e0;
    align-items: center;

    .tab-item {
        font-size: 32rpx;
        color: #666;
        padding: 20rpx 0;
        // flex: 1;
        text-align: center;
        cursor: pointer;
    }

    .tab-item.active {
        color: #333;
        font-weight: bold;
        border-bottom: 4rpx solid #06d1cf;
    }
}



.tab-content {
    flex: 1;
    padding: 20rpx;
}

.section {
    margin-bottom: 30rpx;
    display: flex;
    flex-wrap: wrap;
}

.section-title {
    font-size: 32rpx;
    color: black;
    margin-bottom: 10rpx;
    width: 100%;
}

.section-item {
    display: inline-block;
    // padding: 10rpx 20rpx;
    margin: 10rpx 10rpx 0 10rpx;
    font-size: 28rpx;
    color: #666;
    background-color: #f6f7f9;
    border-radius: 10rpx;
    width: 30%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 80rpx;
    color: black;
}

.section-item.active {
    color: #06d1cf;
    background-color: #e0f5f4;
    border: 1px solid #06d1cf;
}

.footer {
    padding: 20rpx;
    background-color: #fff;
    border-top: 1px solid #e0e0e0;
}
</style>