<template>
    <view class="settings-container" @click="handleContainerClick">
        <!-- 状态栏和导航栏 -->
        <view class="header">
            <u-navbar  :titleStyle="{
                color: '#333',
                fontSize: '34rpx',
                fontWeight: '500'
            }" rightText="1" fixed safeAreaInsetTop placeholder bgColor="#ffffff">
                <view class="u-nav-slot" slot="left">
                    <u-icon name="arrow-left" size="30" @click="handleBack"></u-icon>
                </view>
                <template #right>
                    <view class="navbar-right">
                        <view class="navbar-actions">
                            <view class="action-icon" @click="handleCollect(option)">
                                <image class="star-icon"
                                    :src="isCollected ? '/static/star-filled.svg' : '/static/star-empty.svg'"
                                    mode="aspectFit" />
                            </view>
                            <view class="action-icon" @click="handleShare">
                                 <image class="star-icon" src="@/static/app/home/<USER>" mode="aspectFit" ></image>
                            </view>
                        </view>
                    </view>
                </template>
            </u-navbar>
        </view>

        <!-- 职位信息 -->
        <!-- <view class="position-info" v-if="option.job_info.name">
            <text>{{ option.job_info.name }}</text>
            <uni-icons type="right" size="16"></uni-icons>
        </view> -->

        <scroll-view scroll-y class="scroll-view" :show-scrollbar="false">
            <!-- 用户基本信息 -->
            <view class="signed-user">
                <view class="user-info">
                    <view class="user-avatar">
                        <image :src="option.avatar || '/static/demo/avatar.jpg'" mode="aspectFill"></image>
                    </view>
                    <view class="user-details">
                        <view class="user-name"><text style="font-size: 50rpx;">{{ option.username
                                }}</text><text style="font-size: 30rpx;margin-left: 20rpx;">{{ option.job_info.name
                                }}</text></view>
                        <view class="user-tags">
                            <text class="tag age" v-if="option.age">{{ option.age }}岁</text>
                            <text class="tag experience" v-if="option.work_year">{{ option.work_year }}年</text>
                            <text class="tag education" v-if="option.degree_name">{{ option.degree_name }}</text>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 求职意向 -->
            <view class="section-block job-intention">
                <view class="section-title">求职意向</view>
                <view class="job-intention-content">

                    <view style="display: flex;">
                        <view class="job-type">
                            <text class="job-type-text" v-if="option.job_info.type_text">{{ option.job_info.type_text
                                }}</text>
                        </view>
                        <view class="job-location" style="margin-left: 10rpx;">
                            <text class="location-text" v-if="option.resume_expect.job_classname">{{
                                option.resume_expect.job_classname || '' }}</text>
                        </view>
                        <view class="job-location" style="margin-left: 10rpx;">
                            <text class="location-text" v-if="option.resume_expect.city_name">{{
                                option.resume_expect.city_name
                                || '' }}</text>
                        </view>
                    </view>
                    <view class="job-salary">
                        <text class="salary-text" v-if="option.resume_expect">{{ option.resume_expect.minsalary }} - {{
                            option.resume_expect.maxsalary }}</text>
                    </view>
                </view>
            </view>
            <view class="section-block" v-if="option.resume_expect.report_name">
                <view class="section-title">求职状态</view>
                <view class="job-intention-content" style="font-size: 26rpx;color: #666;line-height: 1.4;">
                    {{ option.resume_expect.report_name }}
                </view>
            </view>
            <view class="section-block" v-if="option.skill_advantage">
                <view class="section-title">个人优势</view>
                <view class="job-intention-content">
                    <text style="font-size: 26rpx;color: #666;line-height: 1.4;">{{ option.skill_advantage }}</text>

                </view>
            </view>

            <!-- 工作经历 -->
            <view class="section-block education" v-if="option.resume_work && option.resume_work.length > 0">
                <view class="section-title">工作经历</view>
                <view class="education-item" v-for="(item, index) in option.resume_work" :key="index">
                    <text class="education-date">{{ item.sdate_format }} - {{ item.edate_format }}</text>
                    <text class="education-degree">{{ item.name }} . {{ item.title }}</text>
                    <view style="font-size: 26rpx;color: #666;line-height: 1.4;">
                        {{ item.content }}
                    </view>
                </view>
            </view>
            <!-- 项目经历 -->
            <view class="section-block education" v-if="option.resume_project && option.resume_project.length > 0">
                <view class="section-title">项目经历</view>
                <view class="education-item" v-for="(item, index) in option.resume_project" :key="index">
                    <text class="education-date">{{ item.sdate_format }} - {{ item.edate_format }}</text>
                    <text class="education-degree">{{ item.name }} . {{ item.title }}</text>
                    <view style="font-size: 26rpx;color: #666;line-height: 1.4;">
                        {{ item.content }}
                    </view>
                </view>
            </view>
            <!-- 教育经历 -->
            <view class="section-block education" v-if="option.resume_edu && option.resume_edu.length > 0">
                <view class="section-title">教育经历</view>
                <view class="education-item" v-for="(item, index) in option.resume_edu" :key="index">
                    <text class="education-date">{{ item.sdate_format }} - {{ item.edate_format }}</text>
                    <text class="education-degree">{{ item.name }}</text>
                </view>
            </view>

            <!-- 技能证书 -->
            <view class="section-block skills" v-if="option.certificate_label_text && option.certificate_label_text.length > 0">
                <view class="section-title">技能证书</view>
                <view class="skill-tags">
                    <text v-for="(item, index) in option.certificate_label_text" :key="index" class="skill-tag">{{ item
                        }}</text>
                </view>
            </view>

            <!-- 底部状态 -->
            <view class="bottom-status">
                <text>已经看完了，快去处理吧～</text>
            </view>

            <!-- 底部占位，防止内容被底部按钮遮挡 -->
            <view style="height: 200rpx;"></view>
        </scroll-view>


    </view>
</template>

<script>
import { applyApi, home } from "@/utils/api"
export default {
    data() {
        return {
            option: {
                job_info: {},
                resume_expect: {},
                resume_work: [],
                resume_project: [],
                resume_edu: []
            },
            showMarkPopup: false,
            markStatus: '',
            optionitem: {},
            isCollected: false // 收藏状态
        }
    },
    async onLoad(options) {
        uni.showLoading()
        if (options) {
            try {
                let res = await applyApi.getTalent({
                    seeker_id: options.seeker_id,
                    job_id: options.job_id
                });
                if (res.code == 200) {
                    console.log(res.data, '^^^')
                    this.option = Object.assign({}, this.option, res.data)
                    this.isCollected = this.option.is_collect == 1 ? true : false
                    uni.hideLoading()
                } else {
                    uni.showToast({
                        title: res.msg,
                        icon: 'none'
                    });
                }
            } catch (e) {
                console.error('解析参数失败', e);
            }
        }
    },
    methods: {
        formatTimestamp(timestamp) {
            const date = new Date(timestamp * 1000); // 将秒转换为毫秒
            const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需要加 1
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            return `${month}/${day} ${hours}:${minutes}`; // 返回格式化的字符串
        },
        handleBack() {
            uni.navigateBack();
        },
        toggleMarkPopup() {
            console.log('点击标记按钮，当前状态:', this.showMarkPopup);
            this.showMarkPopup = !this.showMarkPopup;
            console.log('切换后状态:', this.showMarkPopup);
        },
        hideMarkPopup() {
            this.showMarkPopup = false;
        },
        async selectMarkStatus(status) {
            this.markStatus = status;

            this.markStatus = status;

            // 根据选择的状态打印不同的信息
            const statusMap = {
                'contacted_suitable': { text: '已联系-合适', id: 2 },
                'contacted_not_suitable': { text: '已联系-不合适', id: 3 },
                'not_contacted': { text: '未联系', id: 0 },
                'pending_reply': { text: '已联系', id: 1 }
            };

            const selectedStatus = statusMap[status];
            console.log('选择的标记状态:', selectedStatus.text, '对应的ID:', selectedStatus.id);
            let res = await applyApi.changeApply({ seeker_id: this.optionitem.seeker_id, status: selectedStatus.id })
            if (res.code == 200) {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });

                const paramCount = Object.keys(this.optionitem).length;

                if (paramCount === 1) {
                    let res = await applyApi.getTalent({
                        seeker_id: this.optionitem.seeker_id
                    });
                    if (res.code == 200) {
                        this.option = Object.assign({}, this.option, res.data)
                        const statusMap = {
                            0: 'not_contacted',
                            1: 'pending_reply',
                            2: 'contacted_suitable',
                            3: 'contacted_not_suitable'
                        };
                        this.markStatus = statusMap[this.option.job_info.status] || 'not_contacted';
                        uni.hideLoading()
                    } else {
                        uni.showToast({
                            title: res.msg,
                            icon: 'none'
                        });
                    }
                }
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }

            // 关闭弹窗
            this.showMarkPopup = false;
        },
        handleContainerClick() {
            // 点击容器时关闭弹窗
            console.log('点击容器，弹窗状态:', this.showMarkPopup);
            if (this.showMarkPopup) {
                this.showMarkPopup = false;
                console.log('关闭弹窗');
            }
        },
        handleCallClick() {
            // 打电话按钮的点击事件
            console.log('点击打电话按钮');
            // 这里添加您的打电话逻辑
            // this.hsdahd();
        },
        handleMessageClick() {
            // 发消息按钮的点击事件
            console.log('点击发消息按钮');
            // 这里添加您的发消息逻辑
        },
        handleWechatClick() {
            // 加微信按钮的点击事件
            console.log('点击加微信按钮');
            // 这里添加您的加微信逻辑
        },
        // 处理收藏
        async handleCollect() {
            this.isCollected = !this.isCollected;
            if (this.isCollected) {
                const params = {
                    seeker_id: this.option.id,
                    job_id: this.option.job_info.job_id,
                    type: 1
                }
                await home.colletTalent(params)
            } else {
                const params = {
                    seeker_id: this.option.id,
                    job_id: this.option.job_info.job_id,
                    type: 2
                }
                await home.colletTalent(params)
            }
            uni.showToast({
                title: this.isCollected ? '已收藏' : '已取消收藏',
                icon: 'none',
                duration: 1500
            });
            // 这里可以添加收藏的API调用
            console.log('收藏状态:', this.isCollected);
        },
        // 处理分享
        handleShare() {
            uni.showActionSheet({
                itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
                success: (res) => {
                    console.log('选中了第' + (res.tapIndex + 1) + '个按钮');
                    switch (res.tapIndex) {
                        case 0:
                            this.shareToWechat();
                            break;
                        case 1:
                            this.shareToMoments();
                            break;
                        case 2:
                            this.copyLink();
                            break;
                    }
                },
                fail: (res) => {
                    console.log(res.errMsg);
                }
            });
        },
        // 分享到微信
        shareToWechat() {
            uni.showToast({
                title: '分享到微信',
                icon: 'none'
            });
            // 这里添加分享到微信的逻辑
        },
        // 分享到朋友圈
        shareToMoments() {
            uni.showToast({
                title: '分享到朋友圈',
                icon: 'none'
            });
            // 这里添加分享到朋友圈的逻辑
        },
        // 复制链接
        copyLink() {
            uni.setClipboardData({
                data: window.location.href,
                success: () => {
                    uni.showToast({
                        title: '链接已复制',
                        icon: 'none'
                    });
                }
            });
        }
    }
}
</script>
<style lang="scss" scoped>
.settings-container {
    min-height: 100vh;
    background-color: #f0f1f6;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow-x: hidden;
    box-sizing: border-box;
}

// .header {
//     height: 88rpx;
//     background-color: #f0f1f6;
//     width: 100%;
// }

.navbar-actions {
    display: flex;
    align-items: center;
    gap: 20rpx;
    padding-right: 10rpx;
}

.action-icon {
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;

    &:active {
        transform: scale(0.9);
        background-color: rgba(255, 255, 255, 0.2);
    }
}

.position-info {
    display: flex;
    align-items: center;
    padding: 20rpx 30rpx;
    font-size: 28rpx;
    color: #666;
    width: 100%;
    box-sizing: border-box;

    text {
        margin-right: 10rpx;
    }
}

.scroll-view {
    flex: 1;
    height: calc(100vh - 88rpx - 200rpx);
    width: 100%;
    box-sizing: border-box;
    padding: 0 15rpx;
    overflow-x: hidden;
}

.signed-user {
    background-color: white;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    width: 100%;
    box-sizing: border-box;

    .user-info {
        display: flex;
        align-items: center;
        gap: 20rpx;
    }

    .user-avatar {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        overflow: hidden;
        flex-shrink: 0;
        background: linear-gradient(135deg, #FFE066 0%, #FF6B6B 50%, #4ECDC4 100%);
        display: flex;
        align-items: center;
        justify-content: center;

        image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    .user-details {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 12rpx;
    }

    .user-name {
        font-size: 36rpx;
        font-weight: 600;
        color: #333;
        line-height: 1.2;
    }

    .user-tags {
        display: flex;
        gap: 12rpx;
        flex-wrap: wrap;
    }

    .tag {
        background-color: #f5f5f5;
        color: #666;
        font-size: 24rpx;
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        line-height: 1;

        &.age {
            background-color: #e8f4fd;
            color: #1890ff;
        }

        &.experience {
            background-color: #f6ffed;
            color: #52c41a;
        }

        &.education {
            background-color: #fff2e8;
            color: #fa8c16;
        }
    }
}

.user-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20rpx;
    margin-bottom: 30rpx;
    width: 100%;
    box-sizing: border-box;

    .info-item {
        background-color: #f8f9fa;
        border-radius: 16rpx;
        padding: 20rpx;
        text-align: center;

        .info-label {
            font-size: 26rpx;
            color: #999;
            display: block;
            margin-bottom: 10rpx;
        }

        .info-value {
            font-size: 30rpx;
            color: #333;
            font-weight: 500;
        }
    }
}

.user-photos {
    display: flex;
    margin-bottom: 30rpx;
    width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;

    image {
        width: 160rpx;
        height: 200rpx;
        border-radius: 12rpx;
        margin-right: 20rpx;
        object-fit: cover;
        flex-shrink: 0;
    }
}

.remark-section {
    width: 100%;
    box-sizing: border-box;

    .remark-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10rpx;
        width: 100%;

        text {
            font-size: 28rpx;
            color: #333;
        }

        .remark-date {
            font-size: 24rpx;
            color: #999;
        }
    }
}

.section-block {
    background-color: white;
    // border-radius: 20rpx;
    padding: 30rpx;
    // margin-bottom: 20rpx;
    width: 100%;
    box-sizing: border-box;
}

.section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    position: relative;
    padding-left: 20rpx;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 8rpx;
        height: 8rpx;
        background-color: #52c41a;
        border-radius: 50%;
    }
}

.job-intention-content {
    display: flex;
    // flex-direction: column;
    gap: 16rpx;
    justify-content: space-between;
}

.job-type {
    .job-type-text {
        font-size: 28rpx;
        color: #333;
        line-height: 1.5;
        font-weight: 400;
    }
}

.job-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 8rpx;
}

.job-location {
    // flex: 1;

    .location-text {
        font-size: 26rpx;
        color: #666;
        line-height: 1.4;
    }
}

.job-salary {
    .salary-text {
        font-size: 26rpx;
        color: #1890ff;
        font-weight: 500;
        line-height: 1.4;
    }
}

.education-item {
    width: 100%;

    .education-date {
        font-size: 26rpx;
        color: #999;
        display: block;
        margin-bottom: 10rpx;
    }

    .education-degree {
        font-size: 30rpx;
        color: #333;
    }
}

.skill-tags {
    display: flex;
    flex-wrap: wrap;
    width: 100%;

    .skill-tag {
        background-color: #f0f1f6;
        color: #666;
        font-size: 26rpx;
        padding: 10rpx 20rpx;
        border-radius: 30rpx;
        margin-right: 20rpx;
        margin-bottom: 20rpx;
    }
}

.bottom-status {
    text-align: center;
    color: #999;
    font-size: 26rpx;
    margin: 30rpx 0;
    width: 100%;
}

// 标记弹窗样式
.mark-popup-container {
    position: fixed;
    bottom: 280rpx;
    left: 30rpx;
    z-index: 1000;
    width: 320rpx;
}

.mark-popup {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    border-radius: 16rpx;
    padding: 30rpx 25rpx;
    box-sizing: border-box;
    box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.3);
    border: 1rpx solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.popup-arrow {
    position: absolute;
    bottom: -12rpx;
    left: 50rpx;
    width: 0;
    height: 0;
    border-left: 12rpx solid transparent;
    border-right: 12rpx solid transparent;
    border-top: 12rpx solid #4a5568;
    z-index: 1001;

    &::before {
        content: '';
        position: absolute;
        bottom: 1rpx;
        left: -12rpx;
        width: 0;
        height: 0;
        border-left: 12rpx solid transparent;
        border-right: 12rpx solid transparent;
        border-top: 12rpx solid rgba(255, 255, 255, 0.1);
    }
}

.popup-title {
    font-size: 28rpx;
    color: #ffffff;
    text-align: center;
    margin-bottom: 35rpx;
    font-weight: 500;
    line-height: 1.3;
    letter-spacing: 0.5rpx;
}

.popup-options {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.popup-option {
    display: flex;
    align-items: center;
    padding: 12rpx 8rpx;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 8rpx;

    &:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }

    &:active {
        opacity: 0.8;
        transform: scale(0.98);
    }
}

.option-radio {
    width: 34rpx;
    height: 34rpx;
    border: 2rpx solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;
    flex-shrink: 0;
    background-color: transparent;
    transition: border-color 0.2s ease;
}

.radio-dot {
    width: 18rpx;
    height: 18rpx;
    border-radius: 50%;
    background-color: transparent;
    transition: all 0.2s ease;

    &.checked {
        background-color: #00d4aa;
        box-shadow: 0 0 8rpx rgba(0, 212, 170, 0.3);
    }
}

.option-text {
    font-size: 28rpx;
    color: #ffffff;
    flex: 1;
    font-weight: 400;
    line-height: 1.3;
    letter-spacing: 0.3rpx;
}

.mark-button {
    cursor: pointer;
    user-select: none;

    &:active {
        opacity: 0.7;
    }
}

.action-buttons {
    display: flex;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    padding: 20rpx 30rpx;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    height: 200rpx;
    box-sizing: border-box;
    width: 100%;

    .action-btn {
        flex: 1;
        height: 80rpx;
        border-radius: 10rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        margin: 0 10rpx;
        border: none !important;
        transition: opacity 0.2s ease;

        &.call-btn {
            background-color: #f0f1f6;
            color: #333;
        }

        &.message-btn {
            background-color: #f0f1f6;
            color: #333;
        }

        &.wechat-btn {
            background-color: #00c9c2;
            color: white;
        }

        // 禁用状态样式
        &.u-button--disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    }
}

::v-deep .u-navbar__content {
    background-color: #f0f1f6 !important;
}

::v-deep .uni-button:after {
    border: 1px solid red !important;
}
</style>