<template>
    <view class="attachment-resume-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" :title=title :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
                <template #right>
                    <u-icon name="more-dot-fill" size="20" color="#333"></u-icon>
                </template>
            </u-navbar>
        </view>
        <!-- 页面Loading -->
        <view class="loading" v-if="loding">
            <view class="loading-content">
                <view class="loading-spinner">
                    <u-loading-icon mode="spinner" size="50" color="#14B19E"></u-loading-icon>
                </view>
                <text class="loading-title">简历加载中</text>
                <text class="loading-subtitle">请稍候...</text>
            </view>
        </view>
        <!-- 内容区域 -->
       <scroll-view scroll-y="true" class="scroll-content" v-else>
            <!-- 渲染简历的每一页图片 -->
            <image
                v-for="(pageUrl, index) in resumePages"
                :key="index"
                :src="pageUrl"
                mode="widthFix"
                class="resume-page-image"
            ></image>
        </scroll-view>


    </view>
</template>

<script>
import { resume } from "@/utils/api.js"
export default {
    data() {
        return {
            resumePages: [], // 简历列表
            allUrl: '',
            pdfUrl: '',
            title: '',
            loding: true,
            tile:''
        }
    },
    async onLoad(options) {
        console.log(options)
        try {
           console.log(options)
            const res = await resume.ReumeAttachmentInfo({
                id: options.id
            })
            console.log('res:', res)
            if(res.code == 200){
                this.resumePages = res.data.image_path.split(',')
                this.title =res.data.attachment_name
                setTimeout(() => {
                    this.loding = false
                }, 3000)
            }
            else{
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                })

            }
            uni.hideLoading();
        } catch (e) {
            console.error('参数解析失败:', e)
        }

    },

    methods: {
    }
}
</script>

<style lang="scss" scoped>
.attachment-resume-page {
    min-height: 100vh;
    background-color: #f8f8f8;
    display: flex;
    flex-direction: column;
}

/* Loading样式 */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px;
    // background-color: rgba(255, 255, 255, 0.35);
}

.loading-spinner {
    margin-bottom: 20px;
}

.loading-title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
}

.loading-subtitle {
    font-size: 14px;
    color: #666;
    margin-bottom: 25px;
}

.loading-progress {
    width: 200px;
    height: 6px;
    background-color: rgba(229, 229, 229, 0.5);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #14B19E 0%, #00d4aa 100%);
    border-radius: 3px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.4) 50%, transparent 100%);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.loading-percent {
    font-size: 16px;
    font-weight: bold;
    color: #14B19E;
}

/* 顶部导航栏 */
.navbar {
}
.scroll-content{
    display: flex;
    justify-content: center;
}
.scroll-content {
    flex: 1;
    background-color: #f5f5f5;
}
.resume-page-image {
    width: 100%;
    display: block; /* 消除图片底部间隙 */
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.bottom-actions {
    /* 底部操作栏样式 */
    display: flex;
    height: 60px;
    padding: 10px;
    background-color: #fff;
    border-top: 1px solid #eee;
}
/* 响应式调整 */
@media screen and (max-width: 750rpx) {
    .loading-content {
        min-width: 250px;
        padding: 30px;
    }

    .loading-progress {
        width: 180px;
    }

    .loading-title {
        font-size: 18px;
    }
}
</style>
