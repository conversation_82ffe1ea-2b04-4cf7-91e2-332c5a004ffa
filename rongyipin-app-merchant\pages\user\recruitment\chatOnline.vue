<template>
	<view class="refresh-page-container">
		<!-- 1. 自定义导航栏 -->
		<view class="nav-bar">
			<u-navbar height="44px" title="在线畅聊" :autoBack="true" :leftIconSize="30" :leftIconColor="'#333'"
				safeAreaInsetTop placeholder fixed>
			</u-navbar>
		</view>

		<!-- 2. 主体内容区域 -->
		<view class="content-wrapper">
			<!-- 顶部图片占位符 -->
			<view class="ad-placeholder">
				<u-icon name="photo" color="#c0c4cc" size="64"></u-icon>
			</view>

			<!-- 刷新卡类型 -->
			<view class="section-container">
				<text class="section-title">畅聊卡类型</text>
				<view class="card-list">
					<view v-for="item in cardOptions" :key="item.id" class="card-item"
						:class="{ active: selectedCardId === item.id }" @click="selectCard(item)">
						<!-- 左侧的单选图标和卡片名称 -->
						<view class="card-info">
							<uni-icons class="radio-icon" :type="selectedCardId === item.id ? 'checkbox' : 'circle'"
								:color="selectedCardId === item.id ? '#2979ff' : '#dcdfe6'" size="30"></uni-icons>
							<text class="card-name">{{ item.name }}</text>
						</view>

						<!-- 中间的刷新次数 -->
						<text class="card-count">{{ item.num }}次</text>

						<!-- 右侧的价格 -->
						<text class="card-price" v-if="item">{{ item.price }}积分 / {{ item.points }}元</text>
					</view>
				</view>
			</view>

			<!-- 购买须知 -->
			<view class="section-container">
				<text class="section-title">购买须知</text>
				<view class="notice-list">
					<text class="notice-item">1、xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx;</text>
					<text class="notice-item">2、xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx;</text>
					<text class="notice-item">3、xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx;</text>
					<text class="notice-item">4、xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx;</text>
				</view>
			</view>
		</view>

		<!-- 3. 底部支付栏 -->
		<view class="bottom-bar">
			<view class="price-details">
				<text class="price-main" v-if="selectedCard && selectedCard.price">
					{{ selectedCard.points }}积分 / {{ selectedCard.price }}元
				</text>
				<text class="price-main" v-else>请选择卡片</text>
				<text class="price-sub">购买后立即生效</text>
			</view>
			<button class="pay-button" :disabled="!selectedCard" @click="showPopup = true">立即支付</button>
		</view>

		<u-popup :show="showPopup" @close="close" @open="open">
			<view class="popup-content">
				<!-- 1. 弹窗标题 (u-popup自带关闭按钮，我们只需标题) -->
				<view class="popup-header">
					<text class="popup-title">支付金额</text>
				</view>

				<!-- 2. 支付金额 -->
				<view class="price-section">
					<text class="currency-symbol">¥</text>
					<text class="amount-text">{{ paymentAmount }}</text>
				</view>

				<!-- 3. 支付方式列表 -->
				<view class="payment-list">
					<view class="payment-item" v-for="item in paymentOptions" :key="item.id"
						@click="selectPayment(item.id)">
						<image src="/static/app/my/wxpay.png" class="payment-icon"></image>

						<view class="payment-info">
							<text class="payment-name">{{ item.name }}</text>
							<!-- 按要求只在积分支付时显示额外信息 -->
							<text v-if="item.id === 'points'" class="points-balance">
								剩余 {{ userPoints }} 积分
							</text>
						</view>

						<!-- 选中状态 -->
						<uni-icons v-if="selectedPayment === item.id" type="checkbox" size="30"
							color="#07c160"></uni-icons>
						<uni-icons v-else type="circle" size="30" color="#e0e0e0"></uni-icons>
					</view>
				</view>

				<!-- 4. 确认支付按钮 -->
				<view class="popup-footer">
					<button class="confirm-btn" @click="handleConfirmPayment">
						确认支付 ¥{{ paymentAmount }}
					</button>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import { addApi } from "@/utils/api"
export default {
	data() {
		return {
			// 卡片选项数据
			cardOptions: [
				// {
				// 	id: 'week',
				// 	name: '在线畅聊周卡',
				// 	count: 7,
				// 	price: { points: 100, yuan: 10 }
				// },
				// {
				// 	id: 'month',
				// 	name: '在线畅聊月卡',
				// 	count: 50,
				// 	price: { points: 500, yuan: 50 }
				// },
				// {
				// 	id: 'deluxe',
				// 	name: '畅聊豪华月卡',
				// 	count: 100,
				// 	price: { points: 900, yuan: 90 }
				// }
			],
			paymentAmount: 9.9, // 支付金额
			userPoints: 5200, // 用户剩余积分示例
			selectedPayment: 'alipay', // 默认选中的支付方式
			paymentOptions: [
				{
					id: 'alipay',
					name: '支付宝支付',
					imageUrl: '/static/app/my/zfbpay.png',
				},
				{
					id: 'wechat',
					name: '微信支付',
					imageUrl: '/static/app/my/wxbpay.png'
				},
				{
					id: 'points',
					name: '积分支付',
					imageUrl: '/static/app/my/integralpay.png'
				}
			],
			// 当前选中的卡片ID，默认为第一个
			selectedCardlate: {},
			selectedCardId: null,
			showPopup: false
		};
	},
	computed: {
		// 计算属性：根据selectedCardId找到完整的选中卡片对象
		// 这样在模板中可以直接使用selectedCard，代码更清晰
		selectedCard() {
			return this.cardOptions.find(card => card.id === this.selectedCardId) || null;
		}
	},
	onLoad() {
		// #ifdef APP-PLUS
		// 关键：在每次调用支付前，都强制设置一次沙箱环境
		var EnvUtils = plus.android.importClass('com.alipay.sdk.app.EnvUtils');
		EnvUtils.setEnv(EnvUtils.EnvEnum.SANDBOX);
		// #endif
	},
	async onShow() {
		const paymentParams = await addApi.getOnlineChatPackage();
		if (paymentParams.code == 200) {
			this.cardOptions = paymentParams.data.list
			console.log(this.cardOptions)

			// 默认选择第一个卡片
			if (this.cardOptions && this.cardOptions.length > 0) {
				this.selectedCardId = this.cardOptions[0].id;
				console.log('默认选择第一个卡片:', this.cardOptions[0]);
				this.selectedCardlate = this.cardOptions[0]
			}

			uni.hideLoading()
		} else {
			uni.showToast({
				title: paymentParams.msg,
				icon: 'none'
			})
		}
	},
	methods: {
		selectPayment(id) {
			this.selectedPayment = id;
		},
		// 点击最终的确认支付按钮
		async handleConfirmPayment() {
			console.log(`准备支付 ${this.paymentAmount} 元`);
			console.log(`选择的支付方式: ${this.selectedPayment}`);

			// 根据选择的支付方式进行不同处理
			if (this.selectedPayment === 'points') {
				// 积分支付
				this.handlePointsPayment();
			} else if (this.selectedPayment === 'wechat') {
				// 微信支付
				await this.handleWechatPayment();
			} else if (this.selectedPayment === 'alipay') {
				// 支付宝支付
				await this.handleAlipayPayment();
			}
		},

		// 积分支付处理
		handlePointsPayment() {
			const requiredPoints = this.paymentAmount * 100; // 假设1元=100积分

			if (this.userPoints < requiredPoints) {
				uni.showToast({
					title: '积分不足',
					icon: 'none'
				});
				return;
			}

			uni.showLoading({
				title: '正在处理...'
			});

			// 模拟积分支付请求
			setTimeout(() => {
				uni.hideLoading();
				this.showPopup = false;

				// 更新用户积分
				this.userPoints -= requiredPoints;

				uni.showToast({
					title: '支付成功',
					icon: 'success'
				});

				this.handlePaymentSuccess();
			}, 1500);
		},

		// 微信支付处理
		async handleWechatPayment() {
			try {
				uni.showLoading({
					title: '正在调起支付...'
				});

				// 1. 先调用后端接口获取支付参数

				// 2. 调起微信支付
				const result = await uni.requestPayment({
					provider: 'wxpay',
					orderInfo: {
						appid: paymentParams.appid,           // 微信开放平台审核通过的应用APPID
						partnerid: paymentParams.partnerid,   // 微信支付分配的商户号
						prepayid: paymentParams.prepayid,     // 预支付交易会话ID
						package: 'Sign=WXPay',                // 固定值
						noncestr: paymentParams.noncestr,     // 随机字符串
						timestamp: paymentParams.timestamp,   // 时间戳
						sign: paymentParams.sign              // 签名
					}
				});

				uni.hideLoading();
				this.showPopup = false;

				uni.showToast({
					title: '支付成功',
					icon: 'success'
				});

				this.handlePaymentSuccess();

			} catch (error) {
				uni.hideLoading();
				console.error('微信支付失败:', error);

				if (error.errMsg && error.errMsg.includes('cancel')) {
					uni.showToast({
						title: '支付已取消',
						icon: 'none'
					});
				} else {
					uni.showToast({
						title: '支付失败',
						icon: 'none'
					});
				}
			}
		},

		// 支付宝支付处理
		async handleAlipayPayment() {
			try {
				uni.showLoading({
					title: '正在调起支付...'
				});
				const params = {
					method: 'alipay',
					product_id: this.selectedCardlate.id
				}
				const paymentParams = await addApi.productBuy(params);

				// 检查返回的数据结构
				if (!paymentParams || !paymentParams.data) {
					throw new Error('支付参数获取失败');
				}

				// 获取支付宝订单信息字符串 - 根据实际返回的字段名
				const orderinfo = paymentParams.data.formData
				console.log('支付宝订单信息:', orderinfo);
				if (!orderinfo) {
					throw new Error('支付宝订单信息为空');
				}

				// 2. 调起支付宝支付
				uni.requestPayment({
					provider: 'alipay',
					orderInfo: orderinfo, // 支付宝的支付参数
					complete: (res) => {
						console.log('支付宝支付结果:', res);
						if (res.errMsg === 'requestPayment:ok') {
							// 支付成功
							console.log(res);
						} else {
							// 支付失败
							console.log(res);
						}
					}
				});

				uni.hideLoading();
				this.showPopup = false;

				uni.showToast({
					title: '支付成功',
					icon: 'success'
				});

				this.handlePaymentSuccess();

			} catch (error) {
				uni.hideLoading();
				console.error('支付宝支付失败:', error);

				if (error.errMsg && error.errMsg.includes('cancel')) {
					uni.showToast({
						title: '支付已取消',
						icon: 'none'
					});
				} else {
					uni.showToast({
						title: '支付失败',
						icon: 'none'
					});
				}
			}
		},

		// 获取微信App支付参数（调用后端接口）
		async getWechatPaymentParams() {
			// 这里应该调用你的后端接口获取微信App支付参数
			// 示例代码：
			/*
			const response = await uni.request({
				url: 'https://your-api.com/payment/wechat/app/prepare',
				method: 'POST',
				data: {
					amount: this.paymentAmount,
					orderId: 'your_order_id',
					userId: 'user_id',
					packageId: this.selectedCardlate.id
				}
			});
			return response.data;
			*/

			// 模拟返回微信App支付参数
			return new Promise((resolve) => {
				setTimeout(() => {
					resolve({
						appid: 'wx_app_id_example',           // 微信开放平台应用APPID
						partnerid: '1234567890',             // 微信支付商户号
						prepayid: 'wx_prepay_id_example',     // 预支付交易会话ID
						noncestr: 'random_string_' + Math.random().toString(36).substring(2, 15),
						timestamp: String(Math.floor(Date.now() / 1000)), // 时间戳（秒）
						sign: 'example_app_sign'              // App支付签名
					});
				}, 1000);
			});
		},

		// 获取支付宝支付参数（调用后端接口）
		async getAlipayPaymentParams() {
			// 这里应该调用你的后端接口获取支付宝支付参数
			// 示例代码：
			/*
			const response = await uni.request({
				url: 'https://your-api.com/payment/alipay/prepare',
				method: 'POST',
				data: {
					amount: this.paymentAmount,
					orderId: 'your_order_id',
					userId: 'user_id'
				}
			});
			return response.data;
			*/

			// 模拟返回支付宝支付参数
			return new Promise((resolve) => {
				setTimeout(() => {
					resolve({
						orderInfo: 'alipay_order_info_string_example'
					});
				}, 1000);
			});
		},

		// 支付成功后的处理
		handlePaymentSuccess() {
			// 这里可以添加支付成功后的逻辑
			console.log('支付成功，执行后续逻辑');

			// 例如：跳转到成功页面
			// uni.navigateTo({
			//     url: '/pages/payment/success'
			// });

			// 或者刷新当前页面数据
			// this.refreshData();
		},

		close() {
			this.showPopup = false;
		},
		open() {
			this.showPopup = true;
		},
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		// 选择卡片
		selectCard(card) {
			console.log('选择卡片:', card);
			this.selectedCardId = card.id;
			this.selectedCardlate = card
		},
		// 处理支付
		handlePayment() {
			if (!this.selectedCard) {
				uni.showToast({
					title: '请先选择卡片',
					icon: 'none'
				});
				return;
			}

			console.log('准备支付:', this.selectedCard);
			uni.showToast({
				title: `已选择: ${this.selectedCard.name}`,
				icon: 'none'
			});
			// 此处可以接续调用支付弹窗或支付API
		}
	}
};
</script>

<style lang="scss" scoped>
.refresh-page-container {
	background-color: #f7f8fa;
	// min-height: 100vh;
	padding-bottom: 180rpx; // 为底部支付栏留出空间
}

/* --- 自定义导航栏 --- */
.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	height: 44px;
	background-color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	border-bottom: 1rpx solid #f2f2f2;
	z-index: 100;
	/* #ifndef H5 */
	top: var(--status-bar-height);
	/* #endif */
}

.back-arrow {
	position: absolute;
	left: 30rpx;
	font-size: 40rpx;
	color: #333;
	padding: 10rpx;
}

.nav-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333333;
}

/* --- 主体内容 --- */
.content-wrapper {
	padding: 24rpx;
	padding-top: calc(44px + 24rpx);
	/* #ifndef H5 */
	padding-top: calc(44px + var(--status-bar-height) + 24rpx);
	/* #endif */
}

.ad-placeholder {
	height: 240rpx;
	background-color: #ebebeb;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 30rpx;
}

/* --- 通用区块样式 --- */
.section-container {
	background-color: #ffffff;
	padding: 30rpx;
	border-radius: 16rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 20rpx;
	display: block;
}

/* --- 刷新卡片列表 --- */
.card-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx; // 使用gap属性方便地设置间距
}

.card-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 30rpx;
	border: 2rpx solid #e5e5e5;
	border-radius: 16rpx;
	transition: all 0.2s ease-in-out;
	cursor: pointer;

	// 选中状态的样式
	&.active {
		border-color: #2979ff;
		background-color: #ecf5ff;
	}
}

.card-info {
	display: flex;
	align-items: center;

	.radio-icon {
		// 让图标和文字之间有间距
		margin-right: 20rpx;
	}

	.card-name {
		font-size: 30rpx;
		color: #303133;
		font-weight: 500;
	}
}

.card-count {
	font-size: 28rpx;
	color: #606266;
}

.card-price {
	font-size: 28rpx;
	color: #303133;
}


/* --- 购买须知 --- */
.notice-list {
	font-size: 26rpx;
	color: #909399;
	line-height: 1.7;

	.notice-item {
		display: block;
		margin-bottom: 10rpx;
		overflow: hidden;
	}
}


/* --- 底部支付栏 --- */
.bottom-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #ffffff;
	padding: 20rpx 30rpx;
	padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-top: 1rpx solid #f2f2f2;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.03);
}

.price-details {
	display: flex;
	flex-direction: column;

	.price-main {
		font-size: 32rpx;
		color: #ff5500;
		font-weight: bold;
	}

	.price-sub {
		font-size: 24rpx;
		color: #999999;
		margin-top: 4rpx;
	}
}

.pay-button {
	background: linear-gradient(90deg, #ff8c42, #ff5500);
	color: #ffffff;
	border: none;
	border-radius: 40rpx;
	padding: 0 60rpx;
	height: 80rpx;
	line-height: 80rpx;
	font-size: 30rpx;
	font-weight: bold;
	margin: 0;
	/* 重置按钮默认margin */
}

.popup-content {
	padding: 20rpx 0;
}

.popup-header {
	text-align: center;
	padding: 20rpx 0;
	position: relative;

	.popup-title {
		font-size: 32rpx;
		font-weight: 500;
		color: #303133;
	}
}

.price-section {
	text-align: center;
	padding: 40rpx 0;

	.currency-symbol {
		font-size: 40rpx;
		font-weight: bold;
		color: #303133;
	}

	.amount-text {
		font-size: 72rpx;
		font-weight: bold;
		color: #303133;
		margin-left: 8rpx;
	}
}

.payment-list {
	padding: 0 40rpx;
}

.payment-item {
	display: flex;
	align-items: center;
	padding: 30rpx 0;
	border-bottom: 1rpx solid #f5f5f5;

	&:last-child {
		border-bottom: none;
	}

	.payment-icon {
		width: 48rpx;
		height: 48rpx;
		margin-right: 24rpx;
	}

	.payment-info {
		flex: 1;
		display: flex;
		flex-direction: column;

		.payment-name {
			font-size: 30rpx;
			color: #303133;
		}

		.points-balance {
			font-size: 24rpx;
			color: #909399;
			margin-top: 4rpx;
		}
	}
}

.popup-footer {
	padding: 40rpx;

	.confirm-btn {
		height: 90rpx;
		line-height: 90rpx;
		border-radius: 45rpx;
		font-size: 32rpx;
		font-weight: 500;
		color: #ffffff;
		background: #00c8a0;
		border: none;
		background: linear-gradient(90deg, #00d2af, #00c8a0);

		&::after {
			border: none;
		}

		&:active {
			opacity: 0.8;
		}
	}
}
</style>