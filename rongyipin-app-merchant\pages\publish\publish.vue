<template>
	<view class="home-container">
		<!-- 顶部导航 -->
		<view class="nav-header">
			<text class="title">候选人</text>
			<view class="service-btn">
				<image src="/static/service.png" mode="aspectFit" class="service-icon"></image>
				<text class="service-text">客服</text>
			</view>
		</view>

		<!-- 主要内容区 -->
		<view class="content">
			<view class="banner">
				<text class="banner-title">招人就用青团社</text>
				<text class="banner-subtitle">8600万人在这找工作</text>
			</view>

			<!-- 功能列表 -->
			<view class="feature-list">
				<view class="feature-item">
					<view class="feature-icon lightning">⚡</view>
					<view class="feature-info">
						<text class="feature-title">招人快</text>
						<text class="feature-desc">新商家享高速招聘</text>
					</view>
				</view>

				<view class="feature-item">
					<view class="feature-icon doc">📄</view>
					<view class="feature-info">
						<text class="feature-title">简历多</text>
						<text class="feature-desc">8600万+全行业人才库</text>
					</view>
				</view>

				<view class="feature-item">
					<view class="feature-icon gift">🎁</view>
					<view class="feature-info">
						<text class="feature-title">赠送招聘道具</text>
						<text class="feature-desc">免费职位刷新2次/日</text>
					</view>
				</view>
			</view>

			<!-- 发布按钮 -->
			<view class="publish-btn-wrapper">
				<button class="publish-btn">
					<text class="plus-icon">+</text>
					<text>快速发布职位</text>
					<text class="quick-tag">最快30秒</text>
				</button>
			</view>

			<!-- 常见问题 -->
			<view class="faq-section">
				<text class="faq-title">常见问题</text>
				<view class="faq-item" @click="navigateToFaq('publish')">
					<text>发布职位相关问题</text>
					<text class="arrow">></text>
				</view>
				<view class="faq-item" @click="navigateToFaq('audit')">
					<text>职位审核相关问题</text>
					<text class="arrow">></text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	onTabItemTap() {
		// 点击 tabBar 时跳转到二级页面
		uni.navigateTo({
			url: '/pages/second/second' // 替换为你的二级页面路径
		});
	},
	onLoad(){
		console.log('222')
	},
	methods: {
		navigateToFaq(type) {
			// 处理常见问题导航
			console.log('Navigate to FAQ:', type)
		}
	}
}
</script>

<style lang="scss">
.home-container {
	min-height: 100vh;
	background: #02bdc4;
}

.nav-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 100rpx 70rpx 40rpx 70rpx;

	.title {
		font-size: 36rpx;
		color: #fff;
		font-weight: bold;
	}

	.service-btn {
		display: flex;
		align-items: center;

		.service-icon {
			width: 40rpx;
			height: 40rpx;
		}

		.service-text {
			color: #fff;
			font-size: 28rpx;
			margin-left: 8rpx;
		}
	}
}

.content {
	padding: 0 30rpx;
	background: white;
	height: 100vh;
	border-radius: 40rpx 40rpx 0 0;
}

.banner {
	background: #fff;
	padding: 100rpx 70rpx 40rpx 40rpx;
	border-radius: 20rpx;
	margin-bottom: 30rpx;

	.banner-title {
		font-size: 50rpx;
		font-weight: bold;
		color: black;
		display: block;
		margin-bottom: 16rpx;
	}

	.banner-subtitle {
		font-size: 50rpx;
		color: black;
	}
}

.feature-list {
	background: #fff;
	border-radius: 20rpx;
	padding: 20rpx;
	margin-bottom: 30rpx;

	.feature-item {
		display: flex;
		align-items: center;
		padding: 20rpx;

		.feature-icon {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 36rpx;
			margin-right: 20rpx;
		}

		.feature-info {
			flex: 1;

			.feature-title {
				font-size: 32rpx;
				color: #333;
				font-weight: 500;
				margin-bottom: 8rpx;
				display: block;
			}

			.feature-desc {
				font-size: 26rpx;
				color: #999;
			}
		}
	}
}

.publish-btn-wrapper {
	margin: 40rpx 0;

	.publish-btn {
		background: #01f8e4;
		border-radius: 20rpx;
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		font-size: 32rpx;
		position: relative;
		border: none;

		.plus-icon {
			font-size: 40rpx;
			margin-right: 10rpx;
		}

		.quick-tag {
			position: absolute;
			right: 20rpx;
			top: 50%;
			transform: translateY(-50%);
			background: #ffeb3b;
			color: #333;
			font-size: 24rpx;
			padding: 4rpx 12rpx;
			border-radius: 20rpx;
		}
	}
}

.faq-section {
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;

	.faq-title {
		font-size: 32rpx;
		color: #333;
		font-weight: bold;
		margin-bottom: 20rpx;
		display: block;
	}

	.faq-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		font-size: 28rpx;
		color: #666;
		border-bottom: 1rpx solid #eee;

		&:last-child {
			border-bottom: none;
		}

		.arrow {
			color: #999;
		}
	}
}
</style>
