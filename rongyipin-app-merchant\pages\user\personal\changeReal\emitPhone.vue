<template>
    <view class="verify-method-container">
        <!-- 顶部导航 -->
        <view class="nav-bar">
            <view class="nav-content">
                <view class="nav-left" @click="goBack">
                    <text class="nav-back">‹</text>
                </view>
                <view class="nav-center">
                    <text class="nav-title">选择核实方式</text>
                </view>
                <view class="nav-right"></view>
            </view>
        </view>

        <!-- 页面内容 -->
        <view class="content">
            <!-- 验证方式选项 -->
            <view class="verify-options">
                <view class="option-item" @click="selectVerifyMethod('phone')">
                    <view class="option-content">
                        <view class="option-icon">
                            <text class="icon-phone">📱</text>
                        </view>
                        <view class="option-text">
                            <text class="option-title">绑定手机号验证</text>
                        </view>
                    </view>
                    <view class="option-arrow">
                        <text class="arrow">›</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            selectedMethod: ''
        };
    },
    methods: {
        goBack() {
            uni.navigateBack()
        },
        selectVerifyMethod(method) {
            this.selectedMethod = method;

            if (method === 'phone') {
                // 跳转到手机号验证页面
                uni.navigateTo({
                    url: './emitCode'
                });
            }
        }
    }
}
</script>

<style scoped>
.verify-method-container {
    background: #fff;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 导航栏 */
.nav-bar {
    background: #fff;
    height: 88rpx;
    display: flex;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    /* border-bottom: 1rpx solid #f0f0f0; */
}

.nav-content {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32rpx;
}

.nav-left {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-back {
    font-size: 48rpx;
    color: #333;
    font-weight: 300;
}

.nav-center {
    flex: 1;
    text-align: center;
}

.nav-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
}

.nav-right {
    width: 80rpx;
}

/* 内容区域 */
.content {
    flex: 1;
    padding-top: 88rpx;
    padding: 88rpx 32rpx 40rpx;
}

/* 验证选项 */
.verify-options {
    margin-top: 40rpx;
}

.option-item {
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx 24rpx;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid #f0f0f0;
    transition: all 0.3s;
}

.option-item:active {
    transform: scale(0.98);
    background: #f8f9fa;
}

.option-content {
    display: flex;
    align-items: center;
    flex: 1;
}

.option-icon {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f9ff;
    border-radius: 50%;
    margin-right: 24rpx;
}

.icon-phone {
    font-size: 40rpx;
}

.option-text {
    flex: 1;
}

.option-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
}

.option-arrow {
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.arrow {
    font-size: 32rpx;
    color: #999;
    transform: rotate(90deg);
}
</style>