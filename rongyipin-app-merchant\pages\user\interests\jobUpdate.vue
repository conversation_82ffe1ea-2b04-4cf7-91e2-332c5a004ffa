<template>
    <view class="container">
        <!-- 顶部导航栏 -->

        <u-navbar :autoBack="true" title="职位刷新" :titleStyle="{
            color: '#222',
            fontWeight: 'bold',
            fontSize: '36rpx'
        }" :leftIconSize="22" bgColor="#f8f3f0" :leftIconColor="'#222'" fixed safeAreaInsetTop placeholder></u-navbar>
        <!-- 主要内容区域 -->
        <view class="content">
            <!-- 普通职位卡片 -->
            <view class="card">
                <!-- <view class="card-title">普通职位</view> -->

                <!-- Tab切换区域 -->
                <view class="tab-container">
                    <view class="tab-item">
                        <view class="tab-label">今日已用</view>
                        <view class="tab-value">
                            <text class="number">0</text>
                            <text class="unit">个</text>
                        </view>
                    </view>

                    <view class="tab-divider"></view>

                    <view class="tab-item">
                        <view class="tab-label">今日可用</view>
                        <view class="tab-value">
                            <text class="number">1</text>
                            <text class="unit">个</text>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 当前使用职位卡片 -->
            <view class="cardlate">
                <view class="empty-container">
                    <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit"></image>
                    <text class="empty-text">暂无数据</text>
                    <text class="empty-desc">快去发布职位吧~</text>
                </view>
            </view>

        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            list1: [{
                name: '当前使用职位',
            }, {
                name: '在线职位数组成',
            }],
            current: 0,
        }
    },
    methods: {
        click(item) {
            this.current = item.index
        },
        rightClick() {
            uni.navigateBack()
        }
    }
}
</script>

<style lang="scss">
::v-deep .u-tabs__wrapper__nav__item {
    width: 40% !important;
}

.tab-content {
    padding: 30rpx 0 10rpx;
}

.tab-pane {
    min-height: 100rpx;
}

.content-item {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    padding: 40rpx 0;
}

.container {
    min-height: 100vh;
    background-color: #f8f3f0;
    display: flex;
    flex-direction: column;
}

.content {
    // flex: 1;
    padding: 20rpx 20rpx;
    // margin-top: 70rpx;
    height: calc(100vh - 200rpx);
}

.card {
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    // height: calc(100% - 100rpx);
}

.cardlate {
    // background-color: #ffffff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    // box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    height: calc(100% - 400rpx);
}

.empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40rpx;

    .empty-icon {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 20rpx;
    }

    .empty-text {
        font-size: 32rpx;
        color: #333;
        margin-bottom: 12rpx;
    }

    .empty-desc {
        font-size: 28rpx;
        color: #999;
    }
}

.card-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 30rpx;
}

.tab-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 160rpx;
}

.tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.tab-divider {
    width: 2rpx;
    height: 100rpx;
    background-color: #eee;
}

.tab-label {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 20rpx;
}

.tab-value {
    display: flex;
    align-items: baseline;
}

.number {
    font-size: 64rpx;
    font-weight: bold;
    color: #222;
    font-family: Arial, sans-serif;
}

.unit {
    font-size: 32rpx;
    color: #222;
    margin-left: 4rpx;
}

.card-content {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20rpx 0;
}

.dash-line {
    width: 60rpx;
    height: 6rpx;
    background-color: #333;
    border-radius: 3rpx;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60rpx 0;
}

.empty-icon {
    width: 120rpx;
    height: 120rpx;
    opacity: 0.5;
    margin-bottom: 20rpx;
}

.empty-text {
    font-size: 28rpx;
    color: #999;
}
</style>