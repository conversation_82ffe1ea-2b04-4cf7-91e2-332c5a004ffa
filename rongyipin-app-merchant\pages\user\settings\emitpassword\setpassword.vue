<template>
    <view class="settings-container">
        <!-- 状态栏和导航栏 -->
        <view class="header">
            <u-navbar height="44px" title="设置密码" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>

        <!-- 表单区域 -->
        <view class="form-container">
            <!-- 新密码 -->
            <view class="form-item">
                <text class="form-label">新密码</text>
                <input class="form-input" type="password" placeholder="6~16个数字+字母组合，区分大小写" v-model="newPassword" />
            </view>

            <!-- 确认密码 -->
            <view class="form-item">
                <text class="form-label">确认密码</text>
                <input class="form-input" type="password" placeholder="再次输入密码" v-model="confirmPassword" />
            </view>
        </view>

        <!-- 完成按钮 -->
        <button class="complete-btn" @click="complete">完成</button>
    </view>
</template>

<script>
import { userApi } from "@/utils/api"
export default {
    data() {
        return {
            newPassword: '', // 新密码
            confirmPassword: '', // 确认密码
            phone: null
        };
    },
    onLoad(option) {
        console.log(option.phone);
        this.phone = option.phone;
    },
    methods: {
        async complete() {
            if (!this.newPassword || !this.confirmPassword) {
                uni.showToast({
                    title: '请填写完整信息',
                    icon: 'none'
                });
                return;
            }
            if (this.newPassword !== this.confirmPassword) {
                uni.showToast({
                    title: '两次密码输入不一致',
                    icon: 'none'
                });
                return;
            }
            const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,}$/;
            if (!passwordRegex.test(this.newPassword)) {
                uni.showToast({
                    title: '密码必须包含数字和字母，且长度不少于6位',
                    icon: 'none'
                });
                return;
            }

            if (this.newPassword !== this.confirmPassword) {
                uni.showToast({
                    title: '两次密码输入不一致',
                    icon: 'none'
                });
                return;
            }
            const params = {
                mobile: this.phone,
                newpassword: this.newPassword,
                newpassword2: this.confirmPassword
            };
            let res = await userApi.resetPwd(params)
            if (res.code == 200) {
                // 模拟完成操作
                uni.showToast({
                    title: '密码设置成功',
                    icon: 'success'
                });
                setTimeout(() => {
                    uni.navigateBack({
                        delta: 2
                    });
                }, 1500);
            }

        }
    }
};
</script>

<style lang="scss" scoped>
.settings-container {
    min-height: 100vh;
    // background-color: #f9f8fd;
    /* 背景颜色 */
}

.header {
    background-color: #fff;
}

.form-container {
    margin-top: 20rpx;
    background-color: #fff;
    padding: 20rpx;
    border-radius: 10rpx;
}

.form-item {
    // display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;
    padding: 20rpx;
    border-radius: 10rpx;
    // background-color: #f5f5f5;
    /* 输入框背景颜色 */
}

.form-label {
    font-size: 30rpx;
    color: #333;
    /* 标签文字颜色 */
}

.form-input {
    flex: 1;
    font-size: 28rpx;
    color: #666;
    /* 输入框文字颜色 */
    border: none;
    outline: none;
    background-color: #eff0f7;
    height: 100rpx;
    padding-left: 20rpx;
    border-radius: 20rpx;
    margin-top: 10rpx;
}

.form-input::placeholder {
    color: #d7d7d8;
    /* 占位符颜色 */
}

.complete-btn {
    width: 90%;
    margin: 0 auto;
    margin-top: 40rpx;
    padding: 10rpx 0;
    font-size: 32rpx;
    color: #fff;
    text-align: center;
    background: #00bec4;
    /* 渐变绿色 */
    border: none;
    border-radius: 10rpx;
    cursor: pointer;
}
</style>