<template>
    <view class="invoice-detail-page">
        <!-- 顶部导航栏 -->
        <view class="nav-header">
            <view class="nav-left" @click="goBack">
                <text class="back-icon">‹</text>
            </view>
            <text class="nav-title">开票详情</text>
            <view class="nav-right"></view>
        </view>

        <!-- 发票详情卡片 -->
        <view class="invoice-detail-card">
            <view class="detail-item">
                <text class="label" v-if="invoiceDetail.price">发票总额：</text>
                <text class="value amount">¥ {{ invoiceDetail.price || '2000' }}</text>
            </view>
            <view class="detail-item" v-if="invoiceDetail.create_time">
                <text class="label">申请时间：</text>
                <text class="value">{{ invoiceDetail.create_time || '2025-07-07 13:00' }}</text>
            </view>
            <view class="detail-item" v-if="invoiceDetail.company_name">
                <text class="label">开票主体：</text>
                <text class="value">{{ invoiceDetail.company_name || '河北彩鑫网络技术有限公司' }}</text>
            </view>
            <view class="detail-item" v-if="invoiceDetail.type">
                <text class="label">发票抬头：</text>
                <text class="value">{{ invoiceDetail.type ==1?'个人':'企业' || '企业' }}</text>
            </view>
            <view class="detail-item" v-if="invoiceDetail.email">
                <text class="label">电子邮箱：</text>
                <text class="value">{{ invoiceDetail.email || '3333333' }}</text>
            </view>
            <view class="detail-item" v-if="invoiceDetail.status">
                <text class="label">发票状态：</text>
                <text class="value status">{{ invoiceDetail.status ==0?'待开票':'已开票' || '已开具' }}</text>
            </view>
            <view class="detail-item" v-if="invoiceDetail.order_ids">
                <text class="label">关联订单：</text>
                <text class="value">{{ invoiceDetail.order_ids || 'xxxxxxxxx' }}</text>
            </view>
        </view>

        <!-- 备注卡片 -->
        <view class="remark-card">
            <view class="remark-title">备注</view>
            <view class="remark-content">
                {{ invoiceDetail.memo || 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx' }}
            </view>
        </view>

    </view>
</template>

<script>
import { invoice } from "@/utils/api"
export default {
    data() {
        return {
            invoiceId: '',
            invoiceDetail: {
               
            }
        };
    },
    onLoad(options) {
        if (options.item) {
            const item = JSON.parse(decodeURIComponent(options.item));
            console.log('收到的item:', item);
            this.invoiceDetail = item;
        }
    },
    methods: {
        // 返回上一页
        goBack() {
            uni.navigateBack();
        },

        // 获取发票详情
        async getInvoiceDetail() {
            try {
                // 这里应该调用获取发票详情的API
                // const res = await invoice.getInvoiceDetail(this.invoiceId);
                // if (res.code === 200) {
                //     this.invoiceDetail = res.data;
                // }

                // 暂时使用模拟数据
                this.invoiceDetail = {
                    amount: '2000',
                    applyTime: '2025-07-07 13:00',
                    company: '河北彩鑫网络技术有限公司',
                    header: '企业',
                    email: '3333333',
                    status: '已开具',
                    orderNumber: 'xxxxxxxxx',
                    remark: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
                };
            } catch (error) {
                console.error('获取发票详情失败:', error);
                uni.showToast({
                    title: '获取详情失败',
                    icon: 'none'
                });
            }
        },
        // 其他方法可以根据需要添加
    }
}
</script>

<style lang="scss">
// 页面整体样式
.invoice-detail-page {
    background-color: #7dd3c0;
    min-height: 100vh;
    font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 顶部导航栏 */
.nav-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    background: #7dd3c0;
    padding: 0 30rpx;
    padding-top: var(--status-bar-height, 44rpx);

    .nav-left {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        .back-icon {
            font-size: 40rpx;
            color: #ffffff;
            font-weight: 300;
        }
    }

    .nav-title {
        font-size: 36rpx;
        color: #ffffff;
        font-weight: 600;
    }

    .nav-right {
        width: 60rpx;
    }
}

/* 发票详情卡片 */
.invoice-detail-card {
    background: #ffffff;
    border-radius: 24rpx;
    margin: 30rpx;
    padding: 40rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

    .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx 0;
        border-bottom: 1rpx solid #f0f0f0;

        &:last-child {
            border-bottom: none;
        }

        .label {
            font-size: 28rpx;
            color: #666666;
            font-weight: 400;
        }

        .value {
            font-size: 28rpx;
            color: #333333;
            font-weight: 500;
            text-align: right;
            max-width: 400rpx;
            word-break: break-all;

            &.amount {
                color: #333333;
                font-weight: 600;
                font-size: 32rpx;
            }

            &.status {
                color: #52c41a;
                font-weight: 600;
            }
        }
    }
}

/* 备注卡片 */
.remark-card {
    background: #ffffff;
    border-radius: 24rpx;
    margin: 0 30rpx 30rpx;
    padding: 40rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

    .remark-title {
        font-size: 28rpx;
        color: #666666;
        margin-bottom: 20rpx;
        font-weight: 500;
    }

    .remark-content {
        font-size: 28rpx;
        color: #333333;
        line-height: 1.6;
        word-break: break-all;
        background: #f8f9fa;
        padding: 24rpx;
        border-radius: 12rpx;
        min-height: 120rpx;
    }
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
    .invoice-detail-card {
        margin: 20rpx;
        padding: 30rpx;

        .detail-item {
            padding: 20rpx 0;

            .label,
            .value {
                font-size: 26rpx;
            }
        }
    }

    .remark-card {
        margin: 0 20rpx 20rpx;
        padding: 30rpx;

        .remark-title,
        .remark-content {
            font-size: 26rpx;
        }
    }
}
</style>