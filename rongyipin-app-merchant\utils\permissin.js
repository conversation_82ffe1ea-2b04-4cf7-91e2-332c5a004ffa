/**
 * 权限处理工具
 */

// 检查是否有登录权限
export const checkLoginPermission = () => {
    const token = uni.getStorageSync("token")
    if (!token) {
      uni.showModal({
        title: "提示",
        content: "请先登录",
        success: (res) => {
          if (res.confirm) {
            uni.navigateTo({
              url: "/pages/login/login",
            })
          }
        },
      })
      return false
    }
    return true
  }
  
  // 检查相册权限
  export const checkPhotoPermission = () => {
    return new Promise((resolve, reject) => {
      // #ifdef APP-PLUS
      if (typeof plus !== "undefined" && plus) {
        const permission = "android.permission.READ_EXTERNAL_STORAGE"
        plus.android.requestPermissions(
          [permission],
          (resultObj) => {
            const failed = resultObj.denied && resultObj.denied.length > 0
            if (failed) {
              uni.showModal({
                title: "提示",
                content: "需要相册权限",
                showCancel: false,
              })
              reject(new Error("没有相册权限"))
            } else {
              resolve(true)
            }
          },
          (e) => {
            uni.showModal({
              title: "提示",
              content: "请求权限错误：" + e.message,
              showCancel: false,
            })
            reject(new Error("请求权限错误：" + e.message))
          },
        )
      } else {
        resolve(true) // plus is not available, resolve to true
      }
      // #endif
  
      // #ifdef MP-WEIXIN
      if (typeof uni !== "undefined" && uni) {
        uni.authorize({
          scope: "scope.writePhotosAlbum",
          success() {
            resolve(true)
          },
          fail() {
            uni.showModal({
              title: "提示",
              content: "需要相册权限",
              showCancel: false,
            })
            reject(new Error("没有相册权限"))
          },
        })
      } else {
        resolve(true) // uni is not available, resolve to true
      }
      // #endif
  
      // 其他平台默认有权限
      // #ifndef APP-PLUS || MP-WEIXIN
      resolve(true)
      // #endif
    })
  }
  
  // 检查摄像头权限
  export const checkCameraPermission = () => {
    return new Promise((resolve, reject) => {
      // #ifdef APP-PLUS
      if (typeof plus !== "undefined" && plus) {
        const permission = "android.permission.CAMERA"
        plus.android.requestPermissions(
          [permission],
          (resultObj) => {
            const failed = resultObj.denied && resultObj.denied.length > 0
            if (failed) {
              uni.showModal({
                title: "提示",
                content: "需要摄像头权限",
                showCancel: false,
              })
              reject(new Error("没有摄像头权限"))
            } else {
              resolve(true)
            }
          },
          (e) => {
            uni.showModal({
              title: "提示",
              content: "请求权限错误：" + e.message,
              showCancel: false,
            })
            reject(new Error("请求权限错误：" + e.message))
          },
        )
      } else {
        resolve(true) // plus is not available, resolve to true
      }
      // #endif
  
      // #ifdef MP-WEIXIN
      if (typeof uni !== "undefined" && uni) {
        uni.authorize({
          scope: "scope.camera",
          success() {
            resolve(true)
          },
          fail() {
            uni.showModal({
              title: "提示",
              content: "需要摄像头权限",
              showCancel: false,
            })
            reject(new Error("没有摄像头权限"))
          },
        })
      } else {
        resolve(true) // uni is not available, resolve to true
      }
      // #endif
  
      // 其他平台默认有权限
      // #ifndef APP-PLUS || MP-WEIXIN
      resolve(true)
      // #endif
    })
  }
  
  // 检查位置权限
  export const checkLocationPermission = () => {
    return new Promise((resolve, reject) => {
      // #ifdef APP-PLUS
      if (typeof plus !== "undefined" && plus) {
        const permission = "android.permission.ACCESS_FINE_LOCATION"
        plus.android.requestPermissions(
          [permission],
          (resultObj) => {
            const failed = resultObj.denied && resultObj.denied.length > 0
            if (failed) {
              uni.showModal({
                title: "提示",
                content: "需要位置权限",
                showCancel: false,
              })
              reject(new Error("没有位置权限"))
            } else {
              resolve(true)
            }
          },
          (e) => {
            uni.showModal({
              title: "提示",
              content: "请求权限错误：" + e.message,
              showCancel: false,
            })
            reject(new Error("请求权限错误：" + e.message))
          },
        )
      } else {
        resolve(true) // plus is not available, resolve to true
      }
      // #endif
  
      // #ifdef MP-WEIXIN
      if (typeof uni !== "undefined" && uni) {
        uni.authorize({
          scope: "scope.userLocation",
          success() {
            resolve(true)
          },
          fail() {
            uni.showModal({
              title: "提示",
              content: "需要位置权限",
              showCancel: false,
            })
            reject(new Error("没有位置权限"))
          },
        })
      } else {
        resolve(true) // uni is not available, resolve to true
      }
      // #endif
  
      // 其他平台默认有权限
      // #ifndef APP-PLUS || MP-WEIXIN
      resolve(true)
      // #endif
    })
  }
  
  // 导出权限检查方法
  export default {
    checkLoginPermission,
    checkPhotoPermission,
    checkCameraPermission,
    checkLocationPermission,
  }
  