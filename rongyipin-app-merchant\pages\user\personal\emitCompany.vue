<template>
    <view class="company-container">
        <!-- 导航栏 -->
        <view class="navbar">
            <view class="nav-left" @click="goBack">
                <text class="nav-icon">‹</text>
            </view>
            <view class="nav-title">
                <text class="title-text">我的公司</text>
            </view>
            <view class="nav-right">
            </view>
        </view>

        <!-- 内容区域 -->
        <view class="content">
            <!-- 公司信息展示区域 -->
            <view class="company-info-section">
                <!-- 公司头像 -->
                <view class="company-avatar">
                    <view class="avatar-placeholder">
                        <image class="avatar" :src="companyInfo.business_license" ></image>
                    </view>
                </view>
                
                <!-- 公司名称 -->
                <view class="company-name">
                    <text class="name-text">{{ companyInfo.company_name || '河北彩鑫网络科技有限公司' }}</text>
                </view>
            </view>

            <!-- 离开公司按钮 -->
            <view class="leave-company-section">
                <view class="leave-company-btn" @click="leaveCompany">
                    <text class="btn-text">离开公司</text>
                </view>
            </view>
        </view>

        <!-- 提示弹窗 -->
        <!-- <view class="warning-modal" v-if="showWarning" @click="hideWarning">
            <view class="modal-content" @click.stop>
                <view class="warning-text">
                    <text class="warning-title">您的账号为公司主账号无法离开</text>
                </view>
            </view>
        </view> -->
    </view>
</template>

<script>
import { priseApi } from '@/utils/api.js'

export default {
    data() {
        return {
            companyInfo: {},
            userInfo: {},
            showWarning: false
        }
    },
    onLoad() {
        this.loadCompanyInfo();
    },
    onShow() {
        this.loadCompanyInfo();
    },
    methods: {
        // 加载公司信息
        loadCompanyInfo() {
            // 从存储中获取公司信息
            const authInfo = uni.getStorageSync('is_auth');
            const userInfo = uni.getStorageSync('usinfo');
            
            if (authInfo) {
                this.companyInfo = authInfo;
            }
            
            if (userInfo) {
                this.userInfo = userInfo;
            }
        },
        
        // 返回上一页
        goBack() {
            uni.navigateBack();
        },
        
        // 离开公司
        leaveCompany() {
            uni.showToast({
                title: '您的账号为公司主账号无法离开',
                icon: 'none'
            })

            setTimeout(()=>{
                uni.navigateBack()
            },2000)
        },
        
        // 隐藏警告弹窗
        hideWarning() {
            this.showWarning = false;
        },
        
        // 刷新公司信息
        async refreshCompanyInfo() {
            try {
                uni.showLoading({
                    title: '加载中...'
                });
                
                const res = await priseApi.getCompanyInfoe();
                uni.hideLoading();
                
                if (res.code === 200) {
                    this.companyInfo = res.data;
                    if (res.data.is_auth == 1) {
                        uni.setStorageSync('is_auth', res.data);
                    }
                } else {
                    uni.showToast({
                        title: res.msg || '获取公司信息失败',
                        icon: 'none'
                    });
                }
            } catch (error) {
                uni.hideLoading();
                uni.showToast({
                    title: '网络错误',
                    icon: 'none'
                });
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.company-container {
    min-height: 100vh;
    background: #f8f9fa;
}

/* 导航栏样式 */
.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    padding: 0 30rpx;
    background: #ffffff;
    border-bottom: 1rpx solid #f0f0f0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-left,
.nav-right {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-icon {
    font-size: 40rpx;
    color: #333333;
    font-weight: 500;
}

.nav-title {
    flex: 1;
    text-align: center;
}

.title-text {
    font-size: 32rpx;
    color: #333333;
    font-weight: 500;
}

/* 内容区域 */
.content {
    flex: 1;
    padding: 40rpx 30rpx;
}

/* 公司信息展示区域 */
.company-info-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 80rpx 0;
    margin-bottom: 60rpx;
}

.company-avatar {
    margin-bottom: 40rpx;
}

.avatar-placeholder {
    width: 200rpx;
    height: 200rpx;
    border-radius: 50%;
    background: #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2rpx solid #f0f0f0;
}

.avatar-icon {
    font-size: 80rpx;
    color: #999999;
}

.company-name {
    text-align: center;
}

.name-text {
    font-size: 36rpx;
    color: #333333;
    font-weight: 500;
    line-height: 50rpx;
}

/* 离开公司按钮区域 */
.leave-company-section {
    padding: 0 30rpx;
}

.leave-company-btn {
    width: 100%;
    height: 88rpx;
    background: #4ECDC4;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(78, 205, 196, 0.3);
}

.btn-text {
    font-size: 32rpx;
    color: #ffffff;
    font-weight: 500;
}

/* 提示弹窗 */
.warning-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 40rpx;
}

.modal-content {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 40rpx 30rpx;
    max-width: 600rpx;
    width: 100%;
    position: relative;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.warning-text {
    text-align: center;
}

.warning-title {
    display: block;
    font-size: 32rpx;
    color: #333333;
    font-weight: 500;
    line-height: 44rpx;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
    .navbar {
        height: 80rpx;
        padding: 0 24rpx;
    }

    .content {
        padding: 32rpx 24rpx;
    }

    .company-info-section {
        padding: 60rpx 0;
        margin-bottom: 40rpx;
    }

    .avatar-placeholder {
        width: 160rpx;
        height: 160rpx;
    }

    .avatar-icon {
        font-size: 64rpx;
    }

    .leave-company-section {
        padding: 0 24rpx;
    }

    .modal-content {
        padding: 32rpx 24rpx;
    }
}
</style>
