<template>
    <view class="smart-chat-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" title="智能问答" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
                <template #right>
                    <view class="nav-actions">
                        <u-icon name="reload" size="18" color="#333" @click="clearChat" style="margin-right: 20rpx;"></u-icon>
                        <u-icon name="close" size="20" color="#333" @click="closePage"></u-icon>
                    </view>
                </template>
            </u-navbar>
        </view>

        <!-- AI助手状态栏 -->
        <view class="ai-status">
            <view class="status-indicator">
                <view class="status-dot"></view>
                <text class="status-text">AI助手在线</text>
            </view>
            <text class="status-desc">专业建议 · 即时回答</text>
        </view>

        <!-- 消息列表 -->
        <scroll-view class="message-list" scroll-y :scroll-into-view="lastMessageId" :scroll-with-animation="true">
            <!-- 欢迎卡片 -->
            <view v-if="messages.length === 0" class="welcome-card">
                <view class="welcome-icon">🤖</view>
                <text class="welcome-title">智能AI助手</text>
                <text class="welcome-desc">我是您的专属AI助手，可以为您提供专业建议和即时解答</text>
                <view class="welcome-features">
                    <view class="feature-tag">💡 智能分析</view>
                    <view class="feature-tag">⚡ 快速响应</view>
                    <view class="feature-tag">🎯 精准回答</view>
                </view>
            </view>

            <!-- 循环渲染消息 -->
            <view v-for="(msg, index) in messages" :key="index" :id="'msg-' + index">
                <!-- 普通文本消息 -->
                <view v-if="msg.type === 'text'" class="message-item" :class="{ 'user-message': msg.isUser }">
                    <view v-if="!msg.isUser" class="ai-avatar">
                        <view class="avatar-bg">
                            <text class="avatar-text">AI</text>
                        </view>
                    </view>
                    <view class="message-bubble" :class="{ 'user-bubble': msg.isUser, 'ai-bubble': !msg.isUser }">
                        <text class="message-text">{{ msg.text }}</text>
                        <view v-if="!msg.isUser" class="message-time">
                            <text class="time-text">{{ formatMessageTime(msg.timestamp) }}</text>
                        </view>
                    </view>
                </view>

                <!-- AI思考状态 -->
                <view v-if="msg.type === 'thinking'" class="thinking-message">
                    <view class="ai-avatar">
                        <view class="avatar-bg">
                            <text class="avatar-text">AI</text>
                        </view>
                    </view>
                    <view class="thinking-bubble">
                        <view class="thinking-dots">
                            <view class="dot"></view>
                            <view class="dot"></view>
                            <view class="dot"></view>
                        </view>
                        <text class="thinking-text">AI正在思考中...</text>
                    </view>
                </view>

                <!-- 智能建议卡片 -->
                <view v-if="msg.type === 'suggestions'" class="suggestions-card">
                    <view class="ai-avatar">
                        <view class="avatar-bg">
                            <text class="avatar-text">AI</text>
                        </view>
                    </view>
                    <view class="suggestions-container">
                        <view class="card-header">
                            <u-icon name="lightbulb" size="16" color="#ff9500"></u-icon>
                            <text class="card-title">智能建议</text>
                        </view>

                        <view class="suggestions-list">
                            <view v-for="(suggestion, idx) in msg.suggestions" :key="idx"
                                class="suggestion-item" @click="selectSuggestion(suggestion)">
                                <view class="suggestion-icon">💡</view>
                                <text class="suggestion-text">{{ suggestion }}</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 快速问题按钮 -->
            <view v-if="messages.length > 0" class="quick-questions">
                <text class="quick-title">您还可以问我：</text>
                <view class="quick-buttons">
                    <view v-for="question in quickQuestions" :key="question"
                        class="quick-btn" @click="askQuestion(question)">
                        {{ question }}
                    </view>
                </view>
            </view>
        </scroll-view>

        <!-- 底部输入区域 -->
        <view class="bottom-input">
            <!-- AI状态提示 -->
            <view v-if="isAiTyping" class="ai-status-tip">
                <view class="typing-indicator">
                    <view class="typing-dot"></view>
                    <view class="typing-dot"></view>
                    <view class="typing-dot"></view>
                </view>
                <text class="typing-text">AI正在回复中...</text>
            </view>

            <!-- 输入框区域 -->
            <view class="input-container">
                <view class="input-wrapper">
                    <textarea
                        class="message-input"
                        v-model="userInput"
                        placeholder="请输入您的问题，AI助手将为您提供专业建议..."
                        :auto-height="true"
                        :maxlength="500"
                        @confirm="sendMessage"
                        @input="onInputChange"
                    />
                    <view class="input-tools">
                        <view class="char-count">{{ userInput.length }}/500</view>
                        <view class="tool-buttons">
                            <u-icon name="mic" size="20" color="#999" @click="startVoiceInput"></u-icon>
                            <u-icon name="photo" size="20" color="#999" @click="selectImage" style="margin-left: 20rpx;"></u-icon>
                        </view>
                    </view>
                </view>

                <view class="send-button" :class="{ 'active': canSend }" @click="sendMessage">
                    <u-icon v-if="!isAiTyping" name="arrow-right" size="20" :color="canSend ? '#fff' : '#999'"></u-icon>
                    <u-loading-icon v-else mode="circle" size="20" color="#fff"></u-loading-icon>
                </view>
            </view>

            <!-- 智能提示 -->
            <view v-if="smartTips.length > 0 && !userInput" class="smart-tips">
                <text class="tips-title">💡 智能提示：</text>
                <scroll-view class="tips-scroll" scroll-x show-scrollbar="false">
                    <view v-for="tip in smartTips" :key="tip" class="tip-item" @click="useTip(tip)">
                        {{ tip }}
                    </view>
                </scroll-view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            userInput: '',
            messages: [],
            lastMessageId: '',
            isAiTyping: false,
            // 智能提示
            smartTips: [
                '如何提升工作效率？',
                '职业规划建议',
                '面试技巧分享',
                '简历优化指导'
            ],
            // 快速问题
            quickQuestions: [
                '职业发展建议',
                '技能提升方向',
                '行业趋势分析',
                '薪资谈判技巧'
            ],
            // AI知识库
            aiKnowledge: {
                // 职业发展相关
                '职业': {
                    keywords: ['职业', '发展', '规划', '晋升', '转行'],
                    responses: [
                        '职业发展需要明确目标，建议您：\n1. 评估当前技能和兴趣\n2. 制定短期和长期目标\n3. 持续学习和技能提升\n4. 建立专业网络\n5. 寻求导师指导',
                        '职业规划是一个持续的过程，建议定期回顾和调整您的职业目标，确保与市场需求和个人发展保持一致。'
                    ]
                },
                // 技能提升相关
                '技能': {
                    keywords: ['技能', '学习', '提升', '培训', '能力'],
                    responses: [
                        '技能提升建议：\n1. 识别核心技能差距\n2. 选择合适的学习方式\n3. 制定学习计划\n4. 实践应用所学知识\n5. 寻求反馈和改进',
                        '在快速变化的职场中，持续学习是关键。建议关注行业趋势，学习新兴技术和软技能。'
                    ]
                },
                // 面试相关
                '面试': {
                    keywords: ['面试', '求职', '应聘', '简历'],
                    responses: [
                        '面试成功要点：\n1. 充分准备和研究公司\n2. 练习常见面试问题\n3. 准备具体的工作案例\n4. 展现积极的态度\n5. 提出有价值的问题',
                        '简历优化建议：突出关键成就，使用量化数据，保持格式清晰，针对性地调整内容。'
                    ]
                },
                // 工作效率相关
                '效率': {
                    keywords: ['效率', '时间', '管理', '工作', '生产力'],
                    responses: [
                        '提升工作效率的方法：\n1. 使用时间管理工具\n2. 设定优先级\n3. 减少干扰因素\n4. 定期休息和调整\n5. 自动化重复性任务',
                        '高效工作的关键是专注和计划。建议使用番茄工作法或其他时间管理技巧。'
                    ]
                },
                // 薪资相关
                '薪资': {
                    keywords: ['薪资', '工资', '待遇', '谈判', '涨薪'],
                    responses: [
                        '薪资谈判技巧：\n1. 研究市场薪资水平\n2. 准备具体的成就证据\n3. 选择合适的时机\n4. 以价值为导向进行谈判\n5. 考虑整体薪酬包',
                        '薪资谈判是双向的过程，要展现自己的价值，同时理解公司的预算和政策。'
                    ]
                }
            }
        };
    },
    computed: {
        canSend() {
            return this.userInput.trim().length > 0 && !this.isAiTyping;
        }
    },
    onLoad() {
        // 智能问答页面不需要初始化消息
        this.loadChatHistory();
    },
    methods: {
        // 加载聊天历史
        loadChatHistory() {
            const history = uni.getStorageSync('aiChatHistory');
            if (history && history.length > 0) {
                this.messages = history;
                this.scrollToBottom();
            }
        },

        // 保存聊天历史
        saveChatHistory() {
            uni.setStorageSync('aiChatHistory', this.messages);
        },

        // 清空聊天记录
        clearChat() {
            uni.showModal({
                title: '确认清空',
                content: '确定要清空所有聊天记录吗？',
                success: (res) => {
                    if (res.confirm) {
                        this.messages = [];
                        uni.removeStorageSync('aiChatHistory');
                        uni.showToast({
                            title: '已清空',
                            icon: 'success'
                        });
                    }
                }
            });
        },

        // 发送消息
        sendMessage() {
            if (!this.canSend) return;

            const question = this.userInput.trim();
            this.addUserMessage(question);
            this.userInput = '';

            // 显示AI思考状态
            this.showAiThinking();

            // 模拟AI分析和回复
            setTimeout(() => {
                this.generateAiResponse(question);
            }, 1500 + Math.random() * 1000); // 1.5-2.5秒的思考时间
        },

        // 添加用户消息
        addUserMessage(text) {
            this.messages.push({
                type: 'text',
                text: text,
                isUser: true,
                timestamp: Date.now()
            });
            this.scrollToBottom();
            this.saveChatHistory();
        },

        // 显示AI思考状态
        showAiThinking() {
            this.isAiTyping = true;
            this.messages.push({
                type: 'thinking',
                timestamp: Date.now()
            });
            this.scrollToBottom();
        },

        // 生成AI回复
        generateAiResponse(question) {
            // 移除思考状态
            this.messages = this.messages.filter(msg => msg.type !== 'thinking');
            this.isAiTyping = false;

            // 分析问题并生成回复
            const response = this.analyzeQuestion(question);

            // 添加AI回复
            this.messages.push({
                type: 'text',
                text: response.answer,
                isUser: false,
                timestamp: Date.now()
            });

            // 如果有建议，添加建议卡片
            if (response.suggestions && response.suggestions.length > 0) {
                setTimeout(() => {
                    this.messages.push({
                        type: 'suggestions',
                        suggestions: response.suggestions,
                        timestamp: Date.now()
                    });
                    this.scrollToBottom();
                    this.saveChatHistory();
                }, 500);
            }

            this.scrollToBottom();
            this.saveChatHistory();
        },

        // 智能分析问题
        analyzeQuestion(question) {
            const lowerQuestion = question.toLowerCase();

            // 遍历知识库寻找匹配
            for (const [category, data] of Object.entries(this.aiKnowledge)) {
                for (const keyword of data.keywords) {
                    if (lowerQuestion.includes(keyword)) {
                        const responses = data.responses;
                        const answer = responses[Math.floor(Math.random() * responses.length)];

                        // 生成相关建议
                        const suggestions = this.generateSuggestions(category);

                        return {
                            answer: answer,
                            suggestions: suggestions,
                            category: category
                        };
                    }
                }
            }

            // 如果没有匹配，返回通用回复
            return {
                answer: '这是一个很好的问题！作为AI助手，我会尽力为您提供专业建议。\n\n虽然我可能没有完全理解您的具体需求，但我建议您：\n1. 提供更多具体信息\n2. 尝试换个角度描述问题\n3. 查看相关的专业资料\n\n请随时告诉我更多详情，我会为您提供更精准的建议。',
                suggestions: ['请提供更多详细信息', '换个方式描述问题', '查看相关帮助文档'],
                category: 'general'
            };
        },

        // 生成相关建议
        generateSuggestions(category) {
            const suggestionMap = {
                '职业': ['制定职业发展计划', '寻找导师指导', '参加行业活动', '建立专业网络'],
                '技能': ['选择在线课程', '参加培训班', '寻找实践机会', '加入学习社群'],
                '面试': ['模拟面试练习', '准备作品集', '研究目标公司', '优化个人简历'],
                '效率': ['使用时间管理工具', '制定工作计划', '减少干扰因素', '定期休息调整'],
                '薪资': ['调研市场薪资', '准备谈判材料', '展示工作成果', '考虑整体福利']
            };

            return suggestionMap[category] || ['获取更多信息', '寻求专业建议', '制定行动计划'];
        },

        // 选择建议
        selectSuggestion(suggestion) {
            this.userInput = suggestion;
            this.sendMessage();
        },

        // 快速提问
        askQuestion(question) {
            this.userInput = question;
            this.sendMessage();
        },

        // 使用智能提示
        useTip(tip) {
            this.userInput = tip;
        },

        // 格式化消息时间
        formatMessageTime(timestamp) {
            const date = new Date(timestamp);
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            return `${hours}:${minutes}`;
        },

        // 滚动到底部
        scrollToBottom() {
            this.$nextTick(() => {
                this.lastMessageId = 'msg-' + (this.messages.length - 1);
            });
        },

        // 输入变化处理
        onInputChange() {
            // 可以在这里添加输入提示逻辑
        },

        // 语音输入
        startVoiceInput() {
            uni.showToast({
                title: '语音功能开发中',
                icon: 'none'
            });
        },

        // 选择图片
        selectImage() {
            uni.chooseImage({
                count: 1,
                success: (res) => {
                    this.addUserMessage('[图片]');
                    setTimeout(() => {
                        this.generateAiResponse('用户发送了一张图片，请分析');
                    }, 1000);
                }
            });
        },

        // 关闭页面
        closePage() {
            // 保存聊天记录
            this.saveChatHistory();
            uni.navigateBack();
        }
    }
};

</script>

<style lang="scss" scoped>
.smart-chat-page {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: linear-gradient(180deg, #f8f9ff 0%, #f0f2f5 100%);
}

.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;

    .nav-actions {
        display: flex;
        align-items: center;
    }
}

.ai-status {
    margin-top: 88rpx;
    background: linear-gradient(135deg, #667eea, #764ba2);
    padding: 30rpx;
    text-align: center;

    .status-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10rpx;

        .status-dot {
            width: 12rpx;
            height: 12rpx;
            background-color: #4cd964;
            border-radius: 50%;
            margin-right: 12rpx;
            animation: pulse 2s infinite;
        }

        .status-text {
            color: white;
            font-size: 32rpx;
            font-weight: 600;
        }
    }

    .status-desc {
        color: rgba(255, 255, 255, 0.8);
        font-size: 24rpx;
    }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.message-list {
    flex: 1;
    padding: 20rpx;
    padding-bottom: 280rpx; // 为底部输入区域留出空间
}

.welcome-card {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 24rpx;
    padding: 60rpx 40rpx;
    margin: 40rpx 20rpx;
    text-align: center;
    color: white;

    .welcome-icon {
        font-size: 120rpx;
        margin-bottom: 30rpx;
    }

    .welcome-title {
        display: block;
        font-size: 48rpx;
        font-weight: 600;
        margin-bottom: 20rpx;
    }

    .welcome-desc {
        display: block;
        font-size: 28rpx;
        opacity: 0.9;
        line-height: 1.5;
        margin-bottom: 40rpx;
    }

    .welcome-features {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 20rpx;

        .feature-tag {
            background-color: rgba(255, 255, 255, 0.2);
            padding: 12rpx 24rpx;
            border-radius: 30rpx;
            font-size: 24rpx;
            backdrop-filter: blur(10rpx);
        }
    }
}

.message-item {
    display: flex;
    margin-bottom: 30rpx;
    align-items: flex-start;

    &.user-message {
        justify-content: flex-end;
    }
}

.ai-avatar {
    width: 80rpx;
    height: 80rpx;
    margin-right: 20rpx;
    flex-shrink: 0;

    .avatar-bg {
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);

        .avatar-text {
            color: white;
            font-size: 24rpx;
            font-weight: 600;
        }
    }
}

.message-bubble {
    max-width: 65%;
    padding: 24rpx 30rpx;
    border-radius: 20rpx;
    position: relative;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    &.ai-bubble {
        background-color: white;
        border-top-left-radius: 8rpx;

        .message-text {
            color: #333;
            line-height: 1.6;
        }

        .message-time {
            margin-top: 12rpx;

            .time-text {
                font-size: 22rpx;
                color: #999;
            }
        }
    }

    &.user-bubble {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-top-right-radius: 8rpx;

        .message-text {
            color: white;
        }
    }
}

.message-text {
    font-size: 30rpx;
    line-height: 1.5;
    word-break: break-all;
    white-space: pre-wrap;
}

// AI思考状态
.thinking-message {
    display: flex;
    margin-bottom: 30rpx;
    align-items: flex-start;
}

.thinking-bubble {
    background-color: white;
    border-radius: 20rpx;
    border-top-left-radius: 8rpx;
    padding: 24rpx 30rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    .thinking-dots {
        display: flex;
        gap: 8rpx;
        margin-bottom: 12rpx;

        .dot {
            width: 8rpx;
            height: 8rpx;
            background-color: #667eea;
            border-radius: 50%;
            animation: thinking 1.4s infinite ease-in-out;

            &:nth-child(1) { animation-delay: -0.32s; }
            &:nth-child(2) { animation-delay: -0.16s; }
            &:nth-child(3) { animation-delay: 0s; }
        }
    }

    .thinking-text {
        font-size: 26rpx;
        color: #999;
    }
}

@keyframes thinking {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

// 智能建议卡片
.suggestions-card {
    display: flex;
    margin-bottom: 30rpx;
    align-items: flex-start;
}

.suggestions-container {
    background: linear-gradient(135deg, #fff8e1, #fff3c4);
    border-radius: 20rpx;
    border-top-left-radius: 8rpx;
    padding: 30rpx;
    max-width: 75%;
    box-shadow: 0 4rpx 12rpx rgba(255, 149, 0, 0.1);
    border: 1rpx solid rgba(255, 149, 0, 0.2);

    .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;

        .card-title {
            font-size: 28rpx;
            font-weight: 600;
            color: #e65100;
            margin-left: 8rpx;
        }
    }
}

.suggestions-list {
    .suggestion-item {
        display: flex;
        align-items: center;
        padding: 20rpx;
        margin-bottom: 12rpx;
        background-color: rgba(255, 255, 255, 0.8);
        border-radius: 16rpx;
        transition: all 0.3s;

        &:last-child {
            margin-bottom: 0;
        }

        &:active {
            background-color: rgba(255, 255, 255, 1);
            transform: scale(0.98);
        }

        .suggestion-icon {
            font-size: 32rpx;
            margin-right: 16rpx;
        }

        .suggestion-text {
            font-size: 28rpx;
            color: #333;
            flex: 1;
        }
    }
}

// 快速问题
.quick-questions {
    margin: 40rpx 20rpx;

    .quick-title {
        display: block;
        font-size: 26rpx;
        color: #666;
        margin-bottom: 20rpx;
    }

    .quick-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;

        .quick-btn {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            color: #1976d2;
            padding: 16rpx 24rpx;
            border-radius: 30rpx;
            font-size: 26rpx;
            border: 1rpx solid rgba(25, 118, 210, 0.2);
            transition: all 0.3s;

            &:active {
                background: linear-gradient(135deg, #bbdefb, #90caf9);
                transform: scale(0.95);
            }
        }
    }
}

// 底部输入区域
.bottom-input {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    border-top: 1rpx solid #e0e0e0;
    padding: 20rpx;
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.ai-status-tip {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20rpx;

    .typing-indicator {
        display: flex;
        gap: 6rpx;
        margin-right: 12rpx;

        .typing-dot {
            width: 6rpx;
            height: 6rpx;
            background-color: #667eea;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;

            &:nth-child(1) { animation-delay: -0.32s; }
            &:nth-child(2) { animation-delay: -0.16s; }
            &:nth-child(3) { animation-delay: 0s; }
        }
    }

    .typing-text {
        font-size: 24rpx;
        color: #667eea;
    }
}

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.input-container {
    display: flex;
    align-items: flex-end;
    gap: 20rpx;
}

.input-wrapper {
    flex: 1;
    background-color: #f8f9ff;
    border-radius: 24rpx;
    border: 2rpx solid #e3f2fd;
    overflow: hidden;

    .message-input {
        width: 100%;
        min-height: 80rpx;
        max-height: 200rpx;
        padding: 20rpx 30rpx 0;
        font-size: 28rpx;
        line-height: 1.4;
        background-color: transparent;
        border: none;
        outline: none;
        resize: none;
    }

    .input-tools {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12rpx 30rpx 16rpx;

        .char-count {
            font-size: 22rpx;
            color: #999;
        }

        .tool-buttons {
            display: flex;
            align-items: center;
        }
    }
}

.send-button {
    width: 80rpx;
    height: 80rpx;
    background-color: #e0e0e0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;

    &.active {
        background: linear-gradient(135deg, #667eea, #764ba2);
        box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
    }
}

// 智能提示
.smart-tips {
    margin-top: 20rpx;

    .tips-title {
        font-size: 24rpx;
        color: #666;
        margin-bottom: 12rpx;
    }

    .tips-scroll {
        white-space: nowrap;

        .tip-item {
            display: inline-block;
            background: linear-gradient(135deg, #e8f5e8, #f1f8e9);
            color: #2e7d32;
            padding: 12rpx 20rpx;
            margin-right: 16rpx;
            border-radius: 20rpx;
            font-size: 24rpx;
            border: 1rpx solid rgba(46, 125, 50, 0.2);
            transition: all 0.3s;

            &:active {
                background: linear-gradient(135deg, #c8e6c9, #dcedc8);
                transform: scale(0.95);
            }
        }
    }
}
</style>
