<template>
    <view class="settings-container">
        <!-- 状态栏和导航栏 -->
        <view class="header">
            <u-navbar height="44px" title="设置密码" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>
        <!-- 表单区域 -->
        <view class="form-container">
            <!-- 手机号码 -->
            <view class="form-item">
                <text class="form-label">手机号码</text>
                <input class="form-input" type="text" placeholder="请输入手机号码" v-model="phone" />
            </view>

            <!-- 验证码 -->
            <view class="form-item">
                <text class="form-label">验证码</text>
                <view class="form-code">
                    <input class="form-input" type="text" placeholder="短信验证码" v-model="code" />
                    <u-button style="border-left:1px solid #dfdfdf !important;" class="code-btn" :disabled="isCounting"
                        @click="sendCode" text="月落">{{ isCounting ? `${countdown}s后重试` : '短信验证码' }}</u-button>
                </view>
            </view>
        </view>

        <!-- 下一步按钮 -->
        <button class="next-btn" @click="nextStep">下一步</button>
    </view>
</template>

<script>
import { userApi } from "@/utils/api"
export default {
    data() {
        return {
            phone: '', // 手机号码
            code: '', // 验证码
            isCounting: false, // 是否正在倒计时
            countdown: 60 // 倒计时时间
        }
    },
    methods: {
        // 发送验证码
        async sendCode() {
            if (!this.phone) {
                uni.showToast({
                    title: '请输入手机号码',
                    icon: 'none'
                });
                return;
            }
            const params = {
                phone: this.phone,
                event: "resetpwd"
            }
            let res = await userApi.Captcha(params)
            if (res.code == 200) {
                // 模拟发送验证码
                uni.showToast({
                    title: '验证码已发送',
                    icon: 'success'
                });

                // 开始倒计时
                this.isCounting = true;
                this.countdown = 60;
                const timer = setInterval(() => {
                    this.countdown--;
                    if (this.countdown <= 0) {
                        clearInterval(timer);
                        this.isCounting = false;
                    }
                }, 1000);

            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }

        },

        // 下一步操作
        async nextStep() {
            if (!this.phone || !this.code) {
                uni.showToast({
                    title: '请填写完整信息',
                    icon: 'none'
                });
                return;
            }
            let check = await userApi.phoneCheck({
                phone: this.phone,
                event: "resetpwd",
                code: this.code
            })
            if (check.code == 200) {
                uni.showToast({
                    title: check.msg,
                    icon: 'none'
                });
                setTimeout(() => {
                    uni.navigateTo({
                        url: `./setpassword?phone=${this.phone}`
                    });
                }, 1500);
            } else {
                uni.showToast({
                    title: check.msg,
                    icon: 'none'
                });
            }

            // let res = await userApi.login({
            //     phone: this.phone,
            //     code: this.code
            // });
            // if (res.code == 200) {
            //     // 模拟下一步操作
            //     uni.showToast({
            //         title: '下一步',
            //         icon: 'success'
            //     });
            // }else{
            //     uni.showToast({
            //         title: res.msg,
            //         icon: 'none'
            //     });
            // }

        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep .u-tabbar__content__item-wrapper {
    margin-bottom: 60rpx;
}



.settings-container {
    // min-height: 100vh;
    background-color: #f9f8fd;
}

.form-container {
    margin-top: 20rpx;
    background-color: #fff;
    /* padding: 20rpx; */
    border-radius: 10rpx;
}

.form-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;
    padding-bottom: 10rpx;
    padding: 20rpx 20rpx;
    height: 60rpx;
}

.form-label {
    font-size: 30rpx;
    color: #747476;
}

.form-input {
    flex: 1;
    margin-left: 20rpx;
    font-size: 28rpx;
    color: #666;
    border: none;
    outline: none;
    background-color: transparent;
}

.form-code {
    display: flex;
    align-items: center;
}

.code-btn {
    margin-left: 20rpx;
    // padding: 10rpx 20rpx;
    font-size: 28rpx;
    color: #73c4ab;
    border: none;
    border-radius: 5rpx;
    background-color: white;
    cursor: pointer;
    border: none !important;
    width: 200rpx;
    height: 40rpx;
}

.code-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.code-btn::after {
    border: none !important;
    height: 40rpx !important;
}

::v-deep .uni-input-placeholder {
    color: #d7d7d8;
    margin-top: 10rpx;
}

.next-btn {
    width: 94%;
    margin: 0 auto;
    margin-top: 40rpx;
    padding: 10rpx 0;
    font-size: 32rpx;
    color: #fff;
    text-align: center;
    background: linear-gradient(to right, #5bebd4, #4dc7c8);
    border: none;
    border-radius: 10rpx;
    cursor: pointer;
}
</style>