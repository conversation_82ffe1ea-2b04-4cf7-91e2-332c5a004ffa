<template>
    <view class="favorites-page">
        <u-navbar :autoBack="true" title="我的收藏" :titleStyle="{
            color: '#222',
            fontWeight: 'bold',
            fontSize: '36rpx'
        }" :leftIconSize="22" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>
        <!-- 1. 搜索栏 (保持不变) -->
        <view class="search-bar-container">
            <view class="search-bar">
                <uni-icons type="search" size="20" color="#999"></uni-icons>
                <input class="search-input" type="text" v-model="searchKeyword" @input="onSearchInput" placeholder="搜索牛人" placeholder-class="placeholder-style" />
            </view>
        </view>

        <!-- 2. 收藏列表 -->
        <scroll-view class="list-container" scroll-y="true" refresher-enabled="true" :refresher-triggered="refreshing"
            @refresherrefresh="onRefresh" @scrolltolower="onLoadMore" :lower-threshold="50">

            <view class="list-item" v-if="currentList.length > 0" v-for="(item, index) in currentList" :key="index">
                <view class="item-content">
                    <!-- 上：头像和基本信息 -->
                    <view class="top-section" @click="gotodetail(item)">
                        <view class="avatar-container">
                            <image class="avatar" :src="item.avatarUrl" mode="aspectFill"></image>
                        </view>
                        <view class="basic-info">
                            <view class="user-name">{{ item.username }}</view>
                            <view class="user-details">
                                <text class="experience" v-if="item.work_year">{{ item.work_year }}年</text>
                                <text class="divider">|</text>
                                <text class="education" v-if="item.degree_name">{{ item.degree_name }}</text>
                                <text class="divider">|</text>
                                <text class="age" v-if="item.age">{{ item.age }}岁</text>
                                <text class="divider">|</text>
                                <text class="location">{{ item.location }}</text>
                            </view>
                        </view>
                    </view>

                    <!-- 中：技能和标签 -->
                    <view class="middle-section">
                        <view class="skills">
                            <text class="skill-label">求职期望:</text>
                            <text class="skill-text">{{ item.job_name }}</text>
                            <text class="skill-text" style="margin-left: 30rpx;">{{ item.minsalary }}  - {{
                                item.maxsalary }}</text>
                        </view>
                        <view class="tags">
                            <view class="tag" v-for="(tag, tagIndex) in item.skills_title" :key="tagIndex">
                                {{ tag }}
                            </view>
                        </view>
                    </view>

                    <!-- 下：描述和按钮 -->
                    <view class="bottom-section">
                        <view class="description">{{ item.advantage }}</view>
                        <view class="action-btn" @click="contactUser(item)">
                            <text class="btn-text">打招呼</text>
                        </view>
                    </view>
                </view>
            </view>



            <!-- 暂无数据提示 -->
            <view class="no-data" v-if="currentList.length === 0 && !loading">
                <text class="no-data-text">暂无数据</text>
            </view>

            <!-- 加载更多提示 -->
            <view class="load-more" v-if="loading">
                <text class="loading-text">加载中...</text>
            </view>

            <!-- 没有更多数据提示 -->
            <view class="no-more" v-if="noMore && currentList.length > 0 && !loading">
                <text class="no-more-text">暂无更多数据了</text>
            </view>

        </scroll-view>
    </view>
</template>

<script>
import { home } from "@/utils/api.js"
import { methods } from "uview-ui/libs/mixin/mixin";
export default {
    data() {
        return {
            // 搜索相关
            searchKeyword: '',

            // 收藏列表数据 (保持不变)
            refreshing: false,
            loading: false,
            noMore: false,
            page: 1,
            pageSize: 10,
            currentList: [],
            total: 0,
            pagelist: {},
        };
    },
    async onShow() {
        // 重置状态
        this.page = 1;
        this.noMore = false;
        this.currentList = [];
        await this.postList(true);
    },
    methods: {
        async onSearchInput(e){
            console.log(e.detail.value)
            this.searchKeyword = e.detail.value
            await this.postList(true,this.searchKeyword);
        },
        async onLoadMore() {
            if (this.loading || this.noMore) return;
            this.loading = true;
            this.page++;
            await this.postList(false); // false 表示不是刷新，是加载更多
        },
        async postList(isRefresh = true,name) {
            try {
                if (isRefresh) {
                    this.loading = true;
                }

                const params = {
                    pageL: this.page,
                    size: this.pageSize,
                    name: name
                }

                const res = await home.getColletTalentList(params)
                const newData = res.data.data || [];

                if (isRefresh) {
                    // 刷新时重置数据
                    this.currentList = newData;
                } else {
                    // 加载更多时追加数据
                    this.currentList = [...this.currentList, ...newData];
                }

                // 判断是否还有更多数据
                if (newData.length < this.pageSize) {
                    this.noMore = true;
                } else {
                    this.noMore = false;
                }

            } catch (error) {
                console.error('获取数据失败:', error);
                uni.showToast({
                    title: '获取数据失败',
                    icon: 'none'
                });
            } finally {
                this.loading = false;
                uni.hideLoading();
            }
        },
        async onRefresh() {
            this.refreshing = true;
            // 重置分页状态
            this.page = 1;
            this.noMore = false;
            await this.postList(true); // true 表示是刷新
            this.refreshing = false;
        },
        contactUser(item) {
            let user = {
                ...item,
                user_id: item.id
            }
            console.log(item)
            const userInfo = encodeURIComponent(JSON.stringify(user))
            uni.navigateTo({
                url: `/pages/chat/chat?userInfo=${userInfo}`
            })
        },
        gotodetail(item) {
            console.log(item.id)
            uni.navigateTo({
                url: `/pages/homeIndex/detail/index?seeker_id=${item.id}&job_id=${item.job_id}`
            })
        },
    }
};
</script>

<style lang="scss">
// 页面背景色
page {
    background-color: #f4f4f8;
}

.favorites-page {
    padding: 20rpx;
}

// 1. 搜索栏 (样式不变)
.search-bar-container {
    padding: 0 10rpx;
    margin-bottom: 20rpx;

    .search-bar {
        display: flex;
        align-items: center;
        background-color: #ededed;
        border-radius: 40rpx;
        padding: 10rpx 30rpx;
        height: 72rpx;
        box-sizing: border-box;

        .search-input {
            flex: 1;
            font-size: 28rpx;
            margin-left: 16rpx;
        }

        .placeholder-style {
            color: #999;
        }
    }
}

// 2. 收藏列表 (样式已修正)
.list-container {
    flex: 1;
    // padding: 20rpx 30rpx;
    overflow-y: auto;
    /* 确保可以滚动 */
    box-sizing: border-box;
    /* 包含padding在内的盒模型 */
}

/* 列表项样式 */
.list-item {
    background: #ffffff;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;

    &:active {
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);
    }
}

.item-content {
    padding: 30rpx;
}

/* 上部分：头像和基本信息 */
.top-section {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
}

.avatar-container {
    margin-right: 20rpx;
}

.avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    border: 3rpx solid #f8f9fa;
}

.basic-info {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-size: 36rpx;
    font-weight: 600;
    color: #212529;
    margin-bottom: 8rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.user-details {
    font-size: 26rpx;
    color: #6c757d;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    .divider {
        margin: 0 8rpx;
        color: #dee2e6;
    }
}

/* 中部分：技能和标签 */
.middle-section {
    margin-bottom: 20rpx;
}

.skills {
    margin-bottom: 16rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    .skill-label {
        font-size: 26rpx;
        color: #6c757d;
    }

    .skill-text {
        font-size: 26rpx;
        color: #667eea;
        font-weight: 500;
    }
}

.tags {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
}

.tag {
    padding: 8rpx 16rpx;
    background: #f8f9fa;
    border-radius: 20rpx;
    font-size: 24rpx;
    color: #6c757d;
    border: 1rpx solid #e9ecef;
}

/* 下部分：描述和按钮 */
.bottom-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.description {
    flex: 1;
    font-size: 26rpx;
    color: #6c757d;
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 20rpx;
}

/* 操作按钮样式 */
.action-btn {
    padding: 12rpx 24rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 30rpx;
    box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
    flex-shrink: 0;

    &:active {
        transform: scale(0.95);
    }
}

.btn-text {
    color: #ffffff;
    font-size: 22rpx;
    font-weight: 500;
}

/* 加载状态样式 */
.load-more,
.no-more,
.no-data {
    text-align: center;
    padding: 40rpx 0;
}

.loading-text,
.no-more-text,
.no-data-text {
    font-size: 26rpx;
    color: #6c757d;
}

.no-data {
    padding: 80rpx 0;

    .no-data-text {
        color: #999;
        font-size: 28rpx;
    }
}

::v-deep .u-popup {
    flex: none;
}
</style>