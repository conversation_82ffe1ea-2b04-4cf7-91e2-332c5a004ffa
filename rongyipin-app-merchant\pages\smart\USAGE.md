# 在线客服使用指南

## 快速开始

### 1. 页面跳转

在任何页面中，可以通过以下方式跳转到在线客服：

```javascript
// 基础跳转
uni.navigateTo({
    url: '/pages/smart/onlineService'
});

// 带参数跳转（可选）
uni.navigateTo({
    url: '/pages/smart/onlineService?from=homepage&userId=123'
});
```

### 2. 在页面中添加客服入口

```vue
<template>
    <view class="page">
        <!-- 你的页面内容 -->
        
        <!-- 悬浮客服按钮 -->
        <view class="service-float-btn" @click="openService">
            <u-icon name="chat" size="24" color="white"></u-icon>
        </view>
    </view>
</template>

<script>
export default {
    methods: {
        openService() {
            uni.navigateTo({
                url: '/pages/smart/onlineService'
            });
        }
    }
}
</script>

<style>
.service-float-btn {
    position: fixed;
    right: 30rpx;
    bottom: 100rpx;
    width: 100rpx;
    height: 100rpx;
    background: linear-gradient(135deg, #4cd964, #5ac777);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 20rpx rgba(76, 217, 100, 0.4);
    z-index: 999;
}
</style>
```

## 自定义配置

### 1. 修改问题分类和内容

在 `onlineService.vue` 中找到 `smartQuestions` 数据，按需修改：

```javascript
smartQuestions: {
    '常见问题': [
        { id: 1, question: '如何注册账户？' },
        { id: 2, question: '忘记密码怎么办？' }
    ],
    '订单相关': [
        { id: 3, question: '如何查看订单状态？' },
        { id: 4, question: '如何申请退款？' }
    ],
    '技术支持': [
        { id: 5, question: 'APP闪退怎么办？' },
        { id: 6, question: '如何更新版本？' }
    ]
}
```

### 2. 自定义智能回答

修改 `smartAnswers` 对象：

```javascript
smartAnswers: {
    '如何注册账户？': '点击首页右上角"注册"按钮，填写手机号和验证码即可完成注册。',
    '忘记密码怎么办？': '在登录页面点击"忘记密码"，通过手机验证码重置密码。',
    // 添加更多问答对...
}
```

### 3. 修改关键词匹配规则

在 `getSmartAnswer` 方法中自定义关键词：

```javascript
const keywords = {
    '注册': '注册相关帮助信息...',
    '登录': '登录相关帮助信息...',
    '支付': '支付相关帮助信息...',
    '退款': '退款相关帮助信息...'
};
```

## 样式自定义

### 1. 修改主题色

```scss
// 将所有的 #4cd964 替换为你的主题色
$primary-color: #007aff; // 蓝色主题
$primary-color: #ff3b30; // 红色主题
$primary-color: #ff9500; // 橙色主题
```

### 2. 自定义头像

替换头像图片路径：

```vue
<image class="avatar-img" src="/static/your-avatar.png" />
```

### 3. 修改气泡样式

```scss
.message-bubble {
    &.bot-bubble {
        background-color: #f0f0f0; // 机器人消息背景
        color: #333;
    }
    
    &.user-bubble {
        background-color: #007aff; // 用户消息背景
        color: white;
    }
}
```

## 高级功能

### 1. 接入真实API

```javascript
// 在 methods 中添加 API 调用
async sendMessageToAPI(message) {
    try {
        const response = await uni.request({
            url: 'https://your-api.com/chat',
            method: 'POST',
            data: {
                message: message,
                userId: this.userId
            }
        });
        
        return response.data.reply;
    } catch (error) {
        console.error('API调用失败:', error);
        return '抱歉，服务暂时不可用，请稍后再试。';
    }
}
```

### 2. 添加消息持久化

```javascript
// 保存聊天记录
saveChatHistory() {
    uni.setStorageSync('chatHistory', this.messages);
},

// 加载聊天记录
loadChatHistory() {
    const history = uni.getStorageSync('chatHistory');
    if (history) {
        this.messages = history;
    }
}
```

### 3. 添加用户身份验证

```javascript
onLoad(options) {
    // 检查用户登录状态
    const userInfo = uni.getStorageSync('userInfo');
    if (!userInfo) {
        uni.showModal({
            title: '提示',
            content: '请先登录后使用客服功能',
            success: (res) => {
                if (res.confirm) {
                    uni.navigateTo({
                        url: '/pages/login/login'
                    });
                }
            }
        });
        return;
    }
    
    this.initializeChat();
}
```

## 性能优化

### 1. 消息列表优化

```javascript
// 限制消息数量，避免内存溢出
addMessage(text, isUser) {
    this.messages.push({
        type: 'text',
        text: text,
        isUser: isUser,
        timestamp: new Date().getTime()
    });
    
    // 保持最多100条消息
    if (this.messages.length > 100) {
        this.messages.splice(0, this.messages.length - 100);
    }
    
    this.scrollToBottom();
}
```

### 2. 图片懒加载

```vue
<image 
    class="avatar-img" 
    :src="avatarUrl" 
    lazy-load
    @error="onImageError"
/>
```

## 常见问题

### Q: 如何添加表情功能？
A: 可以集成 emoji 选择器组件，或使用 Unicode 表情符号。

### Q: 如何实现语音消息？
A: 使用 uni.getRecorderManager() API 录制音频，然后上传到服务器。

### Q: 如何添加文件上传？
A: 使用 uni.chooseFile() 选择文件，然后通过 uni.uploadFile() 上传。

### Q: 如何实现消息推送？
A: 集成 uni-push 或第三方推送服务（如极光推送）。

## 部署注意事项

1. 确保所有图片资源路径正确
2. 检查 pages.json 配置是否正确
3. 测试在不同平台（H5、小程序、APP）的兼容性
4. 配置相应的权限（如录音、相机等）

---

更多问题请参考 [README.md](./README.md) 或联系开发团队。
