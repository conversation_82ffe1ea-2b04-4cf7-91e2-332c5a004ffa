<template>
    <view class="failed-page">
        <!-- 顶部关闭按钮 -->
        <view class="header">
            <!-- <view class="close-btn" @click="closePage">
                <text class="close-icon">×</text>
            </view> -->
        </view>
        <!-- 主要内容区域 -->
        <view class="content">
            <!-- 标题 -->
            <view class="title">
                商家资质认证失败，请查看失败原因后再重试
            </view>

            <!-- 失败原因输入框 -->
            <view class="reason-container">
                <textarea
                    class="reason-input"
                    v-model="failureReason"
                    placeholder="失败原因....."
                    placeholder-class="placeholder-style"
                    disabled
                ></textarea>
            </view>

            <!-- 重新认证按钮 -->
            <view class="button-container">
                <button class="retry-btn" @click="retryAuthentication">
                    重新认证
                </button>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            failureReason: '' // 失败原因
        };
    },
    onLoad(options) {
        // 接收传递过来的失败原因
        if (options) {
            console.log('接收到的失败原因:', options)
            this.failureReason = decodeURIComponent(options.msg);
        }
    },
    methods: {
        // 关闭页面
        closePage() {
            uni.navigateBack();
        },

        // 重新认证
        retryAuthentication() {
            // 跳转到认证页面
            uni.navigateTo({
                url: '/pages/authentication/index'
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.failed-page {
    width: 100%;
    height: 100vh;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
}

/* 顶部区域 */
.header {
    padding: 40rpx 30rpx 20rpx;
    display: flex;
    justify-content: flex-end;
    margin-top: 30rpx;
}

.close-btn {
    width: 60rpx;
    height: 60rpx;
    background-color: #333333;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.close-icon {
    color: #ffffff;
    font-size: 36rpx;
    font-weight: bold;
    line-height: 1;
}

/* 主要内容区域 */
.content {
    flex: 1;
    padding: 0 60rpx;
    display: flex;
    flex-direction: column;
}

/* 标题 */
.title {
    font-size: 32rpx;
    color: #333333;
    text-align: center;
    line-height: 1.5;
    margin-bottom: 80rpx;
    font-weight: 500;
}

/* 失败原因容器 */
.reason-container {
    margin-bottom: 120rpx;
}

.reason-input {
    width: 100%;
    height: 300rpx;
    background-color: #f8f8f8;
    border: 2rpx solid #e0e0e0;
    border-radius: 12rpx;
    padding: 30rpx;
    box-sizing: border-box;
    font-size: 28rpx;
    color: #333333;
    line-height: 1.6;
    resize: none;
}

.placeholder-style {
    color: #999999;
    font-size: 28rpx;
}

/* 按钮容器 */
.button-container {
    margin-top: auto;
    padding-bottom: 80rpx;
}

.retry-btn {
    width: 100%;
    height: 88rpx;
    background-color: #4ECDC4;
    color: #ffffff;
    border: none;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    &::after {
        border: none;
    }

    &:active {
        background-color: #45b8b1;
    }
}
</style>