# 在线客服系统

## 功能概述

这是一个基于 uni-app 开发的智能在线客服系统，模仿微信客服界面设计，提供智能问答和人工客服功能。

## 主要功能

### 1. 智能问答
- 🤖 AI机器人"团团"自动回复
- 📝 预设常见问题和答案
- 🔍 关键词匹配智能回复
- 📊 问题分类管理（推荐、找工作、账户信息、企业服务）

### 2. 界面特色
- 💬 仿微信聊天界面设计
- 🎨 绿色主题色调，符合企业形象
- 📱 响应式设计，适配各种屏幕
- ⚡ 流畅的动画效果

### 3. 交互功能
- 📤 文本消息发送
- 🖼️ 图片上传（开发中）
- 🎤 语音输入（开发中）
- 😊 表情选择（开发中）
- 👥 人工客服转接

### 4. 智能推荐
- 🏷️ 问题分类标签
- 🔄 "换一换"功能
- 📋 热门问题列表
- ➡️ 一键选择问题

## 文件结构

```
pages/smart/
├── onlineService.vue    # 主要的在线客服页面
├── chat.vue            # 原有的智能聊天页面
├── serviceDemo.vue     # 功能演示页面
└── README.md           # 说明文档
```

## 使用方法

### 1. 页面跳转
```javascript
// 跳转到在线客服页面
uni.navigateTo({
    url: '/pages/smart/onlineService'
});

// 跳转到演示页面
uni.navigateTo({
    url: '/pages/smart/serviceDemo'
});
```

### 2. 自定义问答数据
在 `onlineService.vue` 中修改 `smartQuestions` 和 `smartAnswers` 数据：

```javascript
smartQuestions: {
    '推荐': [
        { id: 1, question: '你的问题1' },
        { id: 2, question: '你的问题2' }
    ]
},
smartAnswers: {
    '你的问题1': '对应的回答1',
    '你的问题2': '对应的回答2'
}
```

### 3. 样式自定义
主要样式变量：
- 主题色：`#4cd964`（绿色）
- 背景色：`#f5f5f5`（浅灰）
- 卡片圆角：`20rpx`
- 头像大小：`80rpx`

## 技术特点

### 1. 响应式设计
- 使用 Flexbox 布局
- 适配不同屏幕尺寸
- 支持安全区域适配

### 2. 组件化开发
- 消息组件模块化
- 可复用的UI组件
- 清晰的数据结构

### 3. 用户体验
- 平滑滚动到底部
- 输入状态反馈
- 点击反馈效果
- 加载动画

## 扩展功能

### 待开发功能
1. 🔊 语音消息支持
2. 📷 图片消息处理
3. 📍 位置分享
4. 📞 视频通话
5. 📊 客服数据统计
6. 🔔 消息推送
7. 💾 聊天记录存储
8. 🌐 多语言支持

### 集成建议
1. 接入真实的客服API
2. 添加用户身份验证
3. 实现消息持久化
4. 添加文件上传功能
5. 集成第三方AI服务

## 注意事项

1. 当前使用的是模拟数据，实际使用时需要接入真实的后端API
2. 图片路径需要根据实际项目调整
3. 某些功能（语音、图片等）需要相应的权限配置
4. 建议在真机上测试完整功能

## 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 基础聊天界面
- ✅ 智能问答功能
- ✅ 问题分类管理
- ✅ 换一换功能
- ✅ 响应式设计

---

如有问题或建议，请联系开发团队。
