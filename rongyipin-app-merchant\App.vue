<script>
import { getStorage } from './utils/storage'
import { priseApi ,positionsApi} from "@/utils/api"
// import initApp from '@/common/appInit.js';
// import openApp from '@/common/openApp.js';
// #ifdef H5
// openApp() //创建在h5端全局悬浮引导用户下载app的功能
// #endif
// import checkIsAgree from '@/pages/uni-agree/utils/uni-agree.js';
// import uniIdPageInit from '@/uni_modules/uni-id-pages/init.js';
export default {
	globalData: {
		searchText: '',
		appVersion: {},
		config: {},
		$i18n: {},
		$t: {},
		serverUrl: 'ws://*************:82/ws', // 替换为你的 WebSocket 服务地址
		socket: null, // WebSocket 实例（废弃）
		socketTask: null, // SocketTask 实例
		reconnectAttempts: 0, // 重连次数
		maxReconnectAttempts: 999999999999, // 最大重连次数
		reconnectInterval: 3000, // 重连间隔（毫秒）
		heartBeatInterval: null, // 心跳定时器
		heartBeatTime: 30000, // 心跳间隔（30秒）
		isWebSocketConnected: false, // WebSocket 连接状态
		messageCount: 0, // 消息计数器，用于调试
	},
	onLaunch: function () {

		console.log('App Launch')
		// this.globalData.$i18n = this.$i18n
		// this.globalData.$t = str => this.$t(str)

		// 先检查 token，然后再连接 WebSocket
		this.initializeApp()
		setTimeout(() => {
			uni.setTabBarItem({
				index: 2, // 发布选项卡的索引
				text: '发布',
				iconPath: 'static/tabbar/add.png',
				selectedIconPath: 'static/tabbar/add.png'
			});
		}, 100);
		// 检查更新
		this.checkUpdate()
		// 初始化一些全局配置
		this.initApp()
		// 程序启动时初始化 WebSocket 长连接
		// this.connectWebSocket();
		// uniIdPageInit()

		// #ifdef APP
		//checkIsAgree(); APP端暂时先用原生默认生成的。目前，自定义方式启动vue界面时，原生层已经请求了部分权限这并不符合国家的法规
		// #endif

		// #ifdef H5
		// checkIsAgree(); // 默认不开启。目前全球，仅欧盟国家有网页端同意隐私权限的需要。如果需要可以自己去掉注视后生效
		// #endif

		// #ifdef APP-PLUS
		//idfa有需要的用户在应用首次启动时自己获取存储到storage中
		/*var idfa = '';
		var manager = plus.ios.invoke('ASIdentifierManager', 'sharedManager');
		if(plus.ios.invoke(manager, 'isAdvertisingTrackingEnabled')){
			var identifier = plus.ios.invoke(manager, 'advertisingIdentifier');
			idfa = plus.ios.invoke(identifier, 'UUIDString');
			plus.ios.deleteObject(identifier);
		}
		plus.ios.deleteObject(manager);
		console.log('idfa = '+idfa);*/
		// #endif
	},
	onShow: function () {
		console.log('App Show')
		// 应用回到前台时，检查 WebSocket 连接
		setTimeout(() => {
			this.ensureWebSocketConnection()
		}, 1000)
	},
	onHide: function () {
		console.log('App Hide')
	},
	methods: {
		// 初始化应用
		async initializeApp() {
			console.log('🚀 开始初始化应用...')

			// 先检查 token，checkToken 方法内部会调用 connectWebSocket
			await this.checkToken()
		},

		// 确保 WebSocket 连接的方法
		ensureWebSocketConnection() {
			console.log('🔍 检查 WebSocket 连接状态...')
			if (!this.globalData.isWebSocketConnected) {
				console.log('⚠️ WebSocket 未连接，尝试重新连接...')
				this.connectWebSocket()
			} else {
				console.log('✅ WebSocket 已连接')
			}
		},

		// 检查更新
		checkUpdate() {
			// #ifdef MP-WEIXIN
			if (uni.canIUse("getUpdateManager")) {
				const updateManager = uni.getUpdateManager()
				updateManager.onCheckForUpdate(function (res) {
					if (res.hasUpdate) {
						updateManager.onUpdateReady(function () {
							uni.showModal({
								title: "更新提示",
								content: "新版本已经准备好，是否重启应用？",
								success: function (res) {
									if (res.confirm) {
										updateManager.applyUpdate()
									}
								},
							})
						})
						updateManager.onUpdateFailed(function () {
							uni.showModal({
								title: "提示",
								content: "新版本下载失败，请检查网络后重试",
								showCancel: false,
							})
						})
					}
				})
			}
			// #endif
		},
		// 初始化应用
		initApp() {
			// 设置请求拦截器
			uni.addInterceptor("request", {
				invoke(args) {
					// 请求前处理
					console.log("请求拦截器", args)
					return args
				},
				success(args) {
					// 请求成功后处理
					console.log("请求成功拦截器", args)
					return args
				},
				fail(err) {
					// 请求失败处理
					console.log("请求失败拦截器", err)
					return err
				},
				complete(res) {
					// 请求完成处理
					console.log("请求完成拦截器", res)
					return res
				},
			})
		},
		// 检查登录状态
		async checkToken() {
			const token = uni.getStorageSync('token')
			console.log(token, 'token')
			if (token) {
				let res = await priseApi.getUserInfo()
				if (res.data.identity == null) {
					uni.navigateTo({
						url: '/pages/user/realName',
					})
				} else {
					uni.setStorageSync('usinfo', res.data)
					let reslate = await priseApi.getCompanyInfoe();
					if (reslate.code == 200) {
						if (reslate.data.is_auth == 1) {
							uni.setStorageSync('is_auth', reslate.data)
						}
						let res = await positionsApi.postJobList({
							page: 1,
							size: 10000,
							status: 1,
							order: 0
						})
						if (res.code == 200) {
							if (res.data.data.length > 0) {
								console.log('1111' )
								uni.switchTab({
									url: '/pages/homeIndex/index',
								})
							}else{
								console.log('222' )
								uni.redirectTo({
									url: '/pages/second/second',
								})
							}
						}

					} else if (reslate.code == 404) {
						uni.navigateTo({
							url: '/pages/authentication/index',
						})
					} else if (reslate.code == 406) {
						uni.navigateTo({ url: '/pages/audits/index' })
					} else if (reslate.code == 405) {
						console.log('reslate', reslate.data.desc)
						uni.redirectTo({ url: `/pages/audits/failed?msg=${reslate.data.desc}` })
					}

				}
				uni.hideLoading();
				this.connectWebSocket()
			} else {
				uni.reLaunch({
					url: '/pages/login/login',
				})

			}
		},

		connectWebSocket() {
			// 防止重复连接
			if (this.globalData.isWebSocketConnected) {
				console.log('⚠️ WebSocket 已连接，跳过重复连接')
				return
			}

			const token = uni.getStorageSync('token')
			console.log('🔄 开始建立WebSocket连接...')
			console.log('🔑 Token:', token ? `存在(长度:${token.length})` : '不存在')
			console.log('🌐 服务器地址:', this.globalData.serverUrl)

			// 如果没有 token，延迟重试
			if (!token) {
				console.warn('⚠️ Token不存在，3秒后重试连接')
				setTimeout(() => {
					this.connectWebSocket()
				}, 3000)
				return
			}

			const finalUrl = `${this.globalData.serverUrl}?token=${encodeURIComponent(token)}`
			console.log('🔗 最终连接URL:', finalUrl)

			this.socketTask = uni.connectSocket({
				url: finalUrl,
				success: () => {
					console.log('✅ WebSocket: 连接请求发送成功');
				},
				fail: (error) => {
					console.error('❌ WebSocket: 连接请求失败', error);
					this.handleReconnect();
				},
			});

			// 将 socketTask 保存到 globalData 中，方便其他页面访问
			this.globalData.socketTask = this.socketTask;

			// 使用 SocketTask 的方法替换废弃的 uni.onSocket* 方法

			// 监听 WebSocket 连接成功
			this.socketTask.onOpen(() => {
				console.log('🎉 WebSocket: 连接已打开');
				this.globalData.isWebSocketConnected = true; // 更新连接状态
				console.log('✅ 全局连接状态已更新为: true')
				this.reconnectAttempts = 0; // 重置重连计数

				// 发送订阅消息，监听目标接口
				this.socketTask.send({
					data: JSON.stringify({
						type: 'subscribe',
						interface: '/message/list', // 替换为目标接口
					}),
					success: () => {
						console.log('WebSocket: 订阅消息发送成功');
					},
					fail: (error) => {
						console.error('WebSocket: 订阅消息发送失败', error);
					},
				});

				// 启动心跳机制
				this.startHeartBeat();
			});

			// 监听 WebSocket 消息
			this.socketTask.onMessage((res) => {
				this.globalData.messageCount++
				try {
					const message = JSON.parse(res.data); // 假设后端发送 JSON 数据

					if (message.type === 'pong') {
						console.log('💓 WebSocket: 收到心跳响应');
						return;
					}

				
					// 提交到 Vuex 处理消息
					this.$store.dispatch('handleWebSocketMessage', message);

					// 通过事件总线通知其他页面
					uni.$emit('websocket-message', message);

					// 显示通知
					console.log('🔔 准备显示通知...');
					try {
						const pages = getCurrentPages();
						const currentPage = pages[pages.length - 1];
						console.log('📄 当前页面:', currentPage ? currentPage.route : '无页面');

						if (currentPage && currentPage.route !== 'pages/chat/chat') {
							let notificationText = '收到新消息';
							if (message.type === 'img') {
								notificationText = '收到新图片消息';
							} else if (message.type === 'location') {
								notificationText = '收到新位置消息';
							}

							console.log('🔔 显示通知:', notificationText);
							uni.showToast({
								title: notificationText,
								icon: 'none',
								duration: 2000,
							});
						} else {
							console.log('🔕 在聊天页面，不显示通知');
						}
					} catch (pageError) {
						console.error('📄 获取页面信息失败:', pageError);
					}
				} catch (error) {
					console.error('WebSocket: 消息解析失败', error);
					uni.showToast({
						title: '消息解析失败',
						icon: 'error',
						duration: 2000,
					});
				}
			});

			// 监听 WebSocket 连接关闭
			this.socketTask.onClose((res) => {
				console.log('🔴 WebSocket: 连接已关闭', res);
				this.globalData.isWebSocketConnected = false; // 更新连接状态
				this.stopHeartBeat(); // 停止心跳
				this.handleReconnect();
			});

			// 监听 WebSocket 错误
			this.socketTask.onError((error) => {
				console.error('❌ WebSocket: 发生错误', error);
				this.globalData.isWebSocketConnected = false; // 更新连接状态
				this.stopHeartBeat(); // 停止心跳
				this.handleReconnect();
			});
		},

		// 启动心跳机制
		startHeartBeat() {
			this.stopHeartBeat(); // 先清除已有心跳
			this.heartBeatInterval = setInterval(() => {
				if (this.socketTask) {
					this.socketTask.send({
						data: JSON.stringify({ type: 'ping' }),
						success: () => {
							console.log('💓 WebSocket: 心跳消息发送成功');
						},
						fail: (error) => {
							console.error('💔 WebSocket: 心跳消息发送失败', error);
							this.handleReconnect();
						},
					});
				}
			}, this.globalData.heartBeatTime);
		},

		// 停止心跳机制
		stopHeartBeat() {
			if (this.heartBeatInterval) {
				clearInterval(this.heartBeatInterval);
				this.heartBeatInterval = null;
			}
		},

		// 断线重连
		handleReconnect() {
			if (this.reconnectAttempts < this.maxReconnectAttempts) {
				this.reconnectAttempts++;
				console.log(`WebSocket: 尝试第 ${this.reconnectAttempts} 次重连...`);
				setTimeout(() => {
					this.connectWebSocket();
				}, this.reconnectInterval);
			} else {
				console.error('WebSocket: 达到最大重连次数，停止重连');
				uni.showToast({
					title: 'WebSocket 连接失败，请检查网络',
					icon: 'error',
					duration: 3000,
				});
			}
		},

		// 手动关闭 WebSocket
		closeWebSocket() {
			if (this.socket) {
				this.stopHeartBeat(); // 停止心跳
				uni.closeSocket({
					success: () => {
						console.log('WebSocket: 手动关闭成功');
					},
					fail: (error) => {
						console.error('WebSocket: 关闭失败', error);
					},
				});
			}
		}
	}
}
</script>

<style>
/*每个页面公共css */
.uni-tabbar .uni-tabbar__item:nth-child(4) .uni-tabbar__label {
	font-size: 10px !important;
	font-weight: bold !important;
	margin-bottom: 3px !important;
	background: #33d9cd !important;
	color: white !important;
	width: 65px;
	height: 40rpx;
	border-radius: 0px 0px 5px 5px;
}

/* 修改发布选项卡的图标大小 */
.uni-tabbar .uni-tabbar__item:nth-child(4) .uni-tabbar__icon {
	width: 65px !important;
	height: 25px !important;
	background: #06d1cf !important;
	border-radius: 5px 5px 0px 0px;
	margin-bottom: -7rpx;
}

.uni-tabbar .uni-tabbar__item:nth-child(4) .uni-tabbar__icon image,
.uni-tabbar .uni-tabbar__item:nth-child(4) .uni-tabbar__icon img {
	width: 24px !important;
	/* 图片实际宽度 - 保持原始大小或设置您想要的尺寸 */
	height: 24px !important;
	/* 图片实际高度 - 保持原始大小或设置您想要的尺寸 */
	position: relative !important;
	/* 移除可能影响图片尺寸的其他样式 */
	object-fit: contain !important;
	/* 确保图片保持比例 */
}
</style>
