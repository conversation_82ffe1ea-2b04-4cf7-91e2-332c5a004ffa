<template>
    <view class="pin-job-page">
        <!-- 1. 自定义顶部导航栏 (如果需要在所有平台表现一致) -->
        <!-- 如果您使用 uniapp 的 pages.json 配置的默认导航栏，可以注释或删除这部分 -->
        <view class="nav-bar">
            <u-navbar height="44px" title="急招岗位" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>

        <!-- 2. 主体内容区域 -->
        <view class="content-wrapper">
            <!-- 顶部图片/广告位 -->
            <view class="ad-placeholder">
                <!-- 这里可以使用 uniapp 的 image 组件 -->
                <image class="placeholder-icon" src="/static/icon-image.svg" mode="aspectFit"></image>
            </view>

            <!-- 置顶职位 -->
            <view class="section-card">
                <text class="section-title">加急职位</text>
                <!-- 可点击的职位卡片 -->
                <view class="job-card" @click="show = true">
                    <text class="job-info" v-if="geturgentJob">{{ geturgentJob.name }} · {{ geturgentJob.city_name }} · {{
                        geturgentJob.min_salary }} - {{ geturgentJob.max_salary }}</text>
                    <text class="job-info" v-else>请选择您要置顶的职位</text>
                    <view class="change-action">
                        <text class="change-icon">⇄</text> <!-- 刷新图标 -->
                        <text>换绑</text>
                    </view>
                </view>
            </view>

            <!-- 置顶时间 -->
            <view class="section-card">
                <text class="section-title">选择加急效果</text>
                <view class="duration-grid">
                    <view v-for="item in durationOptions" :key="item.id" class="duration-item"
                        :class="{ active: selectedDuration === item.id }" @click="selectDuration(item.id, item)">
                        <text>{{ item.num }}个查看</text>
                        <p>{{ item.day }}天</p>
                    </view>
                </view>
            </view>

            <!-- 购买须知 -->
            <view class="section-card">
                <text class="section-title">购买须知</text>
                <view class="notice-list">
                    <text class="notice-item">1、xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx;</text>
                    <text class="notice-item">2、xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx;</text>
                    <text class="notice-item">3、xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx;</text>
                    <text class="notice-item">4、xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx;</text>
                </view>
            </view>
        </view>

        <!-- 3. 底部支付栏 (固定在页面底部) -->
        <view class="bottom-bar">
            <view class="price-details">
                <text class="price-main-text">{{ currentPrice.points }}积分 / {{ currentPrice.price }}元</text>
                <text class="price-sub-text">购买后{{ currentPrice.day }}天生效</text>
            </view>
            <button class="pay-button" @click="handlePayment">立即支付</button>
        </view>

        <u-popup :show="showPopup" @close="close" @open="open">
            <view class="popup-content">
                <!-- 1. 弹窗标题 (u-popup自带关闭按钮，我们只需标题) -->
                <view class="popup-header">
                    <text class="popup-title">支付金额</text>
                </view>

                <!-- 2. 支付金额 -->
                <view class="price-section">
                    <text class="currency-symbol">¥</text>
                    <text class="amount-text">{{ paymentAmount }}</text>
                </view>

                <!-- 3. 支付方式列表 -->
                <view class="payment-list">
                    <view class="payment-item" v-for="item in paymentOptions" :key="item.id"
                        @click="selectPayment(item.id)">
                        <image src="/static/app/my/wxpay.png" class="payment-icon"></image>

                        <view class="payment-info">
                            <text class="payment-name">{{ item.name }}</text>
                            <!-- 按要求只在积分支付时显示额外信息 -->
                            <text v-if="item.id === 'points'" class="points-balance">
                                剩余 {{ userPoints }} 积分
                            </text>
                        </view>

                        <!-- 选中状态 -->
                        <uni-icons v-if="selectedPayment === item.id" type="checkbox" size="30"
                            color="#07c160"></uni-icons>
                        <uni-icons v-else type="circle" size="30" color="#e0e0e0"></uni-icons>
                    </view>
                </view>

                <!-- 4. 确认支付按钮 -->
                <view class="popup-footer">
                    <button class="confirm-btn" @click="handleConfirmPayment">
                        确认支付 ¥{{ paymentAmount }}
                    </button>
                </view>
            </view>
        </u-popup>

        <u-popup :show="show" @close="closelist" @open="openlist" :round="20" mode="bottom">
            <view class="popup-content">
                <view class="popup-top">
                    <h2>选择置顶职位</h2>
                    <uni-icons @click="show = false" type="closeempty" size="22"></uni-icons>
                </view>
                <scroll-view class="popup-joblist-scroll" v-if="!loding" scroll-y="true"
                    @scrolltolower="onJobListLoadMore" :lower-threshold="100" style="height: 400rpx;">
                    <view class="popup-joblist">
                        <view class="taball" v-for="(item, index) in jobList" :key="index" @click="handleSelect(item)">
                            <uni-icons :type="selectedJobId === item.id ? 'checkbox' : 'circle'"
                                :color="selectedJobId === item.id ? '#02bdc4' : '#dcdfe6'" size="30"></uni-icons>
                            <view style="color: #535353;font-size: 30rpx;color: black;">{{ item.name }} . {{
                                item.city_name }} . {{ item.min_salary }} - {{ item.max_salary }}</view>
                        </view>

                        <!-- 职位列表加载状态提示 -->
                        <view class="job-loading-status">
                            <view v-if="jobListLoading && jobList.length > 0" class="loading-more">
                                <u-loading-icon mode="spinner" color="#02bdc4" size="32"></u-loading-icon>
                                <text class="loading-text">加载中...</text>
                            </view>
                            <view v-else-if="!jobListHasMore && jobList.length > 0" class="no-more">
                                <text>- 没有更多职位了 -</text>
                            </view>
                        </view>
                    </view>
                </scroll-view>
                <view style="text-align: center;" v-else>
                    <u-loading-icon></u-loading-icon>
                    <view style="color: #9a9ca4;">加载中...</view>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
import { positionsApi ,addApi} from "@/utils/api.js"
import { mapGetters, mapActions } from 'vuex';
export default {
    data() {
        return {
            // 职位信息
            jobInfo: {
                title: 'UI设计师',
                location: '邯郸',
                salary: '4-8k'
            },
            // 急招套餐选项 - 将从接口获取
            durationOptions: [],
            // 当前选中的套餐ID
            selectedDuration: null,
            // 当前选中的套餐项
            selectedPackage: null,
            showPopup: false, // 控制弹窗显示
            // paymentAmount: 9.9, // 支付金额 - 改为计算属性
            userPoints: 5200, // 用户剩余积分示例
            selectedPayment: 'alipay', // 默认选中的支付方式
            paymentOptions: [
                {
                    id: 'alipay',
                    name: '支付宝支付',
                    imageUrl: '/static/app/my/zfbpay.png',
                },
                {
                    id: 'wechat',
                    name: '微信支付',
                    imageUrl: '/static/app/my/wxbpay.png'
                },
                {
                    id: 'points',
                    name: '积分支付',
                    imageUrl: '/static/app/my/integralpay.png'
                }
            ],
            show: false,
            jobList: [],
            jobListLoading: false, // 职位列表是否正在加载
            jobListHasMore: true, // 职位列表是否还有更多数据,
            pagelate: 1,
            pagesize: 10,
            loding: false,
            selectedJobId: null,
        };
    },
    computed: {
        // 根据选中的套餐ID动态计算价格
        currentPrice() {
            const selectedOption = this.durationOptions.find(opt => opt.id === this.selectedDuration);
            return selectedOption ? {
                points: selectedOption.points,
                price: selectedOption.price,
                day: selectedOption.day,
                num: selectedOption.num
            } : { points: 'xx', price: 'xx', day: 'xx', num: 'xx' };
        },
        // 计算支付金额
        paymentAmount() {
            const selectedOption = this.durationOptions.find(opt => opt.id === this.selectedDuration);
            return selectedOption ? parseFloat(selectedOption.price) : 0;
        },
        ...mapGetters(['geturgentJob']),
    },
    async onShow(){
        try {
            uni.showLoading({
                title: '加载中...'
            });

            const res = await addApi.getJobUrgentPackage();
            console.log(res, '急招套餐接口返回数据');

            if (res.code === 200 && res.data && res.data.list) {
                // 直接使用接口返回的数据
                this.durationOptions = res.data.list;

                // 设置默认选中第一个套餐
                if (this.durationOptions.length > 0) {
                    this.selectedDuration = this.durationOptions[0].id;
                    this.selectedPackage = this.durationOptions[0];
                }
            } else {
                uni.showToast({
                    title: '获取套餐信息失败',
                    icon: 'none'
                });
            }
        } catch (error) {
            console.error('获取急招套餐失败:', error);
            uni.showToast({
                title: '网络错误，请重试',
                icon: 'none'
            });
        } finally {
            uni.hideLoading();
        }

        console.log(this.geturgentJob);
        this.selectedJobId = this.geturgentJob?.id || '';
    },
    methods: {
        ...mapActions(['urgentJob']),
        async onJobListLoadMore() {
            console.log('职位列表上拉加载更多');
            if (this.jobListLoading || !this.jobListHasMore) {
                console.log('职位列表正在加载或没有更多数据');
                return;
            }

            this.jobListLoading = true;
            this.pagelate++;

            try {
                const params = {
                    page: this.pagelate,
                    size: this.pagesize,
                    status: 4,
                }

                console.log('职位列表加载更多请求参数:', params);
                let res = await positionsApi.postJobList(params);

                if (res.code === 200) {
                    const newJobList = res.data.data || [];
                    this.totallate = res.data.total || 0;

                    // 追加新的职位数据
                    this.jobList = [...this.jobList, ...newJobList];

                    // 判断是否还有更多数据
                    this.jobListHasMore = newJobList.length >= this.pagesize && this.jobList.length < this.totallate;

                    console.log(`职位列表加载更多完成: 当前${this.jobList.length}条，总共${this.totallate}条，还有更多: ${this.jobListHasMore}`);
                    uni.hideLoading()
                } else {
                    uni.hideLoading()
                    throw new Error(res.msg || '获取职位数据失败');
                }
            } catch (error) {
                console.error('职位列表加载更多失败:', error);
                this.pagelate--; // 加载失败时回退页码
                uni.showToast({
                    title: '加载失败',
                    icon: 'none',
                    duration: 1500
                });
            } finally {
                this.jobListLoading = false;
            }
        },
        handleSelect(selectedItem) {
            console.log("用户选择了:", selectedItem);
            this.selectedJobId = selectedItem.id;
            this.urgentJob(selectedItem);

            // 2. 关闭弹窗
            this.show = false;
            // 触发一个 'confirm' 事件，并将选中的数据作为参数传递给父组件
            // this.$emit('confirm', selectedItem);
        },
        async openlist() {
            this.loding = true
            // 重置职位列表分页状态
            this.pagelate = 1
            this.jobListHasMore = true

            const params = {
                page: this.pagelate,
                size: this.pagesize,
                status: 4,
            }
            let res = await positionsApi.postJobList(params)
            this.jobList = res.data.data || []
            this.totallate = res.data.total || 0

            // 判断是否还有更多职位数据
            this.jobListHasMore = this.jobList.length >= this.pagesize && this.jobList.length < this.totallate

            this.loding = false
            uni.hideLoading()
        },
        closelist() {
            this.show = false
        },
        // 返回上一页
        goBack() {
            uni.navigateBack();
        },
        // 点击“换绑”职位
        handleChangeJob() {
            // 提示用户可以切换职位
            uni.showToast({
                title: '切换置顶职位',
                icon: 'none'
            });
            console.log('触发了切换职位事件');
            // 此处可以添加跳转到职位选择页面的逻辑
            // uni.navigateTo({ url: '/pages/job-list/index' });
        },
        // 选择急招套餐
        selectDuration(packageId, packageItem) {
            this.selectedDuration = packageId;
            this.selectedPackage = packageItem;
            console.log(`已选择急招套餐:`, packageItem);
        },
        // 点击“立即支付”
        handlePayment() {
            this.showPopup = true;
        },
        close() {
            this.showPopup = false;
        },
        open() {
            this.showPopup = true;
        },
        selectPayment(id) {
            this.selectedPayment = id;
        },
        // 点击最终的确认支付按钮
        handleConfirmPayment() {
            console.log(`准备支付 ${this.paymentAmount} 元`);
            console.log(`选择的支付方式: ${this.selectedPayment}`);

            // 显示一个加载中的提示
            uni.showLoading({
                title: '正在处理...'
            });

            // 模拟支付请求
            setTimeout(() => {
                uni.hideLoading();

                // 关闭弹窗
                this.showPopup = false;

                // 显示支付成功提示
                uni.showToast({
                    title: '支付成功',
                    icon: 'success'
                });

                // 在这里可以进行页面跳转或其他业务逻辑
                // uni.navigateTo({ url: '/pages/order-success/index' });

            }, 1500); // 模拟1.5秒的网络请求
        }
    }
}
</script>

<style scoped lang="scss">
/* 使用 rpx 作为单位以适应不同屏幕尺寸，px 也可以 */

.pin-job-page {
    background-color: #f7f8fa;
    // min-height: 100vh;
    padding-bottom: 180rpx;
    /* 为底部支付栏留出空间 */
}

/* --- 自定义导航栏 --- */
.nav-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 44px;
    /* 标准导航栏高度 */
    background-color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1rpx solid #f2f2f2;
    z-index: 100;
    /* #ifdef H5 */
    top: 0;
    /* H5 平台没有状态栏 */
    /* #endif */
    /* #ifndef H5 */
    top: var(--status-bar-height);
    /* 其他平台需要考虑状态栏高度 */
    /* #endif */
}

.back-arrow {
    position: absolute;
    left: 30rpx;
    font-size: 40rpx;
    color: #333;
    padding: 10rpx;
}

.nav-title {
    font-size: 34rpx;
    font-weight: 600;
    color: #333333;
}

/* --- 主体内容 --- */
.content-wrapper {
    padding: 24rpx;
    /* 顶部内边距 = 导航栏高度 + 一些间距 */
    padding-top: calc(44px + 24rpx);
    /* #ifndef H5 */
    padding-top: calc(44px + var(--status-bar-height) + 24rpx);
    /* #endif */
}

.ad-placeholder {
    height: 240rpx;
    background-color: #ebebeb;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    /* margin-bottom: 30rpx; */
    border-bottom: 1px solid #ccc;
    width: 100%;
}

.placeholder-icon {
    width: 64rpx;
    height: 64rpx;
    opacity: 0.5;
}

/* --- 通用卡片样式 --- */
.section-card {
    background-color: #ffffff;
    padding: 30rpx;
    border-radius: 16rpx;
    margin-bottom: 30rpx;
}

.section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 30rpx;
    display: block;
    /* text 标签默认为 inline，设为 block 方便控制 margin */
}

/* --- 置顶职位卡片 --- */
.job-card {
    background-color: #e0f8f4;
    border: 1rpx solid #4fd1c5;
    border-radius: 16rpx;
    padding: 24rpx 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
}

.job-card:active {
    opacity: 0.8;
}

.job-info {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
}

.change-action {
    display: flex;
    align-items: center;
    color: #4fd1c5;
    font-size: 26rpx;
}

.change-icon {
    font-size: 32rpx;
    margin-right: 8rpx;
}

.bolt-icon {
    font-size: 32rpx;
    margin-left: 8rpx;
    color: #f7b731;
}

/* --- 置顶时间 --- */
.duration-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
}

.duration-item {
    flex: 1 1 calc(33.333% - 20rpx);
    /* 实现三列布局 */
    box-sizing: border-box;
    text-align: center;
    padding: 24rpx 0;
    border-radius: 12rpx;
    background-color: #f7f7f7;
    border: 1rpx solid #eeeeee;
    font-size: 30rpx;
    color: #333;
    transition: all 0.2s ease;
}

.duration-item:active {
    opacity: 0.8;
}

.duration-item.active {
    background-color: #e0f8f4;
    border-color: #4fd1c5;
    color: #00876c;
    font-weight: 600;
}

/* 调整最后两个元素的宽度，使其充满一行 */
.duration-item:nth-child(4),
.duration-item:nth-child(5) {
    flex: 1 1 calc(50% - 20rpx);
}

/* --- 购买须知 --- */
.notice-list {
    font-size: 26rpx;
    color: #999999;
    line-height: 1.6;
}

.notice-item {
    display: block;
    margin-bottom: 10rpx;
}

/* --- 底部支付栏 --- */
.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #ffffff;
    padding: 20rpx 30rpx;
    padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
    /* 适配 iPhone X 等 */
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 1rpx solid #f2f2f2;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.03);
}

.price-details {
    display: flex;
    flex-direction: column;
}

.price-main-text {
    font-size: 32rpx;
    color: #ff5500;
    font-weight: bold;
}

.price-sub-text {
    font-size: 24rpx;
    color: #999999;
    margin-top: 4rpx;
}

.pay-button {
    background: linear-gradient(90deg, #ff8c42, #ff5500);
    color: #ffffff;
    border: none;
    border-radius: 40rpx;
    padding: 0 60rpx;
    height: 80rpx;
    line-height: 80rpx;
    font-size: 30rpx;
    font-weight: bold;
    margin: 0;
    /* 重置按钮默认margin */
}

.pay-button::after {
    border: none;
    /* 去除 uniapp 按钮默认边框 */
}


.page-container {
    padding: 100rpx 40rpx;
    text-align: center;
}

.content {
    .price-display {
        font-size: 48rpx;
        font-weight: bold;
        display: block;
        margin-bottom: 40rpx;
    }
}

/* 弹窗内部样式 */
.popup-content {
    padding: 20rpx 0;
}

.popup-header {
    text-align: center;
    padding: 20rpx 0;
    position: relative;

    .popup-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #303133;
    }
}

.price-section {
    text-align: center;
    padding: 40rpx 0;

    .currency-symbol {
        font-size: 40rpx;
        font-weight: bold;
        color: #303133;
    }

    .amount-text {
        font-size: 72rpx;
        font-weight: bold;
        color: #303133;
        margin-left: 8rpx;
    }
}

.payment-list {
    padding: 0 40rpx;
}

.payment-item {
    display: flex;
    align-items: center;
    padding: 30rpx 0;
    border-bottom: 1rpx solid #f5f5f5;

    &:last-child {
        border-bottom: none;
    }

    .payment-icon {
        width: 48rpx;
        height: 48rpx;
        margin-right: 24rpx;
    }

    .payment-info {
        flex: 1;
        display: flex;
        flex-direction: column;

        .payment-name {
            font-size: 30rpx;
            color: #303133;
        }

        .points-balance {
            font-size: 24rpx;
            color: #909399;
            margin-top: 4rpx;
        }
    }
}

.popup-footer {
    padding: 40rpx;

    .confirm-btn {
        height: 90rpx;
        line-height: 90rpx;
        border-radius: 45rpx;
        font-size: 32rpx;
        font-weight: 500;
        color: #ffffff;
        background: #00c8a0;
        border: none;
        background: linear-gradient(90deg, #00d2af, #00c8a0);

        &::after {
            border: none;
        }

        &:active {
            opacity: 0.8;
        }
    }
}

.popup-content {
    height: 800rpx;
    padding: 30rpx 20rpx;
    background-color: #f6f7f9;

    .popup-top {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .code-btn::after {
        border: none !important;
    }

    // .popup-taball {
    //     display: flex;
    //     height: 80rpx;
    //     display: flex;
    //     align-items: center;


    // }

    .taball {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 40rpx 30rpx;
        background-color: white;
        border-radius: 20rpx;
        margin-top: 20rpx;

        .code-btn {
            width: 200rpx;
            margin-left: 30rpx;
            border: none !important;
            background-color: #f6f7f9;
            border-radius: 20rpx;
            color: #989fa4;
            font-weight: 600;
        }

        .activeJob {
            background-color: #02bdc4 !important;
            color: white;
            /* 高亮边框 */
        }
    }

    .popup-botom {
        display: flex;
        padding: 30rpx 0;
        color: black;
    }

    .popup-joblist-scroll {
        width: 100%;
        height: calc(100% - 100rpx) !important;
        margin-top: 30rpx;
    }
    // 职位列表加载状态样式
    .job-loading-status {
        padding: 30rpx 0;
        text-align: center;

        .loading-more {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .loading-text {
                margin-top: 15rpx;
                font-size: 26rpx;
                color: #999;
            }
        }

        .no-more {
            padding: 15rpx 0;
            font-size: 26rpx;
            color: #999;
            text-align: center;
        }
    }
}
</style>