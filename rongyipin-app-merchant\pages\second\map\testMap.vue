<template>
    <view class="container">
        <u-navbar 
            :autoBack="true" 
            title="地图测试" 
            bgColor="#fff" 
            fixed 
            safeAreaInsetTop 
            placeholder
        ></u-navbar>
        
        <view class="map-wrapper">
            <map
                id="testMap"
                class="map"
                :longitude="longitude"
                :latitude="latitude"
                :scale="scale"
                show-location
                @regionchange="onRegionChange"
                @error="onMapError"
                @updated="onMapUpdated"
            >
                <cover-view class="map-info">
                    <text>经度: {{ longitude }}</text>
                    <text>纬度: {{ latitude }}</text>
                    <text>缩放: {{ scale }}</text>
                </cover-view>
            </map>
        </view>
        
        <view class="controls">
            <button @click="getCurrentLocation">获取当前位置</button>
            <button @click="changeLocation">切换到北京</button>
            <button @click="changeLocation2">切换到上海</button>
        </view>
        
        <view class="debug-info">
            <text>地图状态: {{ mapStatus }}</text>
            <text>错误信息: {{ errorMsg }}</text>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            longitude: 116.397128, // 北京
            latitude: 39.916527,
            scale: 16,
            mapStatus: '初始化中...',
            errorMsg: ''
        }
    },
    
    onLoad() {
        this.mapStatus = '页面加载完成';
        this.getCurrentLocation();
    },
    
    methods: {
        getCurrentLocation() {
            this.mapStatus = '正在定位...';
            uni.getLocation({
                type: 'gcj02',
                success: (res) => {
                    this.latitude = res.latitude;
                    this.longitude = res.longitude;
                    this.mapStatus = '定位成功';
                    console.log('定位成功:', res);
                },
                fail: (err) => {
                    this.errorMsg = '定位失败: ' + JSON.stringify(err);
                    this.mapStatus = '定位失败';
                    console.error('定位失败:', err);
                }
            });
        },
        
        changeLocation() {
            this.longitude = 116.397128; // 北京
            this.latitude = 39.916527;
            this.mapStatus = '切换到北京';
        },
        
        changeLocation2() {
            this.longitude = 121.473701; // 上海
            this.latitude = 31.230416;
            this.mapStatus = '切换到上海';
        },
        
        onRegionChange(e) {
            console.log('地图区域变化:', e);
            this.mapStatus = '地图可以正常交互';
        },
        
        onMapError(e) {
            console.error('地图错误:', e);
            this.errorMsg = '地图错误: ' + JSON.stringify(e);
            this.mapStatus = '地图加载失败';
        },
        
        onMapUpdated(e) {
            console.log('地图更新:', e);
            this.mapStatus = '地图更新成功';
        }
    }
}
</script>

<style lang="scss" scoped>
.container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.map-wrapper {
    flex: 1;
    position: relative;
    margin: 20rpx;
    border: 2rpx solid #ddd;
    border-radius: 12rpx;
    overflow: hidden;
}

.map {
    width: 100%;
    height: 100%;
}

.map-info {
    position: absolute;
    top: 20rpx;
    left: 20rpx;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 20rpx;
    border-radius: 8rpx;
    font-size: 24rpx;
    
    text {
        display: block;
        margin-bottom: 10rpx;
    }
}

.controls {
    padding: 30rpx;
    display: flex;
    gap: 20rpx;
    flex-wrap: wrap;
    
    button {
        flex: 1;
        min-width: 200rpx;
        height: 80rpx;
        background-color: #10d2c3;
        color: white;
        border: none;
        border-radius: 8rpx;
        font-size: 28rpx;
    }
}

.debug-info {
    padding: 30rpx;
    background-color: #f5f5f5;
    
    text {
        display: block;
        margin-bottom: 10rpx;
        font-size: 24rpx;
        color: #666;
    }
}
</style>
