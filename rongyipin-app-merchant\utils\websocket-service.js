/**
 * WebSocket 服务管理类
 * 用于全局监听聊天消息更新和 /message/list 接口变化
 */
class WebSocketService {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.reconnectTimer = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 3000;
        this.listeners = new Map(); // 存储事件监听器
        this.globalListeners = []; // 存储全局监听器
        this.token = '';
        this.serverUrl = 'ws://8.130.152.121:82/ws'; // 根据记忆中的服务器地址
    }

    /**
     * 初始化 WebSocket 连接
     */
    init() {
        try {
            // 获取token
            this.token = uni.getStorageSync('token') || '';
            console.log('WebSocket: 获取到的token:', this.token ? '存在' : '不存在');
            
            if (!this.token) {
                console.error('WebSocket: 未找到token，无法建立连接');
                return false;
            }

            // 重置重连计数
            this.reconnectAttempts = 0;
            
            this.connect();
            return true;
        } catch (error) {
            console.error('WebSocket初始化失败:', error);
            return false;
        }
    }

    /**
     * 建立 WebSocket 连接
     */
    connect() {
        if (this.socket && this.isConnected) {
            console.log('WebSocket: 连接已存在');
            return;
        }

        // 清理之前的连接
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }

        console.log('WebSocket: 开始连接...', this.serverUrl);
        console.log('WebSocket: 连接配置:', {
            url: this.serverUrl,
            hasToken: !!this.token,
            tokenLength: this.token ? this.token.length : 0
        });

        // 使用 uni-app 的 WebSocket API
        this.socket = uni.connectSocket({
            url: this.serverUrl,
            success: () => {
                console.log('WebSocket: 连接请求发送成功');
            },
            fail: (error) => {
                console.error('WebSocket: 连接请求失败', error);
                this.handleReconnect();
            }
        });

        // 监听连接打开
        this.socket.onOpen(() => {
            console.log('WebSocket: 连接已建立');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            
            // 打印当前监听的接口信息
            this.printListeningInterfaces();
            
            // 触发连接成功事件
            this.emit('connected');
        });

        // 监听消息
        this.socket.onMessage((res) => {
            this.handleMessage(res.data);
        });

        // 监听连接关闭
        this.socket.onClose((res) => {
            console.log('WebSocket: 连接已关闭', res);
            this.isConnected = false;
            this.handleReconnect();
        });

        // 监听连接错误
        this.socket.onError((error) => {
            console.error('WebSocket: 连接错误', error);
            this.isConnected = false;
            this.handleReconnect();
        });
    }

    /**
     * 处理接收到的消息
     */
    handleMessage(data) {
        try {
            console.log('WebSocket: 收到原始消息', data);
            
            // 根据记忆，需要解析JSON数据
            let jsonData;
            if (typeof data === 'string') {
                // 查找JSON开始位置
                const jsonStart = Math.max(data.indexOf('['), data.indexOf('{'));
                if (jsonStart !== -1) {
                    const jsonStr = data.substring(jsonStart);
                    jsonData = JSON.parse(jsonStr);
                } else {
                    jsonData = JSON.parse(data);
                }
            } else {
                jsonData = data;
            }

            console.log('WebSocket: 解析后的消息', jsonData);

            // 触发全局监听器 - 无论在哪个页面都会执行
            this.triggerGlobalListeners(jsonData);

            // 根据消息类型处理
            if (jsonData.event) {
                this.emit(jsonData.event, jsonData.data);
            }

            // 如果是聊天相关消息，触发聊天列表更新
            if (jsonData.event === 'private_msg' || jsonData.event === 'chat_update' || jsonData.event === 'message_list_update') {
                this.emit('chatListUpdate', jsonData.data);
                console.log('111'); // 全局监听 - 无论在哪个页面都打印
            }

            // 特别监听 /message/list 接口变化
            if (jsonData.api === '/message/list' || jsonData.endpoint === '/message/list' || 
                jsonData.path === '/message/list' || jsonData.url === '/message/list') {
                console.log('111'); // 监听到 /message/list 接口变化
                console.log('检测到 /message/list 接口变化:', jsonData);
                this.emit('messageListUpdate', jsonData.data);
            }

            // 监听任何包含 message 或 list 的事件
            if (jsonData.event && (jsonData.event.includes('message') || jsonData.event.includes('list'))) {
                console.log('111'); // 监听到消息列表相关事件
                console.log('检测到消息列表相关事件:', jsonData.event, jsonData);
            }

            // 监听任何数据变化（通用监听）
            if (jsonData.type === 'data_change' || jsonData.action === 'update' || jsonData.method === 'POST') {
                console.log('111'); // 监听到数据变化
                console.log('检测到数据变化:', jsonData);
            }

        } catch (error) {
            console.error('WebSocket: 消息解析失败', error, data);
        }
    }

    /**
     * 发送消息
     */
    send(data) {
        if (!this.isConnected || !this.socket) {
            console.error('WebSocket: 连接未建立，无法发送消息');
            return false;
        }

        try {
            const message = typeof data === 'string' ? data : JSON.stringify(data);
            this.socket.send({
                data: message,
                success: () => {
                    console.log('WebSocket: 消息发送成功', data);
                },
                fail: (error) => {
                    console.error('WebSocket: 消息发送失败', error);
                }
            });
            return true;
        } catch (error) {
            console.error('WebSocket: 发送消息异常', error);
            return false;
        }
    }

    /**
     * 处理重连
     */
    handleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('WebSocket: 重连次数已达上限，停止重连');
            return;
        }

        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
        }

        this.reconnectAttempts++;
        console.log(`WebSocket: ${this.reconnectInterval}ms后进行第${this.reconnectAttempts}次重连`);

        this.reconnectTimer = setTimeout(() => {
            this.connect();
        }, this.reconnectInterval);
    }

    /**
     * 添加事件监听器
     */
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }

    /**
     * 移除事件监听器
     */
    off(event, callback) {
        if (this.listeners.has(event)) {
            const callbacks = this.listeners.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    /**
     * 触发事件
     */
    emit(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`WebSocket: 事件${event}回调执行失败`, error);
                }
            });
        }
    }

    /**
     * 添加全局监听器 - 无论在哪个页面都会执行
     */
    addGlobalListener(callback) {
        this.globalListeners.push(callback);
        console.log('WebSocket: 添加全局监听器，当前数量:', this.globalListeners.length);
    }

    /**
     * 移除全局监听器
     */
    removeGlobalListener(callback) {
        const index = this.globalListeners.indexOf(callback);
        if (index > -1) {
            this.globalListeners.splice(index, 1);
            console.log('WebSocket: 移除全局监听器，当前数量:', this.globalListeners.length);
        }
    }

    /**
     * 触发全局监听器
     */
    triggerGlobalListeners(data) {
        this.globalListeners.forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error('WebSocket: 全局监听器执行失败', error);
            }
        });
    }
