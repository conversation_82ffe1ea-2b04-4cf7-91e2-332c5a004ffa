<template>
	<view class="refresh-page-container">
		<!-- 1. 自定义导航栏 -->
		<view class="nav-bar">
			<u-navbar height="44px" title="职位刷新" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            </u-navbar>
		</view>

		<!-- 2. 主体内容区域 -->
		<view class="content-wrapper">
			<!-- 顶部图片占位符 -->
			<view class="ad-placeholder">
				<u-icon name="photo" color="#c0c4cc" size="64"></u-icon>
			</view>

			<!-- 刷新卡类型 -->
			<view class="section-container">
				<text class="section-title">刷新卡类型</text>
				<view class="card-list">
					<view
						v-for="item in cardOptions"
						:key="item.id"
						class="card-item"
						:class="{ active: selectedCardId === item.id }"
						@click="selectCard(item.id)"
					>
						<!-- 左侧的单选图标和卡片名称 -->
						<view class="card-info">
							<uni-icons
								class="radio-icon"
								:type="selectedCardId === item.id ? 'checkbox' : 'circle'"
								:color="selectedCardId === item.id ? '#2979ff' : '#dcdfe6'"
								size="30"
							></uni-icons>
							<text class="card-name">{{ item.name }}</text>
						</view>
						<!-- 中间的刷新次数 -->
						<text class="card-count">{{ item.num }}次</text>

						<!-- 右侧的价格 -->
						<text class="card-price">{{ item.points }}积分 / {{ item.price }}元</text>
					</view>
				</view>
			</view>

			<!-- 购买须知 -->
			<view class="section-container">
				<text class="section-title">购买须知</text>
				<view class="notice-list">
					<text class="notice-item">1、xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx;</text>
					<text class="notice-item">2、xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx;</text>
					<text class="notice-item">3、xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx;</text>
					<text class="notice-item">4、xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx;</text>
				</view>
			</view>
		</view>

		<!-- 3. 底部支付栏 -->
		<view class="bottom-bar">
			<view class="price-details">
				<text class="price-main">{{ selectedCard ? selectedCard.points : 0 }}积分 / {{ selectedCard ? selectedCard.price : 0 }}元</text>
				<text class="price-sub">购买后立即生效</text>
			</view>
			<button class="pay-button" @click="showPopup = true">立即支付</button>
		</view>

        <u-popup :show="showPopup" @close="close" @open="open">
            <view class="popup-content">
                <!-- 1. 弹窗标题 (u-popup自带关闭按钮，我们只需标题) -->
                <view class="popup-header">
                    <text class="popup-title">支付金额</text>
                </view>

                <!-- 2. 支付金额 -->
                <view class="price-section">
                    <text class="currency-symbol">¥</text>
                    <text class="amount-text">{{ paymentAmount }}</text>
                </view>

                <!-- 3. 支付方式列表 -->
                <view class="payment-list">
                    <view class="payment-item" v-for="item in paymentOptions" :key="item.id"
                        @click="selectPayment(item.id)">
                        <image src="/static/app/my/wxpay.png" class="payment-icon"></image>

                        <view class="payment-info">
                            <text class="payment-name">{{ item.name }}</text>
                            <!-- 按要求只在积分支付时显示额外信息 -->
                            <text v-if="item.id === 'points'" class="points-balance">
                                剩余 {{ userPoints }} 积分
                            </text>
                        </view>

                        <!-- 选中状态 -->
                        <uni-icons v-if="selectedPayment === item.id" type="checkbox" size="30"
                            color="#07c160"></uni-icons>
                        <uni-icons v-else type="circle" size="30" color="#e0e0e0"></uni-icons>
                    </view>
                </view>

                <!-- 4. 确认支付按钮 -->
                <view class="popup-footer">
                    <button class="confirm-btn" @click="handleConfirmPayment">
                        确认支付 ¥{{ paymentAmount }}
                    </button>
                </view>
            </view>
        </u-popup>
	</view>
</template>

<script>
import {addApi} from "@/utils/api.js"
export default {
	data() {
		return {
			// 卡片选项数据 - 将从接口获取
			cardOptions: [],
            // paymentAmount: 9.9, // 支付金额 - 改为计算属性
            userPoints: 5200, // 用户剩余积分示例
            selectedPayment: 'alipay', // 默认选中的支付方式
            paymentOptions: [
                {
                    id: 'alipay',
                    name: '支付宝支付',
                    imageUrl: '/static/app/my/zfbpay.png',
                },
                {
                    id: 'wechat',
                    name: '微信支付',
                    imageUrl: '/static/app/my/wxbpay.png'
                },
                {
                    id: 'points',
                    name: '积分支付',
                    imageUrl: '/static/app/my/integralpay.png'
                }
            ],
			// 当前选中的卡片ID
			selectedCardId: null,
            showPopup:false
		};
	},
	computed: {
		// 计算属性：根据selectedCardId找到完整的选中卡片对象
		selectedCard() {
			return this.cardOptions.find(card => card.id === this.selectedCardId);
		},
		// 计算支付金额
		paymentAmount() {
			return this.selectedCard ? parseFloat(this.selectedCard.price) : 0;
		}
	},
	async onShow(){
		try {
			uni.showLoading({
				title: '加载中...'
			});

			const res = await addApi.getJobRefreshPackage();
			console.log(res, '职位刷新套餐接口返回数据');

			if (res.code === 200) {
				// 直接使用接口返回的数据
				this.cardOptions = res.data.list;
				console.log(this.cardOptions, '刷新套餐数据');

				// 设置默认选中第一个套餐
				if (this.cardOptions.length > 0) {
					this.selectedCardId = this.cardOptions[0].id;
				}
			} else {
				uni.showToast({
					title: '获取套餐信息失败',
					icon: 'none'
				});
			}
		} catch (error) {
			console.error('获取职位刷新套餐失败:', error);
			uni.showToast({
				title: '网络错误，请重试',
				icon: 'none'
			});
		} finally {
			uni.hideLoading();
		}
	},
	methods: {
        selectPayment(id) {
            this.selectedPayment = id;
        },
        // 点击最终的确认支付按钮
        handleConfirmPayment() {
            console.log(`准备支付 ${this.paymentAmount} 元`);
            console.log(`选择的支付方式: ${this.selectedPayment}`);

            // 显示一个加载中的提示
            uni.showLoading({
                title: '正在处理...'
            });

            // 模拟支付请求
            setTimeout(() => {
                uni.hideLoading();

                // 关闭弹窗
                this.showPopup = false;

                // 显示支付成功提示
                uni.showToast({
                    title: '支付成功',
                    icon: 'success'
                });

                // 在这里可以进行页面跳转或其他业务逻辑
                // uni.navigateTo({ url: '/pages/order-success/index' });

            }, 1500); // 模拟1.5秒的网络请求
        },
        close(){
            this.showPopup = false;
        },
        open() {
            this.showPopup = true;
        },
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		// 选择卡片
		selectCard(cardId) {
			this.selectedCardId = cardId;
		},
		// 处理支付
		handlePayment() {
			console.log('准备支付:', this.selectedCard);
			uni.showToast({
				title: `已选择: ${this.selectedCard.name}`,
				icon: 'none'
			});
			// 此处可以接续调用支付弹窗或支付API
		}
	}
};
</script>

<style lang="scss" scoped>
.refresh-page-container {
	background-color: #f7f8fa;
	// min-height: 100vh;
	padding-bottom: 180rpx; // 为底部支付栏留出空间
}

/* --- 自定义导航栏 --- */
.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	height: 44px;
	background-color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	border-bottom: 1rpx solid #f2f2f2;
	z-index: 100;
	/* #ifndef H5 */
	top: var(--status-bar-height);
	/* #endif */
}
.back-arrow {
	position: absolute;
	left: 30rpx;
	font-size: 40rpx;
	color: #333;
	padding: 10rpx;
}
.nav-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333333;
}

/* --- 主体内容 --- */
.content-wrapper {
	padding: 24rpx;
	padding-top: calc(44px + 24rpx);
	/* #ifndef H5 */
	padding-top: calc(44px + var(--status-bar-height) + 24rpx);
	/* #endif */
}

.ad-placeholder {
	height: 240rpx;
	background-color: #ebebeb;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 30rpx;
}

/* --- 通用区块样式 --- */
.section-container {
	background-color: #ffffff;
	padding: 30rpx;
	border-radius: 16rpx;
	margin-bottom: 30rpx;
}
.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 20rpx;
	display: block;
}

/* --- 刷新卡片列表 --- */
.card-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx; // 使用gap属性方便地设置间距
}
.card-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 30rpx;
	border: 2rpx solid #e5e5e5;
	border-radius: 16rpx;
	transition: all 0.2s ease-in-out;
	cursor: pointer;

	// 选中状态的样式
	&.active {
		border-color: #2979ff;
		background-color: #ecf5ff;
	}
}

.card-info {
	display: flex;
	align-items: center;
	.radio-icon {
		// 让图标和文字之间有间距
		margin-right: 20rpx;
	}
	.card-name {
		font-size: 30rpx;
		color: #303133;
		font-weight: 500;
	}
}

.card-count {
	font-size: 28rpx;
	color: #606266;
}

.card-price {
	font-size: 28rpx;
	color: #303133;
}


/* --- 购买须知 --- */
.notice-list {
	font-size: 26rpx;
	color: #909399;
	line-height: 1.7;
	.notice-item {
		display: block;
		margin-bottom: 10rpx;
        overflow: hidden;
	}
}


/* --- 底部支付栏 --- */
.bottom-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #ffffff;
	padding: 20rpx 30rpx;
	padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-top: 1rpx solid #f2f2f2;
	box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.03);
}
.price-details {
	display: flex;
	flex-direction: column;
	.price-main {
		font-size: 32rpx;
		color: #ff5500;
		font-weight: bold;
	}
	.price-sub {
		font-size: 24rpx;
		color: #999999;
		margin-top: 4rpx;
	}
}
.pay-button {
    background: linear-gradient(90deg, #ff8c42, #ff5500);
    color: #ffffff;
    border: none;
    border-radius: 40rpx;
    padding: 0 60rpx;
    height: 80rpx;
    line-height: 80rpx;
    font-size: 30rpx;
    font-weight: bold;
    margin: 0;
    /* 重置按钮默认margin */
}

.popup-content {
    padding: 20rpx 0;
}

.popup-header {
    text-align: center;
    padding: 20rpx 0;
    position: relative;

    .popup-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #303133;
    }
}

.price-section {
    text-align: center;
    padding: 40rpx 0;

    .currency-symbol {
        font-size: 40rpx;
        font-weight: bold;
        color: #303133;
    }

    .amount-text {
        font-size: 72rpx;
        font-weight: bold;
        color: #303133;
        margin-left: 8rpx;
    }
}

.payment-list {
    padding: 0 40rpx;
}

.payment-item {
    display: flex;
    align-items: center;
    padding: 30rpx 0;
    border-bottom: 1rpx solid #f5f5f5;

    &:last-child {
        border-bottom: none;
    }

    .payment-icon {
        width: 48rpx;
        height: 48rpx;
        margin-right: 24rpx;
    }

    .payment-info {
        flex: 1;
        display: flex;
        flex-direction: column;

        .payment-name {
            font-size: 30rpx;
            color: #303133;
        }

        .points-balance {
            font-size: 24rpx;
            color: #909399;
            margin-top: 4rpx;
        }
    }
}

.popup-footer {
    padding: 40rpx;

    .confirm-btn {
        height: 90rpx;
        line-height: 90rpx;
        border-radius: 45rpx;
        font-size: 32rpx;
        font-weight: 500;
        color: #ffffff;
        background: #00c8a0;
        border: none;
        background: linear-gradient(90deg, #00d2af, #00c8a0);

        &::after {
            border: none;
        }

        &:active {
            opacity: 0.8;
        }
    }
}
</style>