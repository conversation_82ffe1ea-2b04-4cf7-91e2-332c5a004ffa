<template>
    <view class="settings-container">
        <!-- 状态栏和导航栏 -->
        <view class="header">
            <u-navbar height="44px" title="设置" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed >
            </u-navbar>
        </view>

        <!-- 设置列表 -->
        <view class="settings-listlate" >
            <!-- 账号与安全 -->
            <view class="settings-item" @click="emitpassword">
                <text class="item-label">账号与安全</text>
                <text class="item-arrow"><uni-icons type="right" size="16" color="#d2d2d2"></uni-icons></text>
            </view>

            <!-- 我的联系方式 -->
            <!-- <view class="settings-item">
                <view class="item-left">
                    <text class="item-label">我的联系方式</text>
                    <view class="red-dot"></view>
                </view>
                <text class="item-arrow"><uni-icons type="right" size="16" color="#d2d2d2"></uni-icons></text>
            </view> -->


        </view>

        <view class="settings-list" style="margin-top: 20rpx;">
            <!-- 系统通知 -->
            <view class="settings-item">
                <text class="item-label">系统通知</text>
                <text class="item-arrow"><uni-icons type="right" size="16" color="#d2d2d2"></uni-icons></text>
            </view>

            <!-- 微信通知 -->
            <view class="settings-item">
                <view class="item-left">
                    <text class="item-label">微信通知</text>
                </view>
                <view class="item-right">
                    <text class="status-text">未开启</text>
                    <text class="item-arrow"><uni-icons type="right" size="16" color="#d2d2d2"></uni-icons></text>
                </view>
            </view>

            <!-- 邮件通知 -->
            <view class="settings-item">
                <view class="item-left">
                    <text class="item-label">邮件通知</text>
                </view>
                <view class="item-right">
                    <text class="status-text">未开启</text>
                    <text class="item-arrow"><uni-icons type="right" size="16" color="#d2d2d2"></uni-icons></text>
                </view>
            </view>

            <!-- 清理缓存 -->
            <view class="settings-item">
                <text class="item-label">清理缓存</text>
                <view class="item-right">
                    <text class="cache-size">0.0M</text>
                    <text class="item-arrow"><uni-icons type="right" size="16" color="#d2d2d2"></uni-icons></text>
                </view>
            </view>


        </view>
        <view class="settings-list" style="margin-top: 20rpx;">
            <!-- 关于我们 -->
            <view class="settings-item">
                <text class="item-label">关于我们</text>
                <text class="item-arrow"><uni-icons type="right" size="16" color="#d2d2d2"></uni-icons></text>
            </view>
        </view>
        <!-- 退出登录按钮 -->
        <view class="logout-btn">
            <text @click="show = true">退出登录</text>

            <u-modal :show="show" :content="content" cancelText="取消" confirmText="确认" @confirm="confirm"
                @cancel="cancel" showCancelButton></u-modal>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            show: false,
            title: '标题',
            content: '确定退出登录吗'
        }
    },
    methods: {
        handleBack() {
            uni.navigateBack();
        },
        confirm() {
            uni.removeStorageSync('token');
            // 其他需要清除的数据
            uni.removeStorageSync('userInfo');

            // 重定向到登录页
            uni.reLaunch({
                url: '/pages/login/login'
            });
        },
        emitpassword(){
            uni.navigateTo({
                 url: './emitpassword/index'
            });
        },
        cancel() {
            this.show = false;
        }
    }
}
</script>

<style>
::v-deep .u-tabbar__content__item-wrapper{
    margin-bottom: 60rpx;
}
.header {
    height: 88rpx;
    /* display: flex;
    justify-content: space-between;
    align-items: center; */
    background-color: #fff;

    .navbar {
        height: 100%;
    }
}

.settings-container {
    min-height: 100vh;
    background-color: #f9f8fd;
}

/* 导航栏样式 */
.nav-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 88rpx 32rpx 20rpx;
    background-color: #fff;
}

.nav-left {
    width: 80rpx;
}

.back-icon {
    font-size: 36rpx;
    color: #333;
}

.nav-title {
    font-size: 34rpx;
    font-weight: 500;
    flex: 1;
    text-align: center;
}

.nav-right {
    width: 80rpx;
}

/* 设置列表样式 */
.settings-list {
    margin-top: 20rpx;
    background-color: #fff;
}

.settings-listlate {
    margin-top: 88rpx;
    background-color: #fff;
}

.settings-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    background: #fff;
    border-bottom: 1rpx solid #f5f5f5;
}

.item-left {
    display: flex;
    align-items: center;
}

.item-label {
    font-size: 30rpx;
    color: #333;
    font-weight: 400;
}

.red-dot {
    width: 12rpx;
    height: 12rpx;
    background-color: #ff4d4f;
    border-radius: 50%;
    margin-left: 8rpx;
}

.item-right {
    display: flex;
    align-items: center;
}

.status-text {
    font-size: 28rpx;
    color: #999;
    margin-right: 8rpx;
}

.cache-size {
    font-size: 28rpx;
    color: #999;
    margin-right: 8rpx;
}

.item-arrow {
    color: #ccc;
    font-size: 32rpx;
}

/* 退出登录按钮样式 */
.logout-btn {
    margin: 40rpx 0rpx;
    background-color: #fff;
    color: #576b95;
    text-align: center;
    padding: 24rpx;
    border-radius: 8rpx;
    font-size: 32rpx;
}
</style>