{
	"easycom": {
		"autoscan": true,
		"custom": {
			"^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue",
			"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
		}
	},
	"pages": [
		
		{
			"path": "pages/login/login",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/user/user",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/position/position",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/publish/publish",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/message/message",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/chat/chat",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/second/second",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/second/positions/positions",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/settings/settings",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/interests/positions",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/interests/jobUpdate",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/interests/invitation",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/function/helpCenter",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/second/map/index",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/second/map/address",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/second/released/index",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/authentication/index",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/authentication/positioning",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/authentication/adress",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/audits/index",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/position/detail",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/position/selectedAddress",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/position/serch",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/position/viewDetails",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/settings/emitpassword/index",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/settings/emitpassword/modify",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/settings/emitpassword/setpassword",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/settings/emitpassword/information",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/realName",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/position/jobserch",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/index/nearbydetail/index",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/index/signeddetail/index",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/demo/websocket-test",
			"style": {
				"navigationBarTitleText": "WebSocket测试",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/second/map/mapPicker",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/homeIndex/index",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/personal/index",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/homeIndex/detail/index",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/personal/emitName",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/personal/changeReal/emitRealname",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/personal/changeReal/emitPhone",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/personal/changeReal/emitCode",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/personal/emitWechat",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/personal/emitCompany",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/personal/myPositions",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/personal/emitPhone",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/recruitment/topPosition",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/recruitment/urgentPositin",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/recruitment/chatOnline",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/recruitment/directDial",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/recruitment/jobUpdate",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/function/integration",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/function/invoice",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/function/feedback",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/function/FeedbackDetails",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/function/application",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/function/feedHistory",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/function/collection",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/function/company",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/function/settings",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/function/agreement",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/chat/mapPicker",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/chat/vaResume",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/smart/chat",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/smart/onlineService",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false,
				"navigationBarTitleText": "在线咨询"
			}
		},
		{
			"path": "pages/smart/serviceDemo",
			"style": {
				"navigationBarTitleText": "客服演示",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/chat/userSetting",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/function/recharge",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/function/pointsList",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/usercontont/InterviewList",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/usercontont/InterviewDetail",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/audits/failed",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/audits/failed",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/function/invoicingDetail",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/settings/emitpassword/cancellation",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/settings/emitpassword/canceagr",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/function/recruitmen",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		}
	],
	"tabBar": {
		"color": "#969ca1",
		"selectedColor": "#000000",
		"borderStyle": "black",
		"list": [
			{
				"pagePath": "pages/homeIndex/index",
				"text": "候选人",
				"iconPath": "static/tabbar/candidates.png",
				"selectedIconPath": "static/tabbar/candidateschoi.png",
				"selectedColor": "#000000"
			},
			{
				"pagePath": "pages/position/position",
				"text": "职位",
				"iconPath": "static/tabbar/posicc.png",
				"selectedIconPath": "static/tabbar/posiblack.png",
				"selectedColor": "#000000"
			},
			{
				"pagePath": "pages/publish/publish",
				"text": "发布",
				"iconPath": "static/tabbar/add.png",
				"selectedIconPath": "static/tabbar/add.png",
				"selectedColor": "#000000"
			},
			{
				"pagePath": "pages/message/message",
				"text": "消息",
				"iconPath": "static/tabbar/candidates.png",
				"selectedIconPath": "static/tabbar/candidateschoi.png",
				"selectedColor": "#000000"
			},
			{
				"pagePath": "pages/user/user",
				"text": "我的",
				"iconPath": "static/tabbar/my.png",
				"selectedIconPath": "static/tabbar/mychoice.png",
				"selectedColor": "#000000"
			}
		],
		"midButton": {
			"width": "80px",
			"height": "50px",
			"iconPath": "static/tabbar/add.png",
			"iconWidth": "24px",
			"backgroundImage": "static/tabbar/add.png"
		}
	},
	"subPackages": [
		{
			"root": "uni_modules/uni-feedback",
			"pages": [
				{
					"path": "pages/opendb-feedback/opendb-feedback",
					"style": {
						"navigationBarTitleText": "意见反馈",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "uni_modules/uni-id-pages/pages",
			"pages": [
				{
					"path": "userinfo/userinfo",
					"style": {
						"navigationBarTitleText": "个人资料"
					}
				},
				{
					"path": "userinfo/realname-verify/realname-verify",
					"style": {
						"enablePullDownRefresh": false,
						"navigationBarTitleText": "实名认证"
					}
				},
				{
					"path": "login/login-withoutpwd"
				},
				{
					"path": "login/login-withpwd"
				},
				{
					"path": "userinfo/deactivate/deactivate",
					"style": {
						"navigationBarTitleText": "注销账号"
					}
				},
				{
					"path": "userinfo/bind-mobile/bind-mobile",
					"style": {
						"navigationBarTitleText": "绑定手机号码"
					}
				},
				{
					"path": "login/login-smscode",
					"style": {
						"navigationBarTitleText": "手机验证码登录"
					}
				},
				{
					"path": "register/register",
					"style": {
						"navigationBarTitleText": "注册"
					}
				},
				{
					"path": "retrieve/retrieve",
					"style": {
						"navigationBarTitleText": "重置密码"
					}
				},
				{
					"path": "common/webview/webview",
					"style": {
						"enablePullDownRefresh": false,
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "userinfo/change_pwd/change_pwd",
					"style": {
						"enablePullDownRefresh": false,
						"navigationBarTitleText": "修改密码"
					}
				},
				{
					"path": "register/register-by-email",
					"style": {
						"navigationBarTitleText": "邮箱验证码注册"
					}
				},
				{
					"path": "retrieve/retrieve-by-email",
					"style": {
						"navigationBarTitleText": "通过邮箱重置密码"
					}
				},
				{
					"path": "userinfo/set-pwd/set-pwd",
					"style": {
						"enablePullDownRefresh": false,
						"navigationBarTitleText": "设置密码"
					}
				},
				{
					"path": "userinfo/cropImage/cropImage"
				},
				{
					"path": "register/register-admin",
					"style": {
						"enablePullDownRefresh": false,
						"navigationBarTitleText": "注册管理员账号"
					}
				}
			]
		}
	],
	"globalStyle": {
		// #ifdef H5
		"h5": {
			"titleNView": false
		},
		// #endif
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-starter",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#F8F8F8",
		"enablePullDownRefresh": false,
		// "maxWidth":375,
		"rpxCalcMaxDeviceWidth": 375,
		"rpxCalcBaseDeviceWidth": 375
		// "rpxCalcIncludeWidth":0
	},
	"condition": {
		"list": [
			{
				"path": "pages/login/login"
			},
			{
				"path": "pages/index/index"
			},
			{
				"path": "pages/user/user"
			},
			{
				"path": "pages/position/position"
			},
			{
				"path": "pages/publish/publish"
			},
			{
				"path": "pages/message/message"
			},
			{
				"path": "pages/second/second"
			},
			{
				"path": "pages/second/positions/positions"
			},
			{
				"path": "pages/user/settings/settings"
			},
			{
				"path": "pages/user/interests/positions"
			},
			{
				"path": "pages/user/interests/jobUpdate"
			},
			{
				"path": "pages/user/interests/invitation"
			},
			{
				"path": "pages/user/settings/helpCenter"
			}
		],
		"current": 1
	},
	"uniIdRouter": {
		"loginPage": "uni_modules/uni-id-pages/pages/login/login-withoutpwd",
		"needLogin": [
			"/uni_modules/uni-id-pages/pages/userinfo/userinfo"
		],
		"resToLogin": true
	}
}