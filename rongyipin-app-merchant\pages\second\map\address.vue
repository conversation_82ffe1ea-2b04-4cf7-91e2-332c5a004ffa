<template>
    <view class="container">
        <u-navbar :autoBack="true" title="搜索地址" :titleStyle="{
            color: '#222',
            fontWeight: 'bold',
            fontSize: '36rpx'
        }" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>
        <view class="search-input">
            <view class="current-city">{{ currentCitylate }} <u-icon size="20" name="arrow-down-fill"></u-icon></view>
            <u--input placeholder="请输入内容" border="surround" v-model="value" @change="reverseGeocoder"></u--input>
        </view>

        <view class="city-list-section">
            <scroll-view class="city-list" scroll-y>
                <u-radio-group v-model="value" @change="radioGroupChange" iconSize="20" v-if="cityList.length > 0">
                    <view class="city-item" v-for="(city, index) in cityList" :key="index">

                        <u-radio :name="city.title" :size="30" :iconSize="20" activeColor="#039885"></u-radio>
                        <view class="content" @click="selectCitylate(city)">
                            <p class="city-title" :class="{ active: value === city.title }">{{ city.title }}</p>
                            <p class="city-address">{{ city.address }}</p>
                        </view>

                    </view>
                </u-radio-group>
                <view v-else class="no-data">
                    <p><image src="@/static/app/second/positioning.png" alt=""/></p>
                    <p>未找到地址</p>
                    <p>请尝试只输入商圈、小区、写字楼或学校名</p>
                </view>
            </scroll-view>
        </view>

    </view>
</template>

<script>
import { homeApi } from '@/utils/api';
export default {
    data() {
        return {
            currentCitylate: '',
            cityList: [],
            value: '', // 当前选中的城市
        };
    },
    // watch: {
    //     '$store.state.currentCity'(newVal) {
    //         this.currentCitylate = newVal;
    //     }
    // },
    mounted() {
        // 初始化赋值
        this.currentCitylate = this.$store.state.currentCity;
        console.log(this.currentCitylate, 'mounted 初始化赋值');
    },
    methods: {
        async reverseGeocoder(latitude, longitude) {
            uni.showLoading({
                title: '加载中...',
                mask: true // 防止用户点击穿透
            });
            // uni.request({
            //     url: 'https://apis.map.qq.com/api/map/ws/place/v1/suggestion', //仅为示例，并非真实接口地址。
            //     data: {
            //         key: 'QMEBZ-QAUKB-HWGUE-NCFVQ-SKKBH-ULBYV',
            //         region: this.currentCitylate,
            //         keyword: this.value
            //     },
            //     success: (res) => {
            //         this.cityList = res.data
            //         uni.hideLoading();
            //     }
            // });
            const res = await homeApi.getMap({
                key: 'QMEBZ-QAUKB-HWGUE-NCFVQ-SKKBH-ULBYV',
                region: this.currentCitylate,
                keyword: this.value
            })
            console.log(res, 'city');
            this.cityList = res.data
            uni.hideLoading();
        },
        selectCitylate(city) {
            console.log(city, '这是城市的选择');
            this.value = city.title
            this.$store.commit('setCity', city);
            // 其他选中逻辑
            uni.navigateBack({
                delta: 2  // 返回的页面数，如果 delta 大于现有页面数，则返回到首页
            });
        },
        radioGroupChange(name) {
            console.log(name, '!!!!!!');
            this.value = name
            this.$store.commit('setCity', this.value);
            // 其他选中逻辑
            uni.navigateBack({
                delta: 2  // 返回的页面数，如果 delta 大于现有页面数，则返回到首页
            });
        },
    },
}


</script>

<style lang="scss" scoped>
.container {
    /* padding: 20rpx; */
    height: 100vh;
}

.search-input {
    // padding: 30rpx;
    width: 93%;
    height: 80rpx;
    margin: 0 auto;
    display: flex;
    border: 1px solid #ccc;
    border-radius: 20rpx;
    margin-top: 30rpx;

    .current-city {
        display: flex;
        align-items: center;
        padding: 0 20rpx;

        .u-icon {
            margin-left: 10rpx;
        }
    }
}

::v-deep .u-input {
    border-left: 1px solid #ccc;
    // border-radius: 20rpx;
    margin-left: 5rpx;
}

.city-list-section {
    padding: 30rpx;
    height: calc(100vh - 230rpx);
    overflow-y: scroll;

    ::v-deep .u-radio-group--row {
        display: inline;
    }

    .city-item {
        width: 100%;
        height: 130rpx;
        display: flex;
        align-items: center;

        .content {
            flex: 1;
            margin-left: 20rpx;
        }

        // border-bottom: 1px solid #ccc;
        .city-title {
            font-size: 30rpx;
            color: #333;
        }

        .city-title.active {
            color: #50b1b2;
            /* 被选中时变绿色 */
        }

        .city-address {
            font-size: 27rpx;
            color: #999;
            margin-top: 10rpx;
        }
    }
}

.no-data {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin-top: 300rpx;

    p {
        // width: 100%;
        margin-top: 20rpx;
        font-size: 28rpx;
        color: #999;

        image {
            width: 100rpx;
            height: 100rpx;
        }
    }

    p:nth-child(2) {
        color: #000000;
        font-size: 35rpx;
    }
}
</style>