<template>
    <view class="container">
        <view class="header">
            <u-navbar :autoBack="true" title="公司主页" :titleStyle="{
                color: '#222',
                fontWeight: 'bold',
                fontSize: '36rpx'
            }" :leftIconSize="22" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>
        </view>

        <view class="card">
            <view class="form-item">
                <text class="label">上传企业LOGO </text>
                <view class="tips">
                    <text>· 企业LOGO原件/电子版 </text>
                </view>
                <view v-if="imageLOGO" class="upload-btn">
                    <image :src="imageLOGO" mode="aspectFit" style="width: 100%; height: 300rpx;" />
                    <view class="verify-options" @tap="chooseImage">
                        <image src="@/static/app/authentication/imgUpload.png" alt="" />
                        <view> 重新上传</view>
                    </view>
                </view>
                <button v-if="!imageLOGO" @tap="chooseImage">拍照或上传照片</button>

            </view>
            <view class="form-item">
                <view class="address-section">
                    <text class="label">工作地址</text>
                    <view class="address-picker" @click="showAddressPicker">
                        <text>{{ selectedAddress || '请选择工作地址' }}</text>
                        <u-icon name="arrow-right" color="black" size="28"></u-icon>
                    </view>
                </view>
            </view>



            <!-- <view class="form-item">
                <text class="label">企业性质</text>
                <view class="nature" @click="showPicker('perty')">
                    <p>{{ property.name }}</p>
                    <u-icon name="arrow-right"></u-icon>
                </view>
            </view> -->
            <view class="form-item">
                <text class="label">公司规模</text>
                <view class="nature" @click="showPicker('Scale')">
                    <p>{{ Scale.name }}</p>
                    <u-icon name="arrow-right"></u-icon>
                </view>
            </view>

            <view class="form-item">
                <text class="label">所属行业</text>
                <uni-section title="">
                    <uni-data-picker popup-title="请选择所属行业" :localdata="dataTree" v-model="classes" @change="onchange"
                        @nodeclick="onnodeclick" @popupopened="onpopupopened" @popupclosed="onpopupclosed"
                        @clear="onclear">
                    </uni-data-picker>
                </uni-section>
            </view>
            <!-- <view class="form-item">
                <text class="label">注册资金</text>
                <u--input placeholder="请输入注册资金" border="surround" v-model="Funds"></u--input>
            </view> -->
            <!-- <view class="form-item">
                <text class="label">联系方式</text>
                <u--input placeholder="请输入联系方式" border="surround" v-model="entertel"></u--input>
            </view> -->
            <view class="form-item">
                <text class="label">企业简介</text>
                <u--textarea v-model="licenseIntro" placeholder="请输入公司介绍"></u--textarea>
            </view>

            <view class="form-item">
                <text class="label">官方网站</text>
                <u--input placeholder="请输入公司官网" border="surround" v-model="website"></u--input>
            </view>
        </view>
        <view class="Submit">
            <button class="submit-btn" @tap="submitForm">提交审核</button>
        </view>
        <u-picker :show="show" :itemHeight="70" :columns="columns" @confirm="onPickerConfirm" @cancel="show = false"
            keyName="name"></u-picker>
        <u-picker :show="show1" :itemHeight="70" :columns="columnsScale" @confirm="onPickerConfirm"
            @cancel="show1 = false" keyName="name"></u-picker>

    </view>
</template>

<script>
import { dictApi, fileApi, priseApi } from '@/utils/api.js'
export default {
    data() {
        return {
            image: '',
            imagePath: '',
            imageLOGO: '',
            licenseIntro: '11',
            columns: [],
            show: false,
            property: '1',
            Scale: '1',
            rawData: [], // 原始数据
            classes: '1-2',
            dataTree: [],
            columnsScale: [],
            show1: false,
            // Funds: '',
            selectedAddress: '',
            // entertel: '',
            imageConver1: '',
            industry1: '',
            industry2: '',
            city: {},
            detailed: '',
            useinfo: {},
            lat: '',
            lng: '',
            website: ''
        }
    },
    watch: {
        '$store.state.cation'(newVal) {
            this.selectedAddress = newVal.title;
            this.city = newVal;
            this.lat = this.city.location.lat;
            this.lng = this.city.location.lng;
            this.city.adcode = this.city.ad_info.adcode
            console.log(this.city, 'watch 监听');
        }
    },
    onShow() {

    },
    async mounted() {
        this.useinfo = uni.getStorageSync('is_auth')
        console.log(this.useinfo)
        this.city.adcode = this.useinfo.area_id
        this.city.title = this.useinfo.address_name
        this.city.address = this.useinfo.formatted_address
        this.lat = this.useinfo.lat,
            this.lng = this.useinfo.lon
        this.selectedAddress = this.useinfo.address_name
        this.imageLOGO = this.useinfo.logo
        this.industry1 = this.useinfo.industry1,
            this.industry2 = this.useinfo.industry2
        this.classes = [this.industry1, this.industry2]
        this.licenseIntro = this.useinfo.content
        this.website = this.useinfo.website
        // 获取企业性质字典数据
        const res = await dictApi.getDict()
        this.property = res.data.job_pr.data[0]
        this.Scale = res.data.job_mun.data[0]
        this.columns = [res.data.job_pr.data]
        this.columnsScale = [res.data.job_mun.data]
        const res1 = await fileApi.getCompany()
        this.rawData = res1.data;
        this.dataTree = this.transformToPickerData(this.rawData)
        // function findIndustry(industry1, industry2, industryList) {
        //     // 先找一级
        //     const first = industryList.find(item => item.value === industry1);
        //     console.log(first)
        //     if (!first) return { first: null, second: null };
        //     // 再找二级
        //     const second = first.children.find(child => child.value === industry2);
        //     return { first, second };
        // }
        // const result = findIndustry(this.industry1, this.industry2, this.dataTree);
        // console.log('一级：', result.first);
        // console.log('二级：', result.second);
        // this.industry1 =result.first.children.value
        // this.industry2 =result.second.value
        uni.hideLoading()
    },
    methods: {
        goBack() {
            uni.navigateBack()
        },
        handleBacklate() {
            uni.switchTab({
                url: '/pages/index/index'
            });
        },
        onclear() {
            console.log('清空');
        },
        transformToPickerData(raw) {
            return raw.map(item => ({
                text: item.name,
                value: item.id,
                children: (item.children || []).map(child => ({
                    text: child.name,
                    value: child.id
                }))
            }))
        },
        onnodeclick(e) {
        },
        onpopupopened(e) {
        },
        onpopupclosed(e) {
        },
        onchange(e) {
            console.log('onchange:', e);
            if (e.detail.value.length === 0) {
                this.industry1 = ''
                this.industry2 = ''
            } else {
                this.industry1 = e.detail.value[0].value
                this.industry2 = e.detail.value[1].value
            }

        },
        showAddressPicker() {
            uni.navigateTo({
                url: '/pages/authentication/positioning'
            });
        },
        showPicker(type) {
            console.log('选择器类型：', type);
            // 根据类型设置不同的选项
            switch (type) {
                // case 'perty':
                //     this.show = true; // 打开选择器
                //     this.columns = this.columns;
                //     break;
                case 'Scale':
                    this.show1 = true; // 打开选择器
                    this.columnsScale = this.columnsScale;
                    break;
            }
            this.currentPickerType = type; // 保存当前选择的类型
        },
        onPickerConfirm(value) {
            switch (this.currentPickerType) {
                case 'perty':
                    this.property = value.value[0];
                    console.log('选择的企业性质:', this.property);
                    this.show = false; // 关闭选择器
                    break;
                case 'Scale':
                    this.Scale = value.value[0];
                    this.show1 = false; // 关闭选择器
                    break;
            }
        },
        chooseImage() {
            uni.chooseImage({
                count: 1,
                success: (res) => {
                    this.image = res.tempFilePaths[0]
                }
            })
        },
        async submitForm() {
            // 提交逻辑
            uni.showLoading({ title: '提交中...' })
            const params = {
                logo: this.imageConver1,
                content: this.licenseIntro,
                industry1: this.industry1,
                industry2: this.industry2,
                size: this.Scale.id,
                area_id: this.city.adcode,
                address_name: this.city.title,
                formatted_address: this.city.address,
                lat: this.lat,
                lon: this.lng,
                website: this.website,
                area_id: this.city.adcode,
                address: this.city.address,
                formatted_address: this.city.title
            }
            let res = await fileApi.getCompanySave(params)
            console.log(res, '提交结果')
            if (res.code !== 200) {
                uni.hideLoading()

                return
            }
            else {
                setTimeout(async () => {

                    uni.showToast({ title: '提交成功', icon: 'success' })
                    let resdata = await priseApi.getCompanyInfoe()
                    uni.setStorageSync('is_auth', resdata.data)
                    uni.showToast({ title: res.msg, icon: 'none' })
                    let logolist = uni.getStorageSync('usinfo')
                    console.log(this.imageConver1, 'logolist')
                    logolist.logo = this.imageConver1
                    console.log(logolist, 'logo')
                    uni.setStorageSync('usinfo', logolist)
                    uni.hideLoading()
                    uni.showToast({ title: res.msg, icon: 'success' })
                    setTimeout(() => {
                        uni.navigateTo({
                            url: '/pages/user/user'
                        })
                    }, 1500)
                }, 2000)
            }
        },
        chooseImage() {
            uni.chooseImage({
                count: 1,
                sizeType: ['compressed'],
                sourceType: ['camera', 'album'], // 支持拍照和相册
                success: (res) => {
                    this.imagePath = res.tempFilePaths[0]
                    this.uploadImage(this.imagePath)
                },
                fail: (err) => {
                    console.log('图片选择失败', err)
                }
            })
        },
        uploadImage(filePath) {
            console.log('选择的图片路径:', filePath)
            uni.showLoading({ title: '上传中' })
            // #ifdef H5
            uni.uploadFile({
                url: '/api/common/upload', // 替换为你自己的上传地址
                filePath: filePath,
                name: 'file', // 根据后端字段设定
                formData: {
                    type: 2
                },
                success: (uploadFileRes) => {
                    console.log(uploadFileRes)
                    uni.hideLoading()
                    uni.showToast({ title: '上传成功', icon: 'success' })
                    this.imageConver1 = JSON.parse(uploadFileRes.data).data.url
                },
                fail: (err) => {
                    uni.hideLoading()
                    uni.showToast({ title: '上传失败', icon: 'none' })
                    console.error('上传失败:', err)
                }
            })
            // #endif

            // #ifdef APP-PLUS
            uni.uploadFile({
                url: 'http://8.130.152.121:82/api/common/upload', // 替换为你自己的上传地址
                filePath: filePath,
                name: 'file', // 根据后端字段设定
                formData: {
                    type: 2
                },
                success: (uploadFileRes) => {
                    console.log(uploadFileRes)
                    uni.hideLoading()
                    uni.showToast({ title: '上传成功', icon: 'success' })
                    this.imageConver1 = JSON.parse(uploadFileRes.data).data.url
                },
                fail: (err) => {
                    uni.hideLoading()
                    uni.showToast({ title: '上传失败', icon: 'none' })
                    console.error('上传失败:', err)
                }
            })
            // #endif
        },
    }
}
</script>

<style scoped lang="scss">
.container {
    min-height: 100vh;
}

// .header {
//     height: 88rpx;
//     /* display: flex;
//     justify-content: space-between;
//     align-items: center; */
//     background-color: #fff;

//     .navbar {
//         height: 100%;
//     }
// }

.header-title {
    display: flex;
    width: 100%;
    flex-wrap: wrap;

    text {
        width: 100%;
        text-align: center;
        color: #fff;
        font-size: 50rpx;

        image {
            width: 80rpx;
            height: 80rpx;
        }
    }
}

.warning-icon {
    font-size: 40rpx;
    display: block;
}

.warning-text {
    font-size: 30rpx;
    font-weight: bold;
    margin-top: 10rpx;
}

.card {
    background-color: #ffffff;
    border-top-left-radius: 30rpx;
    border-top-right-radius: 30rpx;
    padding: 40rpx 30rpx;
    // margin-top: 40rpx;
    height: calc(100vh - 346rpx);
    overflow-y: auto;
}

.form-item {
    margin-bottom: 40rpx;
}

.label {
    font-size: 28rpx;
    font-weight: 600;
    margin-bottom: 20rpx;
    display: block;
}

.required {
    color: #f00;
    margin-right: 6rpx;
}

.nature {
    display: flex;
    align-items: center;
    height: 30rpx;
    justify-content: space-between;
    border-bottom: 1px solid #f8f8f8;
    padding: 26rpx;
    background-color: #f8f8f8;
    border-radius: 10rpx;
}

.input {
    border: 1rpx solid #ddd;
    border-radius: 10rpx;
    padding: 20rpx;
    font-size: 28rpx;
}

.tips text {
    display: block;
    color: #888;
    font-size: 24rpx;
    margin: 6rpx 0;
}

.address-section {
    margin: 40rpx 0;

    .section-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 20rpx;
    }

    .address-picker {
        height: 88rpx;
        background-color: #f8f8f8;
        border-radius: 12rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 30rpx;

        text {
            font-size: 28rpx;
            color: #333;
        }
    }
}

.upload-btn {
    margin-top: 20rpx;
    padding: 20rpx;
    border: 2rpx dashed #ccc;
    text-align: center;
    border-radius: 10rpx;
    color: #333;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.upload-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 10rpx;
}

.Submit {
    width: 100%;
    /* height: 80rpx; */
    /* position: fixed; */
    /* bottom: 30rpx; */
    /* border-radius: 12rpx; */
    padding: 40rpx 0rpx;
    background-color: white;
    z-index: 100;
}

.submit-btn {
    width: 92%;
    /* height: 100%; */
    margin: 0 auto;
    background-color: #00c4b6;
    color: white;
    font-size: 30rpx;
    /* padding: 20rpx; */


}

.verify-options {
    position: absolute;
    width: 240rpx;
    height: 70rpx;
    display: flex;
    justify-content: space-around;
    align-items: center;
    font-size: 30rpx;
    /* color: #999; */
    margin-top: 20rpx;
    background-color: white;
    z-index: 200;
    color: black;
    border-radius: 20rpx;

    image {
        width: 40rpx;
        height: 40rpx;
        /* margin-right: 10rpx; */
    }
}


/* 新版 */


::v-deep .u-textarea {
    border: 1px solid #ccc;
    height: 130rpx;
}

::v-deep .uni-section-header {
    padding: inherit !important;
}

::v-deep .u-input--square {
    border: 1px solid #ccc;
}
</style>