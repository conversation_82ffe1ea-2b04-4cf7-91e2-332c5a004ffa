<template>
    <view class="sms-verify-container">
        <!-- 顶部导航 -->
        <view class="nav-bar">
            <view class="nav-content">
                <view class="nav-left" @click="goBack">
                    <text class="nav-back">‹</text>
                </view>
                <view class="nav-center">
                    <text class="nav-title">请输入短信验证码</text>
                </view>
                <view class="nav-right"></view>
            </view>
        </view>

        <!-- 页面内容 -->
        <view class="content">
            <!-- 手机号提示 -->
            <view class="phone-tip">
                <text>验证码已发送至{{ maskedPhone }}的手机号</text>
            </view>

            <!-- 验证码输入框 -->
            <view class="code-input-container">
                <u-code-input
                    v-model="codeValue"
                    :maxlength="6"
                    :size="80"
                    :space="40"
                    :bold="true"
                    :fontSize="40"
                    @change="onCodeChange"
                    @finish="onCodeFinish"
                ></u-code-input>
            </view>

            <!-- 重新获取按钮 -->
            <view class="resend-container">
                <text
                    class="resend-btn"
                    :class="{ 'disabled': countdown > 0 }"
                    @click="resendCode"
                >
                    {{ countdown > 0 ? `重新获取(${countdown}s)` : '重新获取' }}
                </text>
            </view>

            <!-- 确认按钮 -->
            <view class="submit-container">
                <button
                    class="submit-btn"
                    :class="{ 'active': isCodeComplete }"
                    :disabled="!isCodeComplete"
                    @click="submitCode"
                >
                    确认验证码
                </button>
            </view>
        </view>
    </view>
</template>

<script>
import { userApi } from "@/utils/api"

export default {
    data() {
        return {
            phone: '', // 手机号
            codeValue: '', // 验证码值
            countdown: 0, // 倒计时
            timer: null, // 定时器
        };
    },
    computed: {
        // 脱敏手机号
        maskedPhone() {
            if (this.phone.length === 11) {
                return this.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
            }
            return this.phone;
        },
        // 验证码是否完整
        isCodeComplete() {
            return this.codeValue && this.codeValue.length === 6;
        },
        // 完整验证码
        fullCode() {
            return this.codeValue;
        }
    },
    onLoad() {
        this.getPhoneFromStorage();
        // 页面加载后自动发送验证码
        this.$nextTick(() => {
            this.sendSmsCode();
        });
    },
    onUnload() {
        // 页面卸载时清除定时器
        if (this.timer) {
            clearInterval(this.timer);
        }
    },
    methods: {
        // 从本地存储获取手机号
        getPhoneFromStorage() {
            try {
                // 首先尝试从 userinfo 中获取 telephone
                const userinfo = uni.getStorageSync('usinfo');
                let phone = '';

                if (userinfo && userinfo.mobile) {
                    phone = userinfo.mobile;
                } else {
                    // 如果 userinfo 中没有，则尝试其他存储方式
                    phone = uni.getStorageSync('userPhone') || uni.getStorageSync('phone');
                }

                if (phone) {
                    this.phone = phone;
                } else {
                    uni.showToast({
                        title: '未找到手机号信息',
                        icon: 'none'
                    });
                    setTimeout(() => {
                        uni.navigateBack();
                    }, 1500);
                }
            } catch (error) {
                console.error('获取手机号失败:', error);
                uni.showToast({
                    title: '获取手机号失败',
                    icon: 'none'
                });
            }
        },

        // 发送短信验证码
        async sendSmsCode() {
            if (!this.phone) {
                uni.showToast({
                    title: '手机号不能为空',
                    icon: 'none'
                });
                return;
            }

            // 如果正在倒计时中，不允许重复发送
            if (this.countdown > 0) {
                return;
            }

            try {
                uni.showLoading({ title: '发送中...' });

                const params = {
                    phone: this.phone,
                    event: 'changeauth' // 验证类型
                };

                const result = await userApi.Captcha(params);

                if (result.code === 200) {
                    uni.showToast({
                        title: '验证码已发送',
                        icon: 'success'
                    });
                    // 发送成功后立即开始60秒倒计时
                    this.startCountdown();
                } else {
                    uni.showToast({
                        title: result.msg || '发送失败',
                        icon: 'none'
                    });
                }
            } catch (error) {
                console.error('发送验证码失败:', error);
                uni.showToast({
                    title: '发送失败，请重试',
                    icon: 'none'
                });
            } finally {
                uni.hideLoading();
            }
        },

        // 开始倒计时
        startCountdown() {
            this.countdown = 60;
            this.timer = setInterval(() => {
                this.countdown--;
                if (this.countdown <= 0) {
                    clearInterval(this.timer);
                    this.timer = null;
                }
            }, 1000);
        },

        // 重新发送验证码
        resendCode() {
            // 只有在倒计时结束后才能重新发送
            if (this.countdown > 0) {
                uni.showToast({
                    title: `请等待${this.countdown}秒后重试`,
                    icon: 'none'
                });
                return;
            }
            // 清空之前的验证码输入
            this.clearCode();
            // 重新发送验证码
            this.sendSmsCode();
        },

        // 验证码输入变化
        onCodeChange(value) {
            this.codeValue = value;
        },

        // 验证码输入完成
        onCodeFinish(value) {
            this.codeValue = value;
            // 如果输入完成，自动提交
            if (this.isCodeComplete) {
                setTimeout(() => {
                    this.submitCode();
                }, 300);
            }
        },

        // 提交验证码
        async submitCode() {
            if (!this.isCodeComplete) {
                uni.showToast({
                    title: '请输入完整验证码',
                    icon: 'none'
                });
                return;
            }

            try {
                uni.showLoading({ title: '验证中...' });

                const params = {
                    phone: this.phone,
                    code: this.fullCode,
                    event:'changeauth'
                };

                const result = await userApi.phoneCheck(params);

                if (result.code === 200) {
                    uni.showToast({
                        title: '验证成功',
                        icon: 'success'
                    });

                    // 验证成功后跳转到下一步
                    setTimeout(() => {
                        uni.navigateTo({
                            url: '/pages/user/realName'
                        });
                    }, 1500);
                    uni.removeStorageSync('usinfo');
                } else {
                    uni.showToast({
                        title: result.msg || '验证失败',
                        icon: 'none'
                    });
                    // 清空验证码
                    this.clearCode();
                }
            } catch (error) {
                console.error('验证失败:', error);
                uni.showToast({
                    title: '验证失败，请重试',
                    icon: 'none'
                });
                this.clearCode();
            } finally {
                uni.hideLoading();
            }
        },

        // 清空验证码
        clearCode() {
            this.codeValue = '';
        },

        // 返回上一页
        goBack() {
            uni.navigateBack();
        }
    }
}
</script>

<style scoped>
.sms-verify-container {
    background: #fff;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 手机号提示 */
.phone-tip {
    text-align: center;
    margin: 40rpx 0 60rpx;
    font-size: 28rpx;
    color: #666;
}

/* 验证码输入容器 */
.code-input-container {
    margin-bottom: 60rpx;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 重新获取按钮 */
.resend-container {
    text-align: center;
    margin-bottom: 60rpx;
}

.resend-btn {
    font-size: 28rpx;
    color: #4CAF50;
    cursor: pointer;
    transition: all 0.3s;
}

.resend-btn.disabled {
    color: #ccc;
    cursor: not-allowed;
}

/* 提交按钮 */
.submit-container {
    margin-top: 40rpx;
}

.submit-btn {
    width: 100%;
    height: 88rpx;
    background: #ccc;
    color: #fff;
    border: none;
    border-radius: 12rpx;
    font-size: 32rpx;
    font-weight: 600;
    transition: all 0.3s;
}

.submit-btn.active {
    background: #4CAF50;
}

.submit-btn:disabled {
    background: #ccc;
    color: #999;
}

/* 导航栏 */
.nav-bar {
    background: #fff;
    height: 88rpx;
    display: flex;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    /* border-bottom: 1rpx solid #f0f0f0; */
}

.nav-content {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32rpx;
}

.nav-left {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-back {
    font-size: 48rpx;
    color: #333;
    font-weight: 300;
}

.nav-center {
    flex: 1;
    text-align: center;
}

.nav-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
}

.nav-right {
    width: 80rpx;
}

/* 内容区域 */
.content {
    flex: 1;
    padding-top: 88rpx;
    padding: 88rpx 32rpx 40rpx;
}

/* 验证选项 */
.verify-options {
    margin-top: 40rpx;
}

.option-item {
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx 24rpx;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid #f0f0f0;
    transition: all 0.3s;
}

.option-item:active {
    transform: scale(0.98);
    background: #f8f9fa;
}

.option-content {
    display: flex;
    align-items: center;
    flex: 1;
}

.option-icon {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f9ff;
    border-radius: 50%;
    margin-right: 24rpx;
}

.icon-phone {
    font-size: 40rpx;
}

.option-text {
    flex: 1;
}

.option-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
}

.option-arrow {
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.arrow {
    font-size: 32rpx;
    color: #999;
    transform: rotate(90deg);
}
</style>