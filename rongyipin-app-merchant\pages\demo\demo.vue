<template>
	<view class="container">
		<view class="content">
			<!-- 页面内容区域 -->
			<view class="page-content">
				<text>示例页面内容</text>
			</view>
		</view>
		
		<!-- 自定义TabBar -->
		<view class="custom-tab-bar">
			<view class="tab-item" v-for="(item, index) in tabList" :key="index" @click="switchTab(item.pagePath)">
				<image :src="currentPath === item.pagePath ? item.selectedIconPath : item.iconPath" 
					   class="tab-icon" mode="aspectFit"/>
				<text :class="['tab-text', currentPath === item.pagePath ? 'active' : '']">
					{{item.text}}
				</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			currentPath: '/pages/demo/demo',
			tabList: [
				{
					pagePath: '/pages/index/index',
					text: '候选人',
					iconPath: '/static/tabbar/candidates.png',
					selectedIconPath: '/static/tabbar/candidateschoi.png'
				},
				{
					pagePath: '/pages/position/position',
					text: '职位',
					iconPath: '/static/tabbar/posicc.png',
					selectedIconPath: '/static/tabbar/posiblack.png'
				},
				{
					pagePath: '/pages/publish/publish',
					text: '发布',
					iconPath: '/static/tabbar/add.png',
					selectedIconPath: '/static/tabbar/add.png',
					backgroundColor: '#01f8e4'
				},
				{
					pagePath: '/pages/message/message',
					text: '消息',
					iconPath: '/static/tabbar/candidates.png',
					selectedIconPath: '/static/tabbar/candidateschoi.png'
				},
				{
					pagePath: '/pages/user/user',
					text: '我的',
					iconPath: '/static/tabbar/my.png',
					selectedIconPath: '/static/tabbar/mychoice.png'
				}
			]
		}
	},
	methods: {
		switchTab(path) {
			uni.switchTab({
				url: path
			})
		}
	}
}
</script>

<style lang="scss">
.container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f8f8f8;
}

.content {
	flex: 1;
	overflow-y: auto;
}

.page-content {
	padding: 20rpx;
}

.custom-tab-bar {
	height: 100rpx;
	background-color: #ffffff;
	display: flex;
	justify-content: space-around;
	align-items: center;
	border-top: 1rpx solid #eee;
	padding-bottom: env(safe-area-inset-bottom);
	
	.tab-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 10rpx 0;
		
		&:nth-child(3) {
			background-color: #01f8e4;
			margin: -20rpx 20rpx 0;
			border-radius: 16rpx;
			padding: 20rpx 0;
		}
	}
	
	.tab-icon {
		width: 48rpx;
		height: 48rpx;
		margin-bottom: 6rpx;
	}
	
	.tab-text {
		font-size: 24rpx;
		color: #969ca1;
		
		&.active {
			color: #000000;
		}
	}
}
</style> 