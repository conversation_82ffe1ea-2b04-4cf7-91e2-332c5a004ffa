<template>
    <view class="position-select">
        <!-- 顶部导航栏 -->
        <view class="header">
            <u-navbar :autoBack="true" :titleStyle="{
                color: '#333',
                fontSize: '34rpx',
                fontWeight: '500'
            }" fixed safeAreaInsetTop placeholder bgColor="#ffffff">
                <view class="u-nav-slot" slot="left">
                    <u-icon name="arrow-left" size="30" @click="handleBack"></u-icon>
                    <!-- <u-line direction="column" :hairline="false" length="16" margin="0 8px"></u-line>
						<u-icon name="home" size="20"></u-icon> -->
                </view>
                <template #right>
                    <view class="navbar-right">
                        <view class="service" @click="handleService">
                            <view class="service-avatar">
                                <image src="/static/images/service-avatar.png" mode="aspectFill"></image>
                            </view>
                            <text>客服</text>
                        </view>
                        <view class="help" @click="handleHelp">
                            <uni-icons type="download" size="20" color="#333"></uni-icons>
                            <text>帮助</text>
                        </view>
                    </view>
                </template>
            </u-navbar>
        </view>
        <view class="page-title">选择发布职位类型</view>
        <!-- 搜索框 --><text></text>
        <view class="search-box">
            <view class="search-input-wrap" :class="{ 'active': isSearchFocused }">
                <uni-icons type="search" size="16" color="#999"></uni-icons>
                <u--input type="text" v-model="searchText" placeholder="输入想招的职位" @focus="handleSearchFocus"
                    @blur="handleSearchBlur" @input="handleSearchInput" @confirm="handleSearchEnter" />
                <view v-if="isSearchFocused" class="cancel-btn" @click="handleSearchCancel">取消</view>
            </view>
        </view>

        <!-- 搜索历史 -->
        <view class="search-history" v-if="showHistory">
            <view class="history-header">
                <text>搜索历史</text>
                <uni-icons v-if="searchHistory.length > 0" type="trash" size="16" @click="clearHistory"></uni-icons>
            </view>
            <view class="history-tags">
                <text v-for="(item, index) in searchHistory" :key="index" class="history-tag"
                    @click="handleHistoryClick(item)">
                    {{ item }}
                </text>
            </view>
        </view>

        <!-- 搜索结果 -->
        <view class="search-results" v-if="searchText && !showHistory">
            <view v-if="filteredPositions.length > 0" class="results-list">
                <view v-for="(item, index) in filteredPositions" :key="index" class="result-item"
                    @click="selectPosition(item)">
                    <text class="position-name">{{ item.name }}</text>
                    <text class="position-path">{{ item.path }}</text>
                </view>
            </view>
            <view v-else class="no-results">
                <text>暂无数据</text>
            </view>
        </view>

        <!-- 职位类别列表 -->
        <!-- 左侧一级分类 -->
        <view class="categories-container" v-if="!searchText && !isSearchFocused">
            <!-- 左侧一级分类 -->
            <scroll-view class="primary-category" scroll-y :show-scrollbar="false">
                <view v-for="(category, index) in positionCategories" :key="index" class="category-item"
                    :class="{ 'active': category.name === selectedCategory }" @click="handleCategoryClick(category)">
                    <text class="category-name">{{ category.name }}</text>
                </view>
            </scroll-view>

            <!-- 右侧二级分类 -->
            <scroll-view class="secondary-category" scroll-y>
                <view class="secondary-list" v-if="selectedCategory">
                    <view v-for="(item, index) in currentSecondaryCategories" :key="index" class="secondary-item"
                        @click="handleSecondaryClick(item)">
                        <text class="position-name">{{ typeof item === 'string' ? item : item.name }}</text>
                        <uni-icons type="right" size="14" color="#999"></uni-icons>
                    </view>
                </view>
            </scroll-view>

            <transition name="fade">
                <view class="third-level-container" v-if="selectedThirdLevel">
                    <view class="third-level-mask" @click="closeThirdLevel"></view>
                    <view class="third-level-popup">
                        <!-- <view class="popup-header">
                            <text class="title">{{ selectedThirdLevel ? selectedThirdLevel.name : '' }}</text>
                            <view class="close-btn" @click="closeThirdLevel">
                                <uni-icons type="close" size="20"></uni-icons>
                            </view>
                        </view> -->
                        <scroll-view class="third-level-list" scroll-y>
                            <view v-for="(item, index) in (selectedThirdLevel ? selectedThirdLevel.children : [])"
                                :key="index" class="third-level-item" @click="selectPosition({
                                    id: item.id,
                                    categoryid: positionCategories.find(cat => cat.name === selectedCategory).id,
                                    positionid: selectedThirdLevel.id,
                                    name: item.name,
                                    path: `${selectedCategory}>${selectedThirdLevel.name}>${item.name}`
                                })">
                                <text>{{ item.name }}</text>
                            </view>
                        </scroll-view>
                    </view>
                </view>
            </transition>
        </view>
    </view>
</template>

<script>
import { jobApi } from "@/utils/api"
export default {
    data() {
        return {
            isSearchFocused: false,
            searchText: '',
            showHistory: false,
            searchHistory: [],
            selectedCategory: '',
            positionCategories: [],
            allPositions: [],
            selectedThirdLevel: null,
        }
    },
    computed: {
        filteredPositions() {
            if (!this.searchText) return [];
            return this.allPositions.filter(position =>
                console.log(position, '===position') ||
                (position.name && position.name.includes(this.searchText)) ||
                (position.path && position.path.includes(this.searchText))
            );

        },
        currentSecondaryCategories() {
        if (!this.positionCategories || !this.selectedCategory) return [];
        const category = this.positionCategories.find(cat => cat.name === this.selectedCategory);
        return category ? category.children : [];
    }
    },
    created() {
        // 从本地存储获取搜索历史
    },
    async mounted() {
        uni.showLoading({
            title: '加载中...',
            mask: true
        });
        let res = await jobApi.getJobList()
        this.positionCategories = res.data
        
        // 初始化所有职位数据
        this.positionCategories.forEach(category => {
            category.children.forEach(position => {
                position.children.forEach(childPosition => {
                    this.allPositions.push({
                        id: childPosition.id,
                        categoryid: category.id,
                        positionid: position.id,
                        name: childPosition.name,
                        path: `${category.name}>${position.name}>${childPosition.name}`
                    });
                });
            });
        });
        console.log(this.positionCategories, '===this.positionCategories')
        const history = uni.getStorageSync('positionSearchHistory');
        if (history) {
            this.searchHistory = JSON.parse(history);
        }
        this.initDefaultCategory();
        uni.hideLoading()
    },
    methods: {

        handleService() {
            console.log('111')
        },
        initDefaultCategory() {
            if (this.positionCategories.length > 0 && !this.selectedCategory) {
                this.selectedCategory = this.positionCategories[0].name;
                console.log(this.positionCategories[0], '===this.selectedCategory')
            }
        },
        handleBack() {
            uni.navigateTo()
        },
        handleSearchFocus() {
            this.isSearchFocused = true;
            if (!this.searchText) {
                this.showHistory = true;
            }
        },
        handleSearchBlur() {
            // if (!this.searchText) {
            //     this.isSearchFocused = false;
            //     this.showHistory = false;
            // }
        },
        handleSearchCancel() {
            this.searchText = '';
            this.isSearchFocused = false;
            this.showHistory = false;
        },
        handleSearchInput() {
            this.showHistory = !this.searchText;
        },
        handleSearchEnter(event) {
            console.log(this.searchText, '@@');
            if (this.searchText.trim()) {

                // 添加到搜索历史
                if (!this.searchHistory.includes(this.searchText)) {
                    this.searchHistory.unshift(this.searchText);
                    if (this.searchHistory.length > 10) {
                        this.searchHistory.pop();
                    }
                    uni.setStorageSync('positionSearchHistory', JSON.stringify(this.searchHistory));
                }

                // 直接使用搜索文本创建position对象
                // this.selectPosition({
                //     name: this.searchText,
                //     path: this.searchText
                // });
            }

        },
        handleHistoryClick(text) {
            console.log(text, '===text')
            this.searchText = text;
            this.showHistory = false;
        },
        clearHistory() {
            // this.searchHistory = [];
            console.log(this.searchHistory, '@@');
            uni.showModal({
                title: '提示',
                content: '确定要清空搜索历史吗？',
                success: (res) => {
                    if (res.confirm) {
                        // 用户点击确定
                        this.searchHistory = [];
                        uni.removeStorageSync('positionSearchHistory');
                    }
                    // 如果用户点击取消，什么都不做
                }
            });
        },
        handleCategoryClick(category) {
            console.log(category, '===category')
            this.selectedCategory = category.name;
            // 这里可以处理二级分类的显示逻辑
        },
        handleSecondaryClick(item) {
            console.log(item, this.selectedCategory, '===item');
            this.selectedThirdLevel = item; // 直接显示三级分类
        },
        closeThirdLevel() {
            this.selectedThirdLevel = null;
        },
        selectPosition(position) {
            console.log(position, '===position');
            this.$store.commit('setwork', position); // 将选中的职位存储到 Vuex
            uni.navigateBack({
                delta: 1
            });
            // uni.navigateTo({
            //     url: `/pages/second/second?name=${position.name}&path=${position.path}`
            // });
        }
    }
}
</script>

<style lang="scss" scoped>
.search-input-wrap {
    padding: 6rpx 24rpx !important;
}

.uni-page-wrapper {
    height: 100%;
}

.position-select {
    min-height: 100vh;
    background-color: white;

    .header {
        height: 88rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: white;

        .navbar-right {
            display: flex;
            align-items: center;
            height: 100%;
        }

        .avatar {
            width: 60rpx;
            height: 60rpx;
            border-radius: 50%;
            margin-right: 20rpx;
        }

        .help-text {
            font-size: 28rpx;
            color: #333;
        }

        .navbar-right .service,
        .navbar-right .help {
            display: flex;
            // flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-left: 40rpx;
            height: 100%;
        }

        .service-avatar {
            width: 40rpx;
            height: 40rpx;
            border-radius: 50%;
            overflow: hidden;
            margin-bottom: 4rpx;
        }

        .service-avatar image {
            width: 100%;
            height: 100%;
        }

        .navbar-right text {
            font-size: 24rpx;
            margin-top: 4rpx;
        }

        .back-btn {
            padding: 20rpx 20rpx;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 30rpx;
            padding-right: 40rpx;

            .service,
            .help {
                display: flex;
                align-items: center;
                gap: 8rpx;

                text {
                    font-size: 28rpx;
                    color: #333;
                }
            }

            .service-avatar {
                width: 40rpx;
                height: 40rpx;
                border-radius: 50%;
                overflow: hidden;

                image {
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }

    .page-title {
        background-color: white;
        padding-left: 30rpx;
        // flex: 1;
        // text-align: center;
        font-size: 16px;
        font-weight: 500;
    }

    .search-box {

        background-color: #fff;
        padding: 40rpx 30rpx;
        border-bottom: 1px solid #f5f5f5;

        .search-input-wrap {
            display: flex;
            align-items: center;
            background-color: #f5f5f5;
            border-radius: 4px;
            padding: 8px 12px;

            &.active {
                background-color: #fff;
                border: 1px solid #ddd;
            }

            input {
                flex: 1;
                margin: 0 10px;
                font-size: 14px;
            }

            .cancel-btn {
                font-size: 14px;
                color: #666;
            }
        }
    }

    .search-history {
        background-color: #fff;
        padding: 30rpx 30rpx;

        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;

            text {
                font-size: 14px;
                color: #333;
            }
        }

        .history-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;

            .history-tag {
                padding: 6px 12px;
                background-color: #f5f5f5;
                border-radius: 4px;
                font-size: 12px;
                color: #666;
            }
        }
    }

    .search-results {
        background-color: #fff;
        flex: 1;

        .results-list {
            .result-item {
                padding: 15px;
                border-bottom: 1px solid #f5f5f5;

                .position-name {
                    font-size: 14px;
                    color: #333;
                    margin-bottom: 4px;
                }

                .position-path {
                    font-size: 12px;
                    color: #999;
                }
            }
        }

        .no-results {
            padding: 30px;
            text-align: center;
            color: #999;
            font-size: 14px;
        }
    }

    .categories-container {
        display: flex;
        flex: 1;
        background-color: #fff;
        position: relative;
    }

    .primary-category {
        width: 308rpx;
        height: calc(100vh - 112px); // 减去头部和搜索框的高度
        background-color: #f6f7fb;
        overflow-y: hidden;
        /* 隐藏纵向滚动条 */
        -webkit-overflow-scrolling: auto;

        /* iOS 滚动优化 */
        .category-item {
            padding: 15px 10px;
            position: relative;

            &.active {
                background-color: #fff;

                .category-name {
                    color: #00b38a;
                    font-weight: 500;
                }

                &::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 3px;
                    height: 16px;
                    background-color: #00b38a;
                }
            }

            .category-name {
                font-size: 14px;
                color: #333;
            }
        }
    }

    .custom-scroll-view ::-webkit-scrollbar {
        display: none;
    }

    .secondary-category {
        flex: 1;
        height: calc(100vh - 112px);
        background-color: #fff;

        .secondary-list {
            padding: 0 15px;

            .secondary-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px 0;
                border-bottom: 1px solid #f5f5f5;

                .position-name {
                    font-size: 14px;
                    color: #333;
                }
            }
        }
    }

    .third-level-container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 100;
        display: flex;

        .third-level-mask {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.4);
        }

        .third-level-popup {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 70%;
            background-color: #fff;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;

            .popup-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px;
                border-bottom: 1px solid #f5f5f5;

                .title {
                    font-size: 16px;
                    font-weight: 500;
                    color: #333;
                }

                .close-btn {
                    padding: 5px;
                }
            }

            .third-level-list {
                flex: 1;
                overflow-y: auto;

                .third-level-item {
                    padding: 15px;
                    border-bottom: 1px solid #f5f5f5;

                    text {
                        font-size: 14px;
                        color: #333;
                    }

                    &:active {
                        background-color: #f5f5f5;
                    }
                }
            }
        }
    }

    // 添加动画相关样式
    .fade-enter-active,
    .fade-leave-active {
        transition: all 0.3s ease;
    }

    .fade-enter-from,
    .fade-leave-to {
        opacity: 0;
    }

    .fade-enter-from .third-level-popup,
    .fade-leave-to .third-level-popup {
        transform: translateX(100%);
    }

    .fade-enter-to .third-level-popup,
    .fade-leave-from .third-level-popup {
        transform: translateX(0);
    }

    /* Make sure the popup itself has transition properties */
    .third-level-popup {
        transition: transform 0.3s ease;
    }

    /* Add transition to the mask as well for a complete effect */
    .third-level-mask {
        transition: opacity 0.3s ease;
    }
}
</style>