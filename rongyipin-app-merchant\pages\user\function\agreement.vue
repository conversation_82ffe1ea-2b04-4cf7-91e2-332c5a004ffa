<template>
    <view class="invoice-page">
        <!-- 1. 自定义导航栏 -->
        <u-navbar :autoBack="true" title="用户协议" :titleStyle="{
            color: '#222',
            fontWeight: 'bold',
            fontSize: '36rpx'
        }" :leftIconSize="22" bgColor="#ffffff" :leftIconColor="'#222'" fixed safeAreaInsetTop placeholder></u-navbar>

        <view class="privacy-content">
            <view v-if="privacy" v-html="privacy" class="html-content"></view>
            <view v-else class="loading-text">加载中...</view>
        </view>

    </view>
</template>

<script>
import { sysApi } from '@/utils/api'
export default {
    data() {
        return {
            privacy: null
        };
    },
    async onShow() {
        let res = await sysApi.getSysConfig()
        console.log(res)
        this.privacy = res.data.user_content
        uni.hideLoading();
    },
    methods: {

    }
}
</script>

<style lang="scss">
// 页面整体样式
.invoice-page {
    // background-color: #e6f7f2;
    min-height: 100vh;
    padding-bottom: 180rpx; // 为底部固定栏预留空间
}

// 1. 自定义导航栏
.custom-nav-bar {
    background-color: #e6f7f2;
    padding: 0 30rpx;
    // 适配状态栏高度
    padding-top: var(--status-bar-height);
    height: 90rpx; // 自定义导航栏内容高度
    display: flex;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 99;

    .nav-content {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .back-arrow {
            font-size: 40rpx;
            font-weight: bold;
            width: 100rpx;
        }

        .title {
            font-size: 34rpx;
            font-weight: bold;
            color: #333;
        }

        .right-link {
            font-size: 26rpx;
            color: #888;
            width: 150rpx;
            text-align: right;
        }
    }
}

// 隐私政策内容样式
.privacy-content {
    padding: 30rpx;
    background-color: #ffffff;
    margin: 20rpx;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

    .loading-text {
        text-align: center;
        color: #999;
        font-size: 28rpx;
        padding: 60rpx 0;
    }

    .html-content {
        line-height: 1.8;
        color: #333;

        // 重置HTML标签样式

    }
}

::v-deep p {
    margin: 16rpx 0;
    font-size: 28rpx;
    line-height: 1.6;
}

::v-deep strong,
::v-deep b {
    font-weight: bold;
    color: #222;
}

::v-deep font {
    color: inherit !important;
}

// 标题样式
::v-deep p:first-child {
    text-align: center;
    font-size: 36rpx;
    font-weight: bold;
    color: #222;
    margin-bottom: 40rpx;
}

// 段落缩进
::v-deep p {
    text-indent: 2em;
}

// 标题不缩进
::v-deep p:first-child,
::v-deep p:has(strong) {
    text-indent: 0;
}
</style>