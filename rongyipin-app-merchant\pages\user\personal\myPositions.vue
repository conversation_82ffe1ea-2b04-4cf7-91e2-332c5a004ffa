<template>
    <view class="job-container">
        <!-- 导航栏 -->
        <view class="navbar">
            <view class="nav-left" @click="goBack">
                <text class="nav-icon">‹</text>
            </view>
            <view class="nav-title">
                <text class="title-text">我的职务</text>
            </view>
            <view class="nav-right">
            </view>
        </view>

        <!-- 内容区域 -->
        <view class="content">
            <!-- 职务标签 -->
            <view class="job-label">
                <text class="label-text">填写职务</text>
            </view>

            <!-- 职务输入框 -->
            <view class="input-section">
                <input class="job-input" type="text" v-model="jobTitle" placeholder="请填写职务名称"
                    placeholder-style="color: #999999;" />
            </view>

            <!-- 保存按钮 -->
            <view class="save-section">
                <view class="save-btn" @click="saveJob">
                    <text class="save-text">保存</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { realApi } from "@/utils/api"
export default {
    data() {
        return {
            jobTitle: ''
        }
    },
    onLoad() {
        this.loadJobInfo();
    },
    onShow() {
        this.loadJobInfo();
    },
    methods: {
        // 加载职务信息
        loadJobInfo() {
            // 从存储中获取职务信息
            const jobInfo = uni.getStorageSync('job_title');
            if (jobInfo) {
                this.jobTitle = jobInfo;
            }
        },

        // 返回上一页
        goBack() {
            uni.navigateBack();
        },

        // 保存职务
        async saveJob() {
            if (!this.jobTitle.trim()) {
                uni.showToast({
                    title: '请填写职务名称',
                    icon: 'none'
                });
                return;
            }

            try {
                const res = await realApi.jobPositionEdit({ name: this.jobTitle.trim() })
                if (res.code == 200) {
                    let userInfo = uni.getStorageSync('usinfo') || {};
                    userInfo.job_position_name = this.jobTitle.trim();
                    uni.setStorageSync('usinfo', userInfo);

                    uni.showToast({
                        title: '保存成功',
                        icon: 'success'
                    });

                    // 延迟返回上一页
                    setTimeout(() => {
                        uni.navigateBack();
                    }, 1500);
                }
                // 保存到本地存储


            } catch (error) {
                uni.showToast({
                    title: '保存失败',
                    icon: 'none'
                });
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.job-container {
    min-height: 100vh;
    background: #f8f9fa;
}

/* 导航栏样式 */
.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    padding: 0 30rpx;
    background: #ffffff;
    border-bottom: 1rpx solid #f0f0f0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-left,
.nav-right {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-icon {
    font-size: 40rpx;
    color: #333333;
    font-weight: 500;
}

.nav-title {
    flex: 1;
    text-align: center;
}

.title-text {
    font-size: 32rpx;
    color: #333333;
    font-weight: 500;
}

/* 内容区域 */
.content {
    flex: 1;
    padding: 40rpx 30rpx;
}

/* 职务标签 */
.job-label {
    margin-bottom: 20rpx;
}

.label-text {
    font-size: 28rpx;
    color: #333333;
    font-weight: 500;
}

/* 输入框区域 */
.input-section {
    margin-bottom: 60rpx;
}

.job-input {
    width: 100%;
    height: 88rpx;
    background: #ffffff;
    border: 1rpx solid #e0e0e0;
    border-radius: 8rpx;
    padding: 0 24rpx;
    font-size: 28rpx;
    color: #333333;
    box-sizing: border-box;
}

.job-input:focus {
    border-color: #4ECDC4;
}

/* 保存按钮区域 */
.save-section {
    padding: 0;
}

.save-btn {
    width: 100%;
    height: 88rpx;
    background: #4ECDC4;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(78, 205, 196, 0.3);
}

.save-text {
    font-size: 32rpx;
    color: #ffffff;
    font-weight: 500;
}



/* 响应式适配 */
@media screen and (max-width: 750rpx) {
    .navbar {
        height: 80rpx;
        padding: 0 24rpx;
    }

    .content {
        padding: 32rpx 24rpx;
    }

    .job-input {
        height: 80rpx;
        padding: 0 20rpx;
        font-size: 26rpx;
    }

    .save-btn {
        height: 80rpx;
    }

    .save-text {
        font-size: 30rpx;
    }
}
</style>
