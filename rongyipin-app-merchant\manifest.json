{"name": "容翼聘企业版", "appid": "__UNI__F082DED", "description": "云端一体应用快速开发基本项目模版", "versionName": "1.1.1", "versionCode": 400, "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Geolocation": {}, "Payment": {}, "Barcode": {}, "Bluetooth": {}, "Camera": {}, "FaceID": {}, "Contacts": {}, "FacialRecognitionVerify": {}, "Fingerprint": {}, "Maps": {}, "Push": {}}, "networkTimeout": {"request": 60000, "connectSocket": 60000, "uploadFile": 60000, "downloadFile": 60000}, "allowsInlineMediaPlayback": true, "safearea": {"background": "#ffffff", "bottom": {"offset": "auto"}}, "distribute": {"android": {"abiFilters": ["armeabi-v7a", "arm64-v8a", "x86"], "permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "minSdkVersion": 21}, "ios": {"dSYMs": false}, "orientation": ["portrait-primary"], "sdkConfigs": {"push": {"unipush": {"version": "2", "offline": false}}, "geolocation": {"tencent": {"__platform__": ["ios", "android"], "apikey_ios": "QMEBZ-QAUKB-HWGUE-NCFVQ-SKKBH-ULBYV", "apikey_android": "QMEBZ-QAUKB-HWGUE-NCFVQ-SKKBH-ULBYV"}}, "payment": {"alipay": {"__platform__": ["ios", "android"]}}, "maps": {"amap": {"name": "amap9PRiEYe4", "appkey_ios": "452ae8cbb5b25229f0a96f9e3bc2f1a3", "appkey_android": "452ae8cbb5b25229f0a96f9e3bc2f1a3"}}}, "icons": {"ios": {"appstore": "C:/Users/<USER>/Desktop/企业1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}, "android": {"hdpi": "C:/Users/<USER>/Desktop/merchat.png", "xhdpi": "C:/Users/<USER>/Desktop/merchat.png", "xxhdpi": "C:/Users/<USER>/Desktop/merchat.png", "xxxhdpi": "C:/Users/<USER>/Desktop/merchat.png"}}}, "nativePlugins": {"JG-JCore": {"JPUSH_APPKEY_IOS": "", "JPUSH_CHANNEL_IOS": "", "JPUSH_APPKEY_ANDROID": "4d8e2ddc9647947a0d9fdb92", "JPUSH_CHANNEL_ANDROID": "", "__plugin_info__": {"name": "JG-JCore", "description": "极光推送JCore插件", "platforms": "Android,iOS", "url": "", "android_package_name": "com.rongyipinqiyebanapp.standardsdk", "ios_bundle_id": "", "isCloud": false, "bought": -1, "pid": "", "parameters": {"JPUSH_APPKEY_IOS": {"des": "[iOS]极光portal配置应用信息时分配的AppKey", "key": "JCore:APP_KEY", "value": ""}, "JPUSH_CHANNEL_IOS": {"des": "[iOS]用于统计分发渠道，不需要可填默认值developer-default", "key": "JCore:CHANNEL", "value": ""}, "JPUSH_APPKEY_ANDROID": {"des": "[Android]极光portal配置应用信息时分配的AppKey", "key": "4d8e2ddc9647947a0d9fdb92", "value": ""}, "JPUSH_CHANNEL_ANDROID": {"des": "[Android]用于统计分发渠道，不需要可填默认值developer-default", "key": "JPUSH_CHANNEL", "value": ""}}}}, "JG-JPush": {"JPUSH_ISPRODUCTION_IOS": "", "JPUSH_ADVERTISINGID_IOS": "", "JPUSH_DEFAULTINITJPUSH_IOS": "", "JPUSH_OPPO_APPKEY": "", "JPUSH_OPPO_APPID": "", "JPUSH_OPPO_APPSECRET": "", "JPUSH_VIVO_APPKEY": "", "JPUSH_VIVO_APPID": "", "JPUSH_MEIZU_APPKEY": "", "JPUSH_MEIZU_APPID": "", "JPUSH_XIAOMI_APPKEY": "", "JPUSH_XIAOMI_APPID": "", "JPUSH_HUAWEI_APPID": "", "JPUSH_HONOR_APPID": "", "JPUSH_GOOGLE_API_KEY": "", "JPUSH_GOOGLE_APP_ID": "", "JPUSH_GOOGLE_PROJECT_NUMBER": "", "JPUSH_GOOGLE_PROJECT_ID": "", "JPUSH_GOOGLE_STORAGE_BUCKET": "", "__plugin_info__": {"name": "J<PERSON><PERSON><PERSON><PERSON>", "description": "极光推送Hbuilder插件", "platforms": "Android,iOS", "url": "", "android_package_name": "com.rongyipinqiyebanapp.standardsdk", "ios_bundle_id": "", "isCloud": false, "bought": -1, "pid": "", "parameters": {"JPUSH_ISPRODUCTION_IOS": {"des": "[iOS]是否是生产环境，是填true，不是填false或者不填", "key": "JPush:ISPRODUCTION", "value": ""}, "JPUSH_ADVERTISINGID_IOS": {"des": "[iOS]广告标识符（IDFA）如果不需要使用IDFA，可不填", "key": "JPush:ADVERTISINGID", "value": ""}, "JPUSH_DEFAULTINITJPUSH_IOS": {"des": "[iOS]是否默认初始化，是填true，不是填false或者不填", "key": "JPush:DEFAULTINITJPUSH", "value": ""}, "JPUSH_OPPO_APPKEY": {"des": "厂商OPPO-appkey,示例：***********", "key": "OPPO_APPKEY", "value": ""}, "JPUSH_OPPO_APPID": {"des": "厂商OPPO-appId,示例：***********", "key": "OPPO_APPID", "value": ""}, "JPUSH_OPPO_APPSECRET": {"des": "厂商OPPO-appSecret,示例：***********", "key": "OPPO_APPSECRET", "value": ""}, "JPUSH_VIVO_APPKEY": {"des": "厂商VIVO-appkey,示例：12345678", "key": "com.vivo.push.api_key", "value": ""}, "JPUSH_VIVO_APPID": {"des": "厂商VIVO-appId,示例：12345678", "key": "com.vivo.push.app_id", "value": ""}, "JPUSH_MEIZU_APPKEY": {"des": "厂商MEIZU-<PERSON><PERSON><PERSON>,示例：MZ-12345678", "key": "MEIZU_APPKEY", "value": ""}, "JPUSH_MEIZU_APPID": {"des": "厂商MEIZU-appId,示例：MZ-12345678", "key": "MEIZU_APPID", "value": ""}, "JPUSH_XIAOMI_APPKEY": {"des": "厂商XIAOMI-<PERSON><PERSON><PERSON>,示例：MI-12345678", "key": "XIAOMI_APPKEY", "value": ""}, "JPUSH_XIAOMI_APPID": {"des": "厂商XIAOMI-appId,示例：MI-12345678", "key": "XIAOMI_APPID", "value": ""}, "JPUSH_HUAWEI_APPID": {"des": "厂商HUAWEI-appId,示例：appid=12346578", "key": "com.huawei.hms.client.appid", "value": ""}, "JPUSH_HONOR_APPID": {"des": "厂商HONOR-appId,示例：12346578", "key": "com.hihonor.push.app_id", "value": ""}, "JPUSH_GOOGLE_API_KEY": {"des": "厂商google api_key,示例:g-12346578", "key": "google_api_key", "value": ""}, "JPUSH_GOOGLE_APP_ID": {"des": "厂商google mobilesdk_app_id,示例：g-12346578", "key": "google_app_id", "value": ""}, "JPUSH_GOOGLE_PROJECT_NUMBER": {"des": "厂商google project_number,示例：g-12346578", "key": "gcm_defaultSenderId", "value": ""}, "JPUSH_GOOGLE_PROJECT_ID": {"des": "厂商google project_id ,示例：g-12346578", "key": "project_id", "value": ""}, "JPUSH_GOOGLE_STORAGE_BUCKET": {"des": "厂商google storage_bucket,示例：g-12346578", "key": "google_storage_bucket", "value": ""}}}}}}, "quickapp": {}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false}, "usingComponents": true, "optimization": {"subPackages": true}, "permission": {"scope.userLocation": {"desc": "您的位置信息将用于地图定位和地址选择"}}, "requiredPrivateInfos": ["getLocation"]}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "2", "h5": {"devServer": {"port": 8081, "disableHostCheck": true, "proxy": {"/api/map": {"target": "https://apis.map.qq.com/", "changeOrigin": true, "secure": false, "pathRewrite": {"^/api/map": ""}, "headers": {"Referer": "https://apis.map.qq.com/", "User-Agent": "Mozilla/5.0"}}, "/api": {"target": "http://*************:82", "changeOrigin": true, "secure": false, "pathRewrite": {"^/api": "/api"}}, "/socket.io": {"target": "http://*************:82", "changeOrigin": true, "secure": false, "ws": true, "pathRewrite": {"^/socket.io": ""}}}}, "sdkConfigs": {"maps": {"tencent": {"key": "QMEBZ-QAUKB-HWGUE-NCFVQ-SKKBH-ULBYV"}}}}}