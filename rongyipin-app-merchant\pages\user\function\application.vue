<template>
    <view class="application-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" title="申请开票" :autoBack="true" :leftIconSize="30" :leftIconColor="'#333'"
                safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>

        <!-- 开票总额 -->
        <view class="amount-section">
            <view class="amount-card">
                <text class="amount-label">开票总额</text>
                <text class="amount-value">¥ {{ totalAmount.toFixed(2) }}</text>
            </view>
            <!-- 选中订单信息 -->
            <view class="order-info" v-if="selectedOrderIds.length > 0">
                <text class="order-info-text">已选择 {{ orderCount }} 个订单</text>
                <text class="order-ids-text">订单号：{{ selectedOrderIds.join(', ') }}</text>
            </view>
        </view>

        <!-- 表单区域 -->
        <view class="form-section">
            <!-- 抬头类型 -->
            <view class="form-item">
                <text class="form-label">抬头类型</text>
                <view class="radio-group">
                    <view class="radio-item" @click="selectType(2)">
                        <view class="radio-btn" :class="{ 'active': invoiceType === 2 }">
                            <view v-if="invoiceType === 2" class="radio-checked"></view>
                        </view>
                        <text class="radio-text">企业单位</text>
                    </view>
                    <view class="radio-item" @click="selectType(1)">
                        <view class="radio-btn" :class="{ 'active': invoiceType === 1 }">
                            <view v-if="invoiceType === 1" class="radio-checked"></view>
                        </view>
                        <text class="radio-text">个人/非企业单位</text>
                    </view>
                </view>
            </view>

            <!-- 公司抬头 -->
            <view class="form-item" v-if="invoiceType === 2">
                <text class="form-label">公司抬头</text>
                <view class="input-wrapper">
                    <input class="form-input" v-model="formData.companyName" placeholder="必填"
                        placeholder-class="placeholder-style" />
                    <!-- <text class="required-mark">选择</text> -->
                </view>
            </view>
            <!-- 个人抬头 -->
            <view class="form-item" v-if="invoiceType === 1">
                <text class="form-label">抬头名称</text>
                <view class="input-wrapper">
                    <input class="form-input" v-model="formData.companyName" placeholder="必填"
                        placeholder-class="placeholder-style" />
                    <!-- <text class="required-mark">选择</text> -->
                </view>
            </view>

            <!-- 公司税号 -->
            <view class="form-item" v-if="invoiceType === 2">
                <text class="form-label">公司税号</text>
                <view class="input-wrapper">
                    <input class="form-input" v-model="formData.taxNumber" placeholder="必填"
                        placeholder-class="placeholder-style" />
                </view>
            </view>

            <!-- 公司地址 -->
            <view class="form-item" v-if="invoiceType === 2">
                <text class="form-label">公司地址</text>
                <view class="input-wrapper">
                    <input class="form-input" v-model="formData.companyAddress" placeholder="选填"
                        placeholder-class="placeholder-style" />
                </view>
            </view>

            <!-- 公司电话 -->
            <view class="form-item" v-if="invoiceType === 2">
                <text class="form-label">公司电话</text>
                <view class="input-wrapper">
                    <input class="form-input" v-model="formData.companyPhone" placeholder="选填"
                        placeholder-class="placeholder-style" />
                </view>
            </view>

            <!-- 开户银行 -->
            <view class="form-item" v-if="invoiceType === 2">
                <text class="form-label">开户银行</text>
                <view class="input-wrapper">
                    <input class="form-input" v-model="formData.bankName" placeholder="选填"
                        placeholder-class="placeholder-style" />
                </view>
            </view>

            <!-- 开户账号 -->
            <view class="form-item" v-if="invoiceType === 2">
                <text class="form-label">开户账号</text>
                <view class="input-wrapper">
                    <input class="form-input" v-model="formData.bankAccount" placeholder="选填"
                        placeholder-class="placeholder-style" />
                </view>
            </view>

            <!-- 电子邮箱 -->
            <view class="form-item special-item">
                <text class="form-label">电子邮箱</text>
                <view class="input-wrapper">
                    <input class="form-input" v-model="formData.email" placeholder="必填"
                        placeholder-class="placeholder-style" />
                </view>
            </view>

            <!-- 手机号码 -->
            <view class="form-item">
                <text class="form-label">手机号码</text>
                <view class="input-wrapper">
                    <input class="form-input" v-model="formData.phone" placeholder="选填"
                        placeholder-class="placeholder-style" />
                </view>
            </view>
            <!-- 备注 -->
            <view class="remark-card">
                <view class="remark-title">备注</view>
                <view class="remark-content">
                    <u-textarea v-model="invoiceDetail.remark" placeholder="请输入备注" :maxlength="200"
                        height="100"></u-textarea>
                    <view class="remark-count">
                        {{ invoiceDetail.remark.length }}/200
                    </view>
                </view>
            </view>
        </view>

        <!-- 提交按钮 -->
        <view class="submit-section">
            <view class="submit-btn" @click="submitApplication">
                <text class="submit-text">提交</text>
            </view>
        </view>
    </view>
</template>

<script>
import { invoice } from "@/utils/api"
export default {
    data() {
        return {
            invoiceType: 2, // 发票类型：company-企业单位，personal-个人/非企业单位
            totalAmount: 11.90, // 开票总额
            formData: {
                companyName: '', // 公司抬头
                taxNumber: '', // 公司税号
                companyAddress: '', // 公司地址
                companyPhone: '', // 公司电话
                bankName: '', // 开户银行
                bankAccount: '', // 开户账号
                email: '', // 电子邮箱
                phone: '' // 手机号码
            },
            // 接收的参数
            selectedOrderIds: [], // 选中的订单ID数组
            orderCount: 0, // 选中的订单数量
            invoiceDetail: {
                remark: ''
            }
        }
    },

    // 页面加载时接收参数
    onLoad(options) {
        console.log('接收到的参数:', options);

        // 接收订单ID参数
        if (options.ids) {
            this.selectedOrderIds = options.ids.split(',');
            console.log('选中的订单IDs:', this.selectedOrderIds);
        }

        // 接收总金额参数
        if (options.amount) {
            this.totalAmount = parseFloat(options.amount) || 0;
            console.log('开票总额:', this.totalAmount);
        }

        // 接收订单数量参数
        if (options.count) {
            this.orderCount = parseInt(options.count) || 0;
            console.log('订单数量:', this.orderCount);
        }
    },
    methods: {
        // 选择发票类型
        selectType(type) {
            this.invoiceType = type
        },

        // 验证表单
        validateForm() {
            const { companyName, taxNumber, email, phone } = this.formData

            // 必填项验证
            if (!companyName.trim()) {
                uni.showToast({
                    title: '请填写公司抬头',
                    icon: 'none'
                })
                return false
            }

            if (!taxNumber.trim()) {
                uni.showToast({
                    title: '请填写公司税号',
                    icon: 'none'
                })
                return false
            }

            // 邮箱和手机号至少填写一项
            if (!email.trim() && !phone.trim()) {
                uni.showToast({
                    title: '电子邮箱和手机号码至少填写一项',
                    icon: 'none'
                })
                return false
            }

            // 邮箱格式验证
            if (email.trim()) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
                if (!emailRegex.test(email)) {
                    uni.showToast({
                        title: '请输入正确的邮箱格式',
                        icon: 'none'
                    })
                    return false
                }
            }

            // 手机号格式验证
            if (phone.trim()) {
                const phoneRegex = /^1[3-9]\d{9}$/
                if (!phoneRegex.test(phone)) {
                    uni.showToast({
                        title: '请输入正确的手机号码',
                        icon: 'none'
                    })
                    return false
                }
            }

            return true
        },
        //个人验证
        validateFormlate() {
            const { companyName, email, phone } = this.formData

            // 必填项验证
            if (!companyName.trim()) {
                uni.showToast({
                    title: '请填写公司抬头',
                    icon: 'none'
                })
                return false
            }

            // 邮箱格式验证
            if (email.trim()) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
                if (!emailRegex.test(email)) {
                    uni.showToast({
                        title: '请输入正确的邮箱格式',
                        icon: 'none'
                    })
                    return false
                }
            }

            // 手机号格式验证
            if (phone.trim()) {
                const phoneRegex = /^1[3-9]\d{9}$/
                if (!phoneRegex.test(phone)) {
                    uni.showToast({
                        title: '请输入正确的手机号码',
                        icon: 'none'
                    })
                    return false
                }
            }

            return true
        },
        // 提交申请
        async submitApplication() {
            if (this.invoiceType === 2) {
                if (!this.validateForm()) {
                    return
                }
                const params = {
                    order_ids: this.selectedOrderIds.join(','),
                    type: this.invoiceType,
                    company_name: this.formData.companyName,
                    taxpayer_id: this.formData.taxNumber,
                    address: this.formData.companyAddress,
                    phone: this.formData.companyPhone,
                    bank_name: this.formData.bankName,
                    bank_account: this.formData.bankAccount,
                    email: this.formData.email,
                    telephone: this.formData.phone,
                    price: this.totalAmount,
                    memo:this.invoiceDetail.remark
                }
                const res = await invoice.applyInvoice(params)
                if (res.code === 200) {
                    uni.showModal({
                        title: '提交成功',
                        content: '您的开票申请已提交，我们将在2-3个工作日内处理完成',
                        showCancel: false,
                        success: () => {
                            // 返回上一页
                            uni.navigateBack()
                        }
                    })
                } else {
                    uni.showToast({
                        title: res.msg,
                        icon: 'none'
                    })
                }
            } else {
                if (!this.validateFormlate()) {
                    return
                }
                const params = {
                    order_ids: this.selectedOrderIds.join(','),
                    type: this.invoiceType,
                    company_name: this.formData.companyName,
                    email: this.formData.email,
                    telephone: this.formData.phone,
                    price: this.totalAmount,
                    memo:this.invoiceDetail.remark
                }
                const res = await invoice.applyInvoice(params)
                if (res.code === 200) {
                    uni.showModal({
                        title: '提交成功',
                        content: '您的开票申请已提交，我们将在2-3个工作日内处理完成',
                        showCancel: false,
                        success: () => {
                            // 返回上一页
                            uni.navigateBack()
                        }
                    })
                } else {
                    uni.showToast({
                        title: res.msg,
                        icon: 'none'
                    })
                }
            }




        }
    }
}
</script>

<style lang="scss" scoped>
.application-page {
    min-height: 100vh;
    background: linear-gradient(180deg, #E8F8F5 0%, #F8F8F8 100%);
    padding-bottom: 140rpx; /* 为固定按钮预留空间 */
}

/* 开票总额 */
.amount-section {
    margin: 88rpx 15rpx 30rpx;
}

.amount-card {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.amount-label {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
}

.amount-value {
    font-size: 32rpx;
    color: #333;
    font-weight: 600;
}

/* 订单信息 */
.order-info {
    margin-top: 20rpx;
    padding: 20rpx;
    background-color: #f8f9fa;
    border-radius: 12rpx;
    border: 1rpx solid #e9ecef;
}

.order-info-text {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 8rpx;
    display: block;
}

.order-ids-text {
    font-size: 24rpx;
    color: #999;
    line-height: 1.4;
    word-break: break-all;
}

/* 表单区域 */
.form-section {
    margin: 0 15rpx 30rpx;
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.form-item {
    display: flex;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
        border-bottom: none;
    }

    &.special-item {
        border-bottom: 2rpx solid #14B19E;
    }
}

.form-label {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
    width: 160rpx;
    flex-shrink: 0;
}

.input-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.form-input {
    flex: 1;
    font-size: 28rpx;
    color: #333;
    text-align: right;
}

.placeholder-style {
    color: #999;
    font-size: 28rpx;
}

.required-mark {
    font-size: 28rpx;
    color: #999;
    margin-left: 20rpx;
}

/* 单选按钮组 */
.radio-group {
    display: flex;
    gap: 40rpx;
    flex: 1;
    justify-content: flex-end;
}

.radio-item {
    display: flex;
    align-items: center;
    gap: 12rpx;
}

.radio-btn {
    width: 36rpx;
    height: 36rpx;
    border: 2rpx solid #ddd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    &.active {
        border-color: #14B19E;
    }
}

.radio-checked {
    width: 20rpx;
    height: 20rpx;
    background-color: #14B19E;
    border-radius: 50%;
}

.radio-text {
    font-size: 28rpx;
    color: #333;
}

/* 提交按钮 - 固定在底部 */
.submit-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #ffffff;
    padding: 20rpx 30rpx;
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
    z-index: 100;
    /* 适配iPhone X等底部安全区域 */
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
}

.submit-btn {
    background-color: #666;
    border-radius: 50rpx;
    padding: 24rpx;
    text-align: center;
    transition: all 0.3s ease;
    margin-bottom: 20rpx;
    &:active {
        transform: scale(0.98);
        background-color: #555;
    }
}

.submit-text {
    font-size: 32rpx;
    color: #fff;
    font-weight: 600;
}

.remark-card {
    // background: #ffffff;
    border-radius: 24rpx;
    padding: 30rpx;
    // box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

    .remark-title {
        font-size: 28rpx;
        color: #666666;
        margin-bottom: 20rpx;
        font-weight: 500;
    }

    .remark-content {
        font-size: 28rpx;
        color: #333333;
        line-height: 1.6;
        word-break: break-all;
        // background: #f8f9fa;
        // padding: 24rpx;
        border-radius: 12rpx;
        min-height: 120rpx;
    }
    

    .remark-count {
        text-align: right;
        color: #999;
        font-size: 12px;
        margin-top: 4px;
    }
}
::v-deep .u-textarea--radius{
    background-color: #f8f9fa;
}
/* 全局页面样式 */
page {
    background-color: #f8f8f8;
}
</style>
