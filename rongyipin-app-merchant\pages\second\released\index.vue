<template>
    <view class="position-container">
        <!-- 顶部导航 -->
        <view class="header">
            <u-navbar :autoBack="false" :titleStyle="{
                color: '#333',
                fontSize: '34rpx',
                fontWeight: '500'
            }" rightText="1" :leftIconSize="32" rightClick fixed safeAreaInsetTop placeholder>
                <view class="u-nav-slot" slot="left">
                    <u-icon name="arrow-left" size="30" @click="handleBack"></u-icon>
                    <!-- <u-line direction="column" :hairline="false" length="16" margin="0 8px"></u-line>
						<u-icon name="home" size="20"></u-icon> -->
                </view>
                <template #right>
                    <view class="navbar-right">
                        <view class="service" @click="handleService">
                            <view class="service-avatar">
                                <image src="/static/images/service-avatar.png" mode="aspectFill"></image>
                            </view>
                            <text>客服</text>
                        </view>
                        <view class="help" @click="handleHelp">
                            <uni-icons type="download" size="20" color="#333"></uni-icons>
                            <text>帮助</text>
                        </view>
                    </view>
                </template>
            </u-navbar>
        </view>
        <view style="height: calc(100vh - 260rpx);overflow-y: auto;">
            <view class="title">选择职位要求</view>
            <view class="position-anchor">
                <!-- <view class="position-anchor-form">
                    <uni-forms ref="formlate" :modelValue="formData">
                        <uni-forms-item label="职位标题" required>
                            <uni-easyinput :class="{ error: !formData.name }" v-model="formData.name"
                                placeholder="请输入职位" />
                        </uni-forms-item>

                    </uni-forms>
                    <text style="color: red; font-size:24rpx;" v-if="!formData.name">*标题是必填项</text>
                </view> -->
                <view class="position-tab-container">
                    <!-- <view class="position-tab">
                        <view class="tab-item" :class="{ active: currentTab === 1 }" @click="switchTab(1)">
                            全职
                        </view>
                        <view class="tab-item" :class="{ active: currentTab === 2 }" @click="switchTab(2)">
                            兼职
                        </view>
                    </view> -->
                    <view class="tab-content">
                        <view v-if="currentTab === 0">
                            <view class="tab-latemoney" :class="{ error: validationError || valiError }">
                                <view class="tab-moneyinterval">
                                    <u--input placeholder="最小薪资" border="surround" v-model="minmoney"
                                        @blur="validateInput"></u--input>
                                    <p>至</p>
                                    <u--input class="maxmoney" placeholder="最大薪资" border="surround" v-model="maxmoney"
                                        @blur="validateInput"></u--input>
                                </view>
                                <view class="tab-settlement">
                                    <view v-for="(item, index) in settlement" :key="index">
                                        <p class="tab-settlement-item" :class="{ active: item.selected }"
                                            @click="selectSettlement(index)">{{ item.name }}
                                        </p>
                                    </view>
                                </view>
                            </view>
                            <view v-if="validationError" class="error-message">请输入薪资范围</view>
                            <view v-if="valiError" class="error-message">最高薪资必须大于最低薪资</view>
                        </view>
                        <view v-else>
                            <view class="tab-latemoneypart" :class="{ error: validationError || valiError }">
                                <view class="tab-moneyinterval">
                                    <u--input placeholder="请输入薪资" border="surround" v-model="money"
                                        @blur="validateInput"></u--input>
                                    <p style="border-left: 1px solid #ccc;padding-left: 40rpx;display: flex;"
                                        @click="showPicker('salary')"> <text>{{ salary.name }}</text>
                                        <u-icon style="margin-top: 20rpx;width: 15rpx;height: 15rpx;"
                                            name="arrow-down-fill"></u-icon>
                                    </p>
                                </view>
                                <view class="tab-settlement">
                                    <view v-for="(item, index) in billing" :key="index">
                                        <p class="tab-settlement-item" :class="{ active: item.selected }"
                                            @click="selectSettle(index)">{{ item.name }}</p>
                                    </view>
                                </view>
                            </view>
                            <view v-if="validationError" class="error-message">请输入薪资范围</view>
                            <view v-if="valiError" class="error-message">最高薪资必须大于最低薪资</view>
                        </view>
                    </view>
                </view>

                <!-- <view class="position-description">
                    <uni-forms ref="form" :modelValue="formData">
                        <uni-forms-item label="职位描述" required>
                            <uni-easyinput :class="{ error: !formData.introduction }" type="textarea"
                                v-model="formData.introduction"
                                placeholder="请输入工作内容与工作要求，如有需要求职者完成什么工作，对求职者的哪些要求，请详细说明。" />
                        </uni-forms-item>

                        <text style="color: red; font-size:24rpx;" v-if="!formData.introduction">*职位描述是必填项</text>
                        <uni-forms-item class="position-description-phone" label="求职者可通过微信号联系我" required>
                            <uni-easyinput ref="phoneInput" :clearable="false" v-model="formData.phone"
                                placeholder="请输入联系方式" :disabled="true" />
                            <img @click="open('search')"
                                style="width: 35rpx;height: 35rpx;margin-top: 20rpx;padding-left: 30rpx;"
                                src="@/static/app/second/modify.png" alt="">
                        </uni-forms-item>
                        <uni-forms-item class="position-description-phone" label="招聘人数" required>
                            <uni-easyinput :clearable="false" v-model="formData.number" placeholder="请输入联系方式"
                                :disabled="true" />
                            <img @click="open('recruit')"
                                style="width: 35rpx;height: 35rpx;margin-top: 20rpx;padding-left: 30rpx;"
                                src="@/static/app/second/modify.png" alt="">
                        </uni-forms-item>
                    </uni-forms>
                </view> -->
                <!-- <view class="position-anchor-form">
                    <uni-forms ref="formlate" :modelValue="formData">
                        <uni-forms-item label="详细地址" required>
                            <uni-easyinput :class="{ error: !formData.address }" v-model="formData.address"
                                placeholder="请输入详细地址" />
                        </uni-forms-item>

                    </uni-forms>
                    <text style="color: red; font-size:24rpx;" v-if="!formData.address">*详细是必填项</text>
                </view> -->
                <view class="position-folding">
                    <view>
                        <view class="info-list">
                            <view class="form-item">
                                <text class="label"><text class="required">*</text>所属行业</text>
                                <uni-section title="">
                                    <uni-data-picker popup-title="请选择所属行呀" :localdata="dataTree" v-model="classes"
                                        @change="onchange" @nodeclick="onnodeclick" @popupopened="onpopupopened"
                                        @popupclosed="onpopupclosed" @clear="onclear">
                                    </uni-data-picker>
                                </uni-section>
                            </view>
                            <view class="info-item" @click="showPicker('age')">
                                <text class="info-label">年龄</text>
                                <view class="info-value">
                                    <text>{{ age.name }}</text>
                                    <u-icon name="arrow-right" size="20" color="#ccc"></u-icon>
                                </view>
                            </view>
                            <view class="info-item" @click="showPicker('gender')">
                                <text class="info-label">性别</text>
                                <view class="info-value">
                                    <text>{{ gender.name }}</text>
                                    <u-icon name="arrow-right" size="20" color="#ccc"></u-icon>
                                </view>
                            </view>
                            <view class="info-item" @click="showPicker('education')">
                                <text class="info-label">学历</text>
                                <view class="info-value">
                                    <text>{{ education.name }}</text>
                                    <u-icon name="arrow-right" size="20" color="#ccc"></u-icon>
                                </view>
                            </view>
                            <view class="info-item" @click="showPicker('workTime')">
                                <text class="info-label">工作经验 </text>
                                <view class="info-value">
                                    <text>{{ workTime.name }}</text>
                                    <u-icon name="arrow-right" size="20" color="#ccc"></u-icon>
                                </view>
                            </view>
                            <view v-if="currentTab === 0" class="info-item" @click="open('welfare')">
                                <text class="info-label">福利与提成</text>
                                <view class="info-value">
                                    <text>{{ selectedIndexes.length || 0 }}项福利</text>
                                    <u-icon name="arrow-right" size="20" color="#ccc"></u-icon>
                                </view>
                            </view>
                            <view v-if="currentTab === 0" class="info-item" @click="opennew">
                                <text class="info-label">职位关键词</text>
                                <view class="info-value">
                                    <text>{{ selectedKeywords.length || '' }}</text>
                                    <u-icon name="arrow-right" size="20" color="#ccc"></u-icon>
                                </view>
                            </view>

                            <view v-if="currentTab === 1" class="info-item" @click="open('period')">
                                <text class="info-label">工作时段</text>
                                <view class="info-value">
                                    <text
                                        style="width: 300rpx;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;"
                                        v-if="partTab == 1">{{ timestart }} ~ {{ timeend }},{{ workTimedata.name
                                        }},{{
                                            workPeriodOptions == 0 ? '不限' : `${workPeriodOptions.length}种工作时段`
                                        }}</text>
                                    <text
                                        style="width: 300rpx;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;"
                                        v-else>{{ workingday.name }}, {{ workTimedata.name }},{{
                                            workPeriodOptions == 0
                                                ? '不限' : `${workPeriodOptions.length}种工作时段` }}</text>
                                    <u-icon name="arrow-right" size="20" color="#ccc"></u-icon>
                                </view>
                            </view>
                            <!-- <view class="info-item" @click="opennew">
                                <text class="info-label">职位关键词</text>
                                <view class="info-value">
                                    <text>{{ selectedKeywords.length || '' }}</text>
                                    <u-icon name="arrow-right" size="20" color="#ccc"></u-icon>
                                </view>
                            </view> -->
                        </view>
                    </view>

                    <u-picker :show="show" :itemHeight="70" :columns="ageColumns" keyName="name"
                        @confirm="onPickerConfirm" @cancel="show = false" />
                    <u-picker :show="show1" :itemHeight="60" :columns="genderColumns" keyName="name"
                        @confirm="onPickerConfirm" @cancel="show1 = false" />
                    <u-picker :show="show2" :itemHeight="60" :columns="educationColumns" keyName="name"
                        @confirm="onPickerConfirm" @cancel="show2 = false" />
                    <u-picker :show="show3" :itemHeight="60" :columns="workTimeColumns" keyName="name"
                        @confirm="onPickerConfirm" @cancel="show3 = false" />
                    <!-- <u-picker :show="show4" :itemHeight="60" :columns="benefitsColumns" keyName="name"
                        @confirm="onPickerConfirm" @cancel="show4 = false" /> -->
                    <u-picker :show="show5" :itemHeight="60" :columns="salaryColumns" keyName="name"
                        @confirm="onPickerConfirm" @cancel="show5 = false" />
                    <u-picker :show="show6" :itemHeight="60" :columns="periodColumns" keyName="name"
                        @confirm="onPickerConfirm" @cancel="show6 = false" />
                </view>

            </view>
        </view>


        <view class="position-submit">
            <button class="uni-btn" @click="submit">确认发布</button>
        </view>

        <u-popup :show="showpopup" mode="bottom" @close="close" @open="open">
            <template>
                <view class="popup-late" v-if="tablate === 1">
                    <view class="popup-title">
                        <text>
                            修改联系方式
                        </text>
                        <text @click="close">
                            <u-icon name="close"></u-icon>
                        </text>
                    </view>
                    <view class="popup-content">
                        联系方式
                        <u--input type="number" :class="{ 'input-error': !chanpassword }" placeholder="请输入内容"
                            v-model="chanpassword"></u--input>
                    </view>
                    <view v-if="!chanpassword" style="color: red;font-size: 25rpx;">*电话号码不能为空</view>
                    <view class="popup-button">
                        <button class="uni-btn" @click="Confirm">确认</button>
                    </view>
                </view>
                <view class="popup-late" v-if="tablate === 2">
                    <view class="popup-title">
                        <text>
                            自定义招聘人数
                        </text>
                        <text @click="close">
                            <u-icon name="close"></u-icon>
                        </text>
                    </view>
                    <view class="popup-content">
                        招聘人数
                        <u--input type="number" :class="{ 'input-error': !recruitment }" placeholder="请输入内容"
                            v-model="recruitment"></u--input>
                    </view>
                    <view v-if="!recruitment" style="color: red;font-size: 25rpx;">*招聘人数不能为空</view>
                    <view class="popup-button">
                        <button class="uni-btn" @click="Confirmlate">确认</button>
                    </view>
                </view>
                <view class="popup-late" v-if="tablate === 3">
                    <view class="popup-title">
                        <text>
                            员工福利
                        </text>
                        <text @click="close">
                            <u-icon name="close"></u-icon>
                        </text>
                    </view>
                    <view
                        style="width: 100%;display: flex;flex-wrap: wrap;justify-content: space-around;align-items: center">
                        <view v-for="(item, index) in benefits" :key="item.id" class="popup-item"
                            :class="{ 'active': selectedIndexes.includes(index) }" @click="toggleBenefit(index)">
                            {{ item.name }}
                        </view>
                    </view>

                    <view class="popup-button">
                        <button class="uni-btn" @click="Confirmlatebtn">确认</button>
                    </view>
                </view>

            </template>
        </u-popup>

        <u-popup :show="partpopup" mode="bottom" @close="close" @open="open">
            <view class="tab-container">
                <view class="tab-header">
                    <view class="tab-item" :class="{ active: partTab === 0 }" @click="partchTab(0)">

                        长期兼职
                    </view>
                    <view class="tab-item" :class="{ active: partTab === 1 }" @click="partchTab(1)">
                        临时兼职
                    </view>
                    <text @click="close">
                        <u-icon name="close"></u-icon>
                    </text>
                </view>
                <view class="tab-content">
                    <view v-if="partTab === 1">
                        <!-- 临时兼职内容 -->
                        <view class="section">
                            <text class="section-title">工作日期</text>
                            <view class="section-item" :class="{ active: isDateSelected }"
                                style="color: black;width: 100%;text-align: center;height: 70rpx;line-height: 70rpx;"
                                @click="toggleDateSelection">
                                自定义日期
                            </view>
                            <view style="text-align: center;width: 100%;margin-top: 10rpx;"
                                v-if="timestart != '' && timeend != ''">{{ timestart }} ~ {{ timeend }}</view>
                        </view>
                        <view class="section">
                            <text class="section-title">工作时间</text>
                            <view class="section-item" v-for="(item, index) in workTimeOptions" :key="index"
                                :class="{ active: selectedWorkTime === index }" @click="selectWorkTime(item)">
                                {{ item.name }}
                            </view>
                        </view>
                        <view class="section">
                            <text class="section-title">工作时段 · 多选</text>
                            <view class="section-item" v-for="(item, index) in selectedWorkPeriods" :key="item.id"
                                :class="{ 'active': workPeriodOptions.includes(index) }"
                                @click="toggleWorkPeriod(index)">
                                {{ item.name }}
                            </view>
                        </view>

                        <uni-calendar ref="calendar" class="uni-calendar--hook" :clear-date="true" :date="info.date"
                            :insert="info.insert" :lunar="info.lunar" :startDate="info.startDate"
                            :endDate="info.endDate" :range="info.range" @confirm="confirm" @close="calenclose" />
                    </view>
                    <view v-else-if="partTab === 0">
                        <!-- 长期兼职内容 -->
                        <view class="section">
                            <text class="section-title">每周工作天数</text>
                            <view class="section-item" v-for="(item, index) in Workingdays" :key="index"
                                :class="{ active: selectedworkday === index }" @click="selectWorkday(item)">
                                {{ item.name }}
                            </view>
                        </view>
                        <view class="section">
                            <text class="section-title">工作时间</text>
                            <view class="section-item" v-for="(item, index) in workTimeOptions" :key="index"
                                :class="{ active: selectedWorkTime === index }" @click="selectWorkTime(item)">
                                {{ item.name }}
                            </view>
                        </view>
                        <view class="section">
                            <text class="section-title">工作时段 · 多选</text>
                            <view class="section-item" v-for="(item, index) in selectedWorkPeriods" :key="item.id"
                                :class="{ 'active': workPeriodOptions.includes(index) }"
                                @click="toggleWorkPeriod(index)">
                                {{ item.name }}
                            </view>
                        </view>
                    </view>
                </view>

                <view class="submit-btn">
                    <button class="uni-btn" @click="Confirmlatebtn">确认</button>
                </view>
            </view>

        </u-popup>

        <u-popup :show="posipopup" @close="closenew" @open="opennew">
            <view class="popup-late" style="height: 1000rpx;">
                <view class="titlelate">
                    <view class="popup-title">
                        <text>
                            职位关键词
                        </text>
                        <text @click="closenew">
                            <u-icon name="close"></u-icon>
                        </text>
                    </view>
                    <view style="width: 100% ;height: 90%;overflow: hidden;overflow-y: auto;margin-top: 10rpx;">
                        <view v-for="(item, index) in positions" :key="item.id">
                            <p style="margin-top: 10rpx;">{{ item.name }}</p>

                            <view
                                style="display: flex;justify-content: flex-start;width: 100%;flex-wrap: wrap;gap: 10px;">
                                <view v-for="(ite, ind) in item.children" :key="ite.id" class="popup-keywords"
                                    :class="{ 'active': selectedKeywords.includes(ite.id) }"
                                    @click="toggleKeyword(ite)">
                                    {{ ite.name }}
                                </view>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="popup-button">
                    <button class="uni-btn" @click="Confirmlatebtn">确认</button>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
import { dictApi, priseApi, fileApi } from "../../../utils/api"; // 引入字典数据接口
export default {
    data() {
        return {
            recruitment: '', // 招聘信息
            chanpassword: '', // 修改的联系方式
            showpopup: false,
            partpopup: false,
            show: false,
            show1: false,
            show2: false,
            show3: false,
            show4: false,
            show5: false,
            show6: false,
            currentTab: 1,
            age: '',
            gender: '',
            education: '',
            workTime: '',
            benefits: '',
            other: '',
            salary: '', // 薪资
            period: '',
            ageColumns: [], // 年龄
            genderColumns: [], // 性别
            educationColumns: [], // 学历
            workTimeColumns: [], // 工作年限
            benefitsColumns: [], // 福利与提成
            salaryColumns: [], // 薪资
            periodColumns: [],
            partTab: 0,
            billing: [
                { name: '日结', type: 0, selected: true }, // 默认选中第一个
                { name: '周结', type: 1, selected: false },
                { name: '月结', type: 2, selected: false }, // 默认选中第一个
                { name: '完工结', type: 3, selected: false }
            ], // 结算方式
            settlement: [
                { name: '月结', type: 0, selected: true }, // 默认选中第一个
                { name: '面议', type: 1, selected: false }
            ],
            // 表单数据
            formData: {
                name: '这是',
                introduction: '',
                phone: '',
                number: 10,
                address: '25楼'
            },
            minmoney: 1000,
            maxmoney: 2000,
            validationError: false, // 校验错误状态
            valiError: false,
            city: {}, // 城市数据
            work: '',
            selectedSettlementId: 0, // 保存选中的结算方式的 id
            tablate: 0,
            working: '', // 全职工作方式
            selectedIndexes: [], // 选中的福利
            money: '100', // 兼职薪资

            workTimedata: '',
            workTimeOptions: [], //工作时间
            selectedWorkTime: 0, // 选中的工作时间索引

            workPeriodOptions: [0], //工作时段
            selectedWorkPeriods: '', // 选中的工作时段索引，

            Workingdays: [], //长期兼职 工作天数
            workingday: '',
            selectedworkday: 0,
            showCalendar: false,
            info: {
                lunar: true,
                range: true,
                insert: false,
                selected: []
            },
            timestart: '',
            timeend: '',
            isDateSelected: false,
            positions: [], //职位关键词
            posipopup: false,
            selectedKeywords: [], // 选中的关键词ID数组
            dataTree: [],
            classes: '1-2',
            industry1: '',
            industry2: ''
        }
    },
    computed: {
        //判断输入框是否有值
        validateInput() {
            if (!this.minmoney || !this.maxmoney) {
                this.validationError = true; // 如果任一输入框为空，显示错误
            } else if (this.minmoney >= this.maxmoney) {
                console.log(this.minmoney, this.maxmoney, 'minmoney')
                this.validationError = false;
                this.valiError = true;
            } else {
                this.validationError = false; // 输入框有值时清除错误
                this.valiError = false;
            }
        },
    },
    async onLoad(options) {
        // 获取城市数据，添加安全检查
        this.city = this.$store.state.city || {};
        this.work = this.$store.state.work || {};
        this.formData.introduction = this.$store.state.introduction
        this.formData.address = this.$store.state.address
        this.currentTab = this.$store.state.partTab
        console.log(this.work, 'address')
        // 安全设置职位名称 
        if (this.work && this.work.name) {
            this.formData.name = this.work.name;
        }
        const res1 = await fileApi.getCompany()
        this.rawData = res1.data;
        this.dataTree = this.transformToPickerData(this.rawData)
    },
    async mounted() {
        let userinfo = await dictApi.getUserInfo();
        this.formData.phone = userinfo.data.mobile;
        const params = {
            job_class_id: this.work.id
        }
        let JobType = await priseApi.getJobTypeSkill(params)
        console.log(JobType, 'JobType')
        if (JobType.code == 200) {
            this.positions = JobType.data
        }
        let res = await dictApi.getDict();
        // 初始化默认值
        this.age = res.data.job_age.data[0];
        this.gender = res.data.user_sex.data[0];
        this.education = res.data.job_edu.data[0];
        this.workTime = res.data.job_exp.data[0];
        this.benefits = res.data.job_welfare.data;
        this.salary = res.data.part_salary_type.data[0];
        this.workTimedata = res.data.part_work_type.data[0]
        this.selectedWorkPeriods = res.data.part_work_time.data
        this.workingday = res.data.part_work_day.data[0]
        this.ageColumns = [res.data.job_age.data]
        this.genderColumns = [res.data.user_sex.data]
        this.educationColumns = [res.data.job_edu.data]
        this.workTimeColumns = [res.data.job_exp.data]
        this.benefitsColumns = [res.data.job_welfare.data]
        this.salaryColumns = [res.data.part_salary_type.data]
        this.workTimeOptions = res.data.part_work_type.data
        this.Workingdays = res.data.part_work_day.data
        uni.hideLoading()
    },
    methods: {
        transformToPickerData(raw) {
            return raw.map(item => ({
                text: item.name,
                value: item.id,
                children: (item.children || []).map(child => ({
                    text: child.name,
                    value: child.id
                }))
            }))
        },
        onnodeclick(e) {
        },
        onpopupopened(e) {
        },
        onpopupclosed(e) {
        },
        onchange(e) {
            console.log('onchange:', e);
            if (e.detail.value.length === 0) {
                this.industry1 = ''
                this.industry2 = ''
            } else {
                this.industry1 = e.detail.value[0].value
                this.industry2 = e.detail.value[1].value
                console.log(this.industry1, this.industry2)
            }

        },
        opennew() {
            this.posipopup = true
        },
        closenew() {
            this.posipopup = false
        },
        toggleDateSelection() {
            // 切换高亮状态
            this.isDateSelected = !this.isDateSelected;
            console.log(this.isDateSelected)


            if (this.isDateSelected == false) {
                this.timestart = ''
                this.timeend = ''
            } else {
                this.calenopen();
            }
        },
        confirm(e) {
            console.log(e)
            this.timestart = e.range.before
            this.timeend = e.range.after
        },
        calenclose() {
            console.log(this.timestart)
            if (this.timestart != '' && this.timeend != '') {
                this.isDateSelected = true
            } else {
                this.isDateSelected = false
                console.log(111)
            }
        },
        calenopen() {
            this.$refs.calendar.open()

        },
        toggleWorkPeriod(index) {
            console.log(index, '$$')
            if (index === 0) {
                // 如果选中的是第一项，则清空其他选项并只选中第一项
                this.workPeriodOptions = [0];
            } else {

                // 切换当前选项的选中状态
                const firstIndex = this.workPeriodOptions.indexOf(0);
                if (firstIndex > -1) {
                    this.workPeriodOptions.splice(firstIndex, 1);
                }

                // 切换当前选项的选中状态
                const selectedIndex = this.workPeriodOptions.indexOf(index);
                if (selectedIndex > -1) {
                    // 如果已选中，则取消选中
                    this.workPeriodOptions.splice(selectedIndex, 1);
                } else {
                    // 如果未选中，则添加到选中列表
                    this.workPeriodOptions.push(index);
                }
            }
        },
        partchTab(index) {
            this.partTab = index;
        },
        selectWorkTime(item) {
            console.log(item)
            this.selectedWorkTime = item.id;
            this.workTimedata = item
            console.log(this.workTimedata)
        },
        selectWorkday(item) {
            this.selectedworkday = item.id;
            this.workingday = item
        },
        toggleBenefit(index) {
            const selectedIndex = this.selectedIndexes.indexOf(index);
            console.log(selectedIndex, '这是从')
            if (selectedIndex > -1) {
                // 如果已选中，则取消选中
                this.selectedIndexes.splice(selectedIndex, 1);
            } else {
                // 如果未选中，则添加到选中列表
                this.selectedIndexes.push(index);
            }
        },
        toggleKeyword(keyword) {
            const selectedIndex = this.selectedKeywords.indexOf(keyword.id);
            if (selectedIndex > -1) {
                // 如果已选中，则取消选中
                this.selectedKeywords.splice(selectedIndex, 1);
            } else {
                // 检查是否已达到最大选择数量
                if (this.selectedKeywords.length >= 6) {
                    uni.showToast({
                        title: '最多选择6个关键词',
                        icon: 'none'
                    });
                    return;
                }
                // 如果未选中，则添加到选中列表
                this.selectedKeywords.push(keyword.id);
                console.log(this.selectedKeywords, '这是选中的关键词')
            }
        },
        Confirm() {
            if (!this.chanpassword) {
                return;
            }
            this.formData.phone = this.chanpassword
            this.showpopup = false
        },
        Confirmlate() {
            if (!this.recruitment) {
                return;
            }
            this.formData.number = this.recruitment
            this.showpopup = false
        },
        Confirmlatebtn() {
            this.showpopup = false
            this.partpopup = false
            this.posipopup = false
        },
        open(value) {
            console.log(value, '@@@')
            // console.log('open');
            this.showpopup = true
            if (value === 'search') {
                this.tablate = 1
            } else if (value === 'recruit') {
                this.tablate = 2
            } else if (value === 'welfare') {
                this.tablate = 3
            } else if (value === 'period') {
                this.partpopup = true
            }

            // if()
        },
        close() {
            this.showpopup = false
            this.tempSelectedIndexes = [];
            this.selectedIndexes = [];

            this.partpopup = false
            // console.log('close');
        },
        focusInput(refName) {
            this.$refs[refName].click(); // 调用输入框的 focus 方法
        },
        handleBack() {
            uni.navigateBack({
                delta: 1 // 返回的页面数，如果 delta 大于现有页面数，则返回到首页
            })
        },
        handleService() {
            uni.showToast({
                title: '正在连接客服...',
                icon: 'none'
            });
        },
        handleHelp() {
            uni.showToast({
                title: '帮助中心',
                icon: 'none'
            });
        },
        // 触发提交表单
        async submit() {

            if (this.industry1 == '' || this.industry2 == '' || this.money == '' || this.formData.name == '' || this.formData.introduction == '' || this.minmoney == '' || this.maxmoney == '') {
                uni.showToast({
                    title: '请填写完整信息',
                    icon: 'none'
                });
            } else {
                if (this.currentTab == 0) {
                    let selectedSettlement = this.settlement.find(item => item.selected);

                    let params = {
                        type: 1,
                        name: this.formData.name,
                        content: this.formData.introduction,
                        three_cityid: this.city.adcode,
                        formatted_address: this.city.address,
                        lat: this.city.latitude,
                        lon: this.city.longitude,
                        experience: this.workTime.id,
                        education: this.education.id,
                        job1: this.work.categoryid,
                        job2: this.work.positionid,
                        job3: this.work.id,
                        max_salary: this.maxmoney,
                        min_salary: this.minmoney,
                        is_negotiable: selectedSettlement.type,
                        welfare: this.selectedIndexes.join(','),
                        keywords: this.selectedKeywords.join(','), // 添加选中的关键词
                        sex: this.gender.id,
                        age: this.age.id,
                        address_name: this.city.name,
                        address: this.formData.address,
                        skill_ids: this.selectedKeywords.join(','),
                        classid1: this.industry1,
                        classid2: this.industry2
                    }
                    
                    let res = await dictApi.postjobInsert(params);
                    if (res.code == 200) {
                        uni.showToast({
                            title: '发布成功',
                            icon: 'none'
                        });
                        setTimeout(() => {
                            uni.switchTab({
                                url: '/pages/position/position'
                            });
                        }, 1000);
                        uni.hideLoading()
                    } else {
                        uni.showToast({
                            title: res.msg,
                            icon: 'none'
                        });

                    }
                } else {
                    let params = {
                        type: 2,
                        name: this.formData.name,
                        content: this.formData.introduction,
                        three_cityid: this.city.adcode,
                        lat: this.city.latitude,
                        lon: this.city.longitude,
                        experience: this.workTime.id,
                        education: this.education.id,
                        job1: this.work.categoryid,
                        job2: this.work.positionid,
                        job3: this.work.id,
                        sex: this.gender.id,
                        salary: this.money,
                        salary_type: this.salary.id,
                        billing_cycle: this.selectedSettlementId,
                        formatted_address: this.city.address,
                        address_name: this.city.name,
                        address: this.formData.address,
                        age: this.age.id,
                        job_type: this.partTab,
                        worktype: this.workTimedata.id,
                        worktime: this.workPeriodOptions.join(','),
                        keywords: this.selectedKeywords.join(','), // 添加选中的关键词
                        classid1: this.industry1,
                        classid2: this.industry2
                    }
                    if (this.partTab == 0) {
                        params.workdate = this.workingday.id
                    } else {
                        params.start_date = this.timestart
                        params.end_date = this.timeend
                    }
                    let reslate = await dictApi.postjobInsert(params);
                    if (reslate.code == 200) {
                        uni.showToast({
                            title: '发布成功',
                            icon: 'none'
                        });
                        setTimeout(() => {
                            uni.switchTab({
                                url: '/pages/position/position'
                            });
                        }, 1000);
                        uni.hideLoading()
                    } else {
                        uni.showToast({
                            title: reslate.msg,
                            icon: 'none'
                        });
                        uni.hideLoading()
                    }
                }
            }
        },
        switchTab(index) {
            this.currentTab = index;
        },
        selectSettlement(index) {

            this.settlement.forEach((item, i) => {
                item.selected = i === index; // 仅选中当前点击的项
            });
        },
        selectSettle(index) {
            // 更新选中状态
            this.billing.forEach((item, i) => {
                item.selected = i === index; // 仅选中当前点击的项
            });
            // 保存选中的 id
            this.selectedSettlementId = this.billing[index].type;
            console.log(this.selectedSettlementId, '###');
        },

        showPicker(type) {
            // 根据类型设置不同的选项
            switch (type) {
                case 'age':
                    this.show = true; // 打开选择器
                    this.ageColumns = this.ageColumns;
                    break;
                case 'gender':
                    this.show1 = true; // 打开选择器
                    this.genderColumns = this.genderColumns;
                    break;
                case 'education':
                    this.show2 = true; // 打开选择器
                    this.educationColumns = this.educationColumns;
                    break;
                case 'workTime':
                    this.show3 = true; // 打开选择器
                    this.workTimeColumns = this.workTimeColumns;
                    break;
                // case 'benefits':
                //     this.show4 = true; // 打开选择器
                //     this.benefitsColumns = this.benefitsColumns;
                //     break;
                case 'salary':
                    this.show5 = true; // 打开选择器
                    this.salaryColumns = this.salaryColumns;
                    break;
                case 'period':
                    this.show6 = true; // 打开选择器
                    this.periodColumns = this.periodColumns;
                    break;
            }
            this.currentPickerType = type; // 保存当前选择的类型
        },
        onPickerConfirm(value) {
            switch (this.currentPickerType) {
                case 'age':
                    this.age = value.value[0];
                    this.show = false; // 关闭选择器
                    break;
                case 'gender':
                    this.gender = value.value[0];
                    console.log(this.gender);
                    this.show1 = false; // 关闭选择器
                    break;
                case 'education':
                    this.education = value.value[0];
                    this.show2 = false; // 关闭选择器
                    break;
                case 'workTime':
                    this.workTime = value.value[0];
                    this.show3 = false; // 关闭选择器
                    break;
                // case 'benefits':
                //     this.benefits = value.value[0];
                //     this.show4 = false; // 关闭选择器
                //     break;
                case 'salary':
                    this.salary = value.value[0];
                    this.show5 = false; // 关闭选择器
                    break;
                case 'period':
                    this.period = value.value[0];
                    this.show6 = false; // 关闭选择器
                    break;
            }

        }
    }
}
</script>

<style lang="scss" scoped>
.position-container {
    min-height: 100vh;
    // background-color: #e1f8e4;
    // padding: 0 20rpx;

}

// ::v-deep .u-navbar__content {
//     background-color: #e1f8e4 !important;
// }

.header {
    // height: 88rpx;
    // display: flex;
    // justify-content: space-between;
    // align-items: center;
    // background-color: #e1f8e4;

    .navbar-right {
        display: flex;
        align-items: center;
        height: 100%;
    }

    .avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        margin-right: 20rpx;
    }

    .help-text {
        font-size: 28rpx;
        color: #333;
    }

    .navbar-right .service,
    .navbar-right .help {
        display: flex;
        // flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-left: 40rpx;
        height: 100%;
    }

    .service-avatar {
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;
        overflow: hidden;
        margin-bottom: 4rpx;
    }

    .service-avatar image {
        width: 100%;
        height: 100%;
    }

    .navbar-right text {
        font-size: 24rpx;
        margin-top: 4rpx;
    }
}

.position-content {

    font-weight: bold;
    color: #333;
    margin: 20rpx 0;
    padding: 0 20rpx;

    .position-title {
        width: 100%;
        font-size: 44rpx;
    }

    .position-item {
        display: flex;
        align-items: center;
        width: 100%;
        height: 70rpx;
        align-items: center;
        font-size: 30rpx;

        .position-item-left {
            color: #828b7d;
            font-size: 25rpx;
        }

        .position-item-right {
            background-color: white;
            color: #828b7d;
            height: 70%;
            margin-left: 10rpx;
            padding: 0 20rpx;
            border-radius: 10rpx;

            .position-item-right-text {
                color: #10d2c3;
                padding: 0rpx 10rpx;
            }
        }
    }
}

.title {
    font-size: 54rpx;
    font-weight: bold;
    color: #333;
    margin: 30rpx 0;
    text-align: center;
}

.position-anchor {
    width: 95%;
    // height: 100rpx;
    background-color: #fff;
    border-radius: 20rpx;
    margin: 0 auto;
    margin-top: 20rpx;

    // padding: 0 20rpx;
    .position-anchor-form {
        padding: 0rpx 20rpx;
    }

    .position-tab-container {
        padding: 20rpx;

        .position-tab {
            display: flex;

            .tab-item {
                width: 10%;
                text-align: center;
                padding: 24rpx 0;
                font-size: 32rpx;
                color: #999;
                position: relative;
                cursor: pointer;

            }

            .tab-item:nth-child(2) {
                margin-left: 30rpx;
            }

            .tab-item.active {
                color: black;
                font-weight: bold;
                border-bottom: 4rpx solid black;
                font-weight: 400;
                // width: fit-content; // 让下划线宽度根据内容自适应
                // margin: 0 auto; // 居中对齐
            }


        }

        .tab-content {
            padding: 0rpx 0;
        }

        .tab-latemoney {
            border: 1px solid #ccc;
            margin-top: 20rpx;
            width: 100%;
            border-radius: 15rpx;
            padding: 0rpx 0rpx 20rpx 0rpx;

            .tab-moneyinterval {
                display: flex;
                align-items: center;

                .maxmoney {
                    margin-left: 40rpx;
                }
            }

            .tab-settlement {
                display: flex;
                width: 100%;
                margin-top: 10rpx;

                .tab-settlement-item {
                    width: 100rpx;
                    background-color: #f7f6fb;
                    text-align: center;
                    margin-left: 30rpx;
                    border-radius: 10rpx;
                    font-size: 28rpx;
                }

                .tab-settlement-item.active {
                    background-color: #e1f5f4; // 选中背景色
                    border: 1px solid #a2d8d6;
                    color: #24aba7;
                }
            }
        }

        .tab-latemoney.error {
            border: 1px solid red; // 输入框边框变红
        }

        .error-message {
            color: red;
            font-size: 24rpx;
            margin-top: 10rpx;
        }

        .tab-latemoneypart {
            border: 1px solid #ccc;
            margin-top: 20rpx;
            width: 100%;
            border-radius: 15rpx;
            padding: 0rpx 0rpx 20rpx 0rpx;

            .tab-moneyinterval {
                width: 90%;
                display: flex;
                align-items: center;

                .maxmoney {
                    margin-left: 40rpx;
                }
            }

            .tab-settlement {
                display: flex;
                width: 100%;
                margin-top: 10rpx;

                .tab-settlement-item {
                    width: 100rpx;
                    background-color: #f7f6fb;
                    text-align: center;
                    margin-left: 30rpx;
                    border-radius: 10rpx;
                    font-size: 28rpx;
                }

                .tab-settlement-item.active {
                    background-color: #e1f5f4; // 选中背景色
                    border: 1px solid #a2d8d6;
                    color: #24aba7;
                }
            }
        }
    }

    .position-description {
        padding: 0rpx 20rpx;
    }

    .position-description-phone {
        display: flex !important;
        width: 100%;

        ::v-deep .uni-forms-item__label {
            width: 55% !important;
            color: black;
            font-size: 24rpx;
        }

        ::v-deep .uni-forms-item__content {
            width: 35% !important;
            flex: none;
            display: flex !important;
        }

        ::v-deep .is-input-border {
            border: none;
        }

        ::v-deep .uni-easyinput__content-input {
            padding-left: 0rpx !important;
            width: 220rpx;
            background-color: #fff;
            color: #333;
        }
    }

    .position-folding {
        .custom-title {
            padding: 0 20rpx;
            display: flex;
            margin-bottom: 20rpx;
            margin-top: 30rpx;

            .custom-title-text {
                font-size: 30rpx;
                color: black;
            }

            .custom-title-p {
                font-size: 24rpx;
                color: #555;
                padding: 5rpx 20rpx;
                background: #f7f6fb;
                margin-left: 10rpx;
            }
        }

        .info-list {
            background-color: #fff;
            border-radius: 10rpx;
            padding: 20rpx;
        }

        .info-item {
            // display: flex;
            // justify-content: space-between;
            // align-items: center;
            padding: 20rpx 0;
            border-bottom: 1px solid #f0f0f0;
        }

        // .info-item:last-child {
        //     border-bottom: none;
        // }

        .info-label {
            font-size: 28rpx;
            color: #333;
        }

        .info-value {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 28rpx;
            color: #999;
        }

        .popup-content {
            padding: 20rpx;
            text-align: center;

        }

        .form-item {
            // margin-bottom: 40rpx;
        }

        .label {
            font-size: 28rpx;
            // font-weight: 600;
            // margin-bottom: 20rpx;
            display: block;
        }

        .required {
            color: #f00;
            margin-right: 6rpx;
        }
    }
}

.position-submit {
    position: fixed;
    bottom: 0rpx;
    left: 0rpx;
    width: 100%;
    height: 140rpx;
    margin-top: 20rpx;
    background-color: white;
    border-radius: 30rpx 30rpx 0 0;

    .uni-btn {
        width: 90%;
        height: 80rpx;
        margin: 0 auto;
        margin-top: 20rpx;
        background-color: #06d1cf;
        color: white;
        font-size: 32rpx;
        border-radius: 10rpx;
        font-weight: 500;
    }
}

::v-deep.uni-forms-item {
    display: block !important;
    margin-bottom: 0rpx !important;
}

::v-deep .uni-forms-item__label {
    width: 100% !important;
    color: black;

}

// ::v-deep .u-transition {
//     height: 50%;
// }

::v-deep(.u-picker__content) {
    max-height: 80%; // 内容区域高度
    padding: 20rpx; // 增加内边距
}

::v-deep .u-slide-up-enter-active {
    // height: 30%;
}

.popup-late {
    padding: 20rpx;
    height: 500rpx;

    .titlelate {
        height: 90%;
        overflow: hidden;
        overflow-y: auto;
    }

    .popup-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 40rpx;
        text-align: center;
        font-weight: 600;

        text {
            font-size: 30rpx;
            // color: #333;
            cursor: pointer;
        }
    }

    .popup-content {
        padding-top: 30rpx;

        .u-border {
            border: 1px solid #ccc;
            border-radius: 20rpx;
            padding: 0 20rpx;
            margin-top: 30rpx;
        }

        .input-error {
            border: 1px solid red;
        }
    }

    .popup-button {
        width: 95%;
        position: fixed;
        bottom: 40rpx;
        margin: 0 auto;

        button {
            background-color: #06d1cf;
            color: white;
        }
    }

    .popup-item {
        width: 30%;
        height: 70rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 28rpx;
        // margin: 20rpx;
        margin-top: 20rpx;
        border-radius: 20rpx;
        background-color: #f0f1f6;
    }

    .popup-item.active {
        background-color: #e0f5f4;
        /* 选中时的背景色 */
        color: #189e98;
        /* 选中时的文字颜色 */
        border: 1px solid #a8e0dd;
        /* 选中时的边框颜色 */
    }

    .popup-keywords {
        width: 30%;
        height: 35px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        margin-top: 10px;
        border-radius: 10px;
        background-color: #f0f1f6;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .popup-keywords.active {
        background-color: #e0f5f4;
        /* 选中时的背景色 */
        color: #189e98;
        /* 选中时的文字颜色 */
        border: 1px solid #a8e0dd;
        /* 选中时的边框颜色 */
    }
}

.error {
    border: 1px solid red;
}

.tab-container {
    height: 1400rpx;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .submit-btn {
        width: 100%;
        height: 200rpx;
        border-top: 1px solid #f2f2f2;

        .uni-btn {
            width: 96%;
            margin: 0 auto;
            background-color: #06d1cf;
            color: white;
            margin-top: 30rpx;
        }
    }
}

.tab-header {
    display: flex;
    justify-content: space-around;
    background-color: #f6f7f9;
    padding: 20rpx 0;
    border-bottom: 1px solid #e0e0e0;
    align-items: center;

    .tab-item {
        font-size: 32rpx;
        color: #666;
        padding: 20rpx 0;
        // flex: 1;
        text-align: center;
        cursor: pointer;
    }

    .tab-item.active {
        color: #333;
        font-weight: bold;
        border-bottom: 4rpx solid #06d1cf;
    }
}



.tab-content {
    flex: 1;
    padding: 20rpx;
}

.section {
    margin-bottom: 30rpx;
    display: flex;
    flex-wrap: wrap;
}

.section-title {
    font-size: 32rpx;
    color: black;
    margin-bottom: 10rpx;
    width: 100%;
}

.section-item {
    display: inline-block;
    // padding: 10rpx 20rpx;
    margin: 10rpx 10rpx 0 10rpx;
    font-size: 28rpx;
    color: #666;
    background-color: #f6f7f9;
    border-radius: 10rpx;
    width: 30%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 80rpx;
    color: black;
}

.section-item.active {
    color: #06d1cf;
    background-color: #e0f5f4;
    border: 1px solid #06d1cf;
}

.footer {
    padding: 20rpx;
    background-color: #fff;
    border-top: 1px solid #e0e0e0;
}

::v-deep .uni-section-header {
    padding: 14rpx 20rpx !important;
}
</style>