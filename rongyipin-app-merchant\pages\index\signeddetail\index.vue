<template>
    <view class="settings-container" @click="handleContainerClick">
        <!-- 状态栏和导航栏 -->
        <view class="header">
            <u-navbar height="44px" title="" :autoBack="true" :leftIconSize="30" :leftIconColor="'#333'"
                safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>

        <!-- 职位信息 -->
        <view class="position-info" v-if="option.job_info.name">
            <text>{{ option.job_info.name }}</text>
            <uni-icons type="right" size="16"></uni-icons>
        </view>

        <scroll-view scroll-y class="scroll-view" :show-scrollbar="false">
            <!-- 用户基本信息 -->
            <view class="signed-user">
                <view class="signed-title">
                    <view class="title-name">
                        <text class="username">{{ option.username }}</text>
                        <text class="subtitle">{{ option.job_info.status_name }}</text>
                        <text class="subtitle" v-if="option.job_info.status != 3">已报名3天，建议立即联系</text>
                    </view>
                    <view class="title-img">
                        <image src="/static/demo/avatar.jpg" mode="aspectFill"></image>
                    </view>
                </view>

                <!-- 用户信息网格 -->
                <view class="user-info-grid">
                    <view class="info-item" v-if="option.sex">
                        <text class="info-label">性别</text>
                        <text class="info-value">{{ option.sex_name }}</text>
                    </view>
                    <view class="info-item" v-if="option.age">
                        <text class="info-label">年龄</text>
                        <text class="info-value">{{ option.age }}</text>
                    </view>
                    <view class="info-item" v-if="option.degree_name">
                        <text class="info-label">学历</text>
                        <text class="info-value">{{ option.degree_name }}</text>
                    </view>
                    <view class="info-item" v-if="option.work_name">
                        <text class="info-label">身份</text>
                        <text class="info-value">{{ option.work_name }}</text>
                    </view>
                    <view class="info-item" v-if="option.job_info.distance">
                        <text class="info-label">位置</text>
                        <text class="info-value">{{ option.job_info.distance }}km</text>
                    </view>
                    <!-- <view class="info-item">
                        <text class="info-label">认证</text>
                        <text class="info-value">芝麻工作证</text>
                    </view> -->
                </view>

                <!-- 备注信息 -->
                <view class="remark-section">
                    <view class="remark-header">
                        <text></text>
                        <text class="remark-date">{{ formatTimestamp(option.job_info.apply_time) }}报名</text>
                    </view>
                </view>
            </view>

            <!-- 求职意向 -->
            <view class="section-block job-intention">
                <view class="section-title">求职意向</view>
                <view class="intention-item">
                    <text class="intention-label">工作时间:</text>
                    <text class="intention-value">工作日、周末节假日、寒暑假</text>
                </view>
                <view class="intention-item">
                    <text class="intention-label">工作时段:</text>
                    <text class="intention-value">早班、白班</text>
                </view>
                <view class="intention-item">
                    <text class="intention-label">长期工作:</text>
                    <text class="intention-value">否</text>
                </view>
                <view class="intention-item">
                    <text class="intention-label">每周工作:</text>
                    <text class="intention-value">3～4天、5天以上</text>
                </view>
            </view>

            <!-- 教育经历 -->
            <view class="section-block education" v-if="option.resume_edu.length > 0">
                <view class="section-title">教育经历</view>
                <view class="education-item" v-for="(item, index) in option.resume_edu" :key="index">
                    <text class="education-date">{{ item.sdate_format }} - {{ item.edate_format }}</text>
                    <text class="education-degree">{{ item.name }}</text>
                </view>
            </view>
            <!-- 工作经历 -->
            <view class="section-block education" v-if="option.resume_work.length > 0">
                <view class="section-title">工作经历</view>
                <view class="education-item" v-for="(item, index) in option.resume_work" :key="index">
                    <text class="education-date">{{ item.sdate_format }} - {{ item.edate_format }}</text>
                    <text class="education-degree">{{ item.name }}</text>
                </view>
            </view>

            <!-- 技能证书 -->
            <!-- <view class="section-block skills">
                <view class="section-title">技能证书</view>
                <view class="skill-tags">
                    <text class="skill-tag">文化娱乐</text>
                    <text class="skill-tag">Office</text>
                    <text class="skill-tag">方言</text>
                    <text class="skill-tag">普通话</text>
                    <text class="skill-tag">健康证</text>
                </view>
            </view> -->

            <!-- 底部状态 -->
            <view class="bottom-status">
                <text>已经看完了，快去处理吧～</text>
            </view>

            <!-- 底部占位，防止内容被底部按钮遮挡 -->
            <view style="height: 200rpx;"></view>
        </scroll-view>

        <!-- 标记弹窗 -->
        <view v-if="showMarkPopup" class="mark-popup-container" @click.stop>
            <view class="mark-popup">
                <view class="popup-title">标记状态</view>
                <view class="popup-options">
                    <view class="popup-option" @click="selectMarkStatus('contacted_suitable')">
                        <view class="option-radio">
                            <view class="radio-dot" :class="{ 'checked': markStatus === 'contacted_suitable' }"></view>
                        </view>
                        <text class="option-text">已联系-合适</text>
                    </view>
                    <view class="popup-option" @click="selectMarkStatus('contacted_not_suitable')">
                        <view class="option-radio">
                            <view class="radio-dot" :class="{ 'checked': markStatus === 'contacted_not_suitable' }">
                            </view>
                        </view>
                        <text class="option-text">已联系-不合适</text>
                    </view>
                    <view class="popup-option" @click="selectMarkStatus('not_contacted')">
                        <view class="option-radio">
                            <view class="radio-dot" :class="{ 'checked': markStatus === 'not_contacted' }"></view>
                        </view>
                        <text class="option-text">未联系</text>
                    </view>
                    <view class="popup-option" @click="selectMarkStatus('pending_reply')">
                        <view class="option-radio">
                            <view class="radio-dot" :class="{ 'checked': markStatus === 'pending_reply' }"></view>
                        </view>
                        <text class="option-text">已联系</text>
                    </view>
                </view>
                <!-- 小三角 -->
                <view class="popup-arrow"></view>
            </view>
        </view>

        <!-- 底部操作按钮 -->
        <view class="action-buttons" @click.stop>
            <view class="mark-button" @click.stop="toggleMarkPopup"
                style="display: flex; height: 70rpx;align-items: center;">
                标记<u-icon style="margin-top: 10rpx;"
                    :name="showMarkPopup ? 'arrow-up-fill' : 'arrow-down-fill'"></u-icon>
            </view>
            <u-button class="action-btn call-btn" :disabled="showMarkPopup" @click="handleCallClick"
                style="border: none !important;">
                <text>打电话</text>
            </u-button>
            <u-button class="action-btn message-btn" :disabled="showMarkPopup" @click="handleMessageClick">
                <text>发消息</text>
            </u-button>
            <u-button class="action-btn wechat-btn" :disabled="showMarkPopup" @click="handleWechatClick">
                <text>加微信</text>
            </u-button>
        </view>
    </view>
</template>

<script>
import { applyApi } from "@/utils/api"
export default {
    data() {
        return {
            option: {},
            showMarkPopup: false,
            markStatus: '',
            optionitem: {}
        }
    },
    async onLoad(options) {
        if (options) {
            try {
                console.log('接收到的参数:', options);
                this.optionitem = options
                // 获取参数数量
                const paramCount = Object.keys(options).length;

                if (paramCount === 1) {
                    let res = await applyApi.getTalent({
                        seeker_id: options.seeker_id
                    });
                    if (res.code == 200) {
                        this.option = res.data
                        const statusMap = {
                            0: 'not_contacted',
                            1: 'pending_reply',
                            2: 'contacted_suitable',
                            3: 'contacted_not_suitable'
                        };
                        this.markStatus = statusMap[this.option.job_info.status] || 'not_contacted';
                        uni.hideLoading()
                    } else {
                        uni.showToast({
                            title: res.msg,
                            icon: 'none'
                        });
                    }
                } else if (paramCount === 2) {
                    let res = await applyApi.getTalent({
                        seeker_id: options.seeker_id,
                        job_id: options.job_id
                    });
                    if (res.code == 200) {
                        this.option = res.data
                        const statusMap = {
                            0: 'not_contacted',
                            1: 'pending_reply',
                            2: 'contacted_suitable',
                            3: 'contacted_not_suitable'
                        };
                        this.markStatus = statusMap[this.option.job_info.status] || 'not_contacted';
                        uni.hideLoading()
                    } else {
                        uni.showToast({
                            title: res.msg,
                            icon: 'none'
                        });
                    }
                }


            } catch (e) {
                console.error('解析参数失败', e);
            }
        }
    },
    methods: {
        formatTimestamp(timestamp) {
            const date = new Date(timestamp * 1000); // 将秒转换为毫秒
            const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需要加 1
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            return `${month}/${day} ${hours}:${minutes}`; // 返回格式化的字符串
        },
        handleBack() {
            uni.navigateBack();
        },
        toggleMarkPopup() {
            console.log('点击标记按钮，当前状态:', this.showMarkPopup);
            this.showMarkPopup = !this.showMarkPopup;
            console.log('切换后状态:', this.showMarkPopup);
        },
        hideMarkPopup() {
            this.showMarkPopup = false;
        },
        async selectMarkStatus(status) {
            this.markStatus = status;

            this.markStatus = status;

            // 根据选择的状态打印不同的信息
            const statusMap = {
                'contacted_suitable': { text: '已联系-合适', id: 2 },
                'contacted_not_suitable': { text: '已联系-不合适', id: 3 },
                'not_contacted': { text: '未联系', id: 0 },
                'pending_reply': { text: '已联系', id: 1 }
            };

            const selectedStatus = statusMap[status];
            console.log('选择的标记状态:', selectedStatus.text, '对应的ID:', selectedStatus.id);
            let res = await applyApi.changeApply({ seeker_id: this.optionitem.seeker_id, status: selectedStatus.id })
            if (res.code == 200) {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });

                const paramCount = Object.keys(this.optionitem).length;

                if (paramCount === 1) {
                    let res = await applyApi.getTalent({
                        seeker_id: this.optionitem.seeker_id
                    });
                    if (res.code == 200) {
                        this.option = res.data
                        const statusMap = {
                            0: 'not_contacted',
                            1: 'pending_reply',
                            2: 'contacted_suitable',
                            3: 'contacted_not_suitable'
                        };
                        this.markStatus = statusMap[this.option.job_info.status] || 'not_contacted';
                        uni.hideLoading()
                    } else {
                        uni.showToast({
                            title: res.msg,
                            icon: 'none'
                        });
                    }
                }
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }

            // 关闭弹窗
            this.showMarkPopup = false;
        },
        handleContainerClick() {
            // 点击容器时关闭弹窗
            console.log('点击容器，弹窗状态:', this.showMarkPopup);
            if (this.showMarkPopup) {
                this.showMarkPopup = false;
                console.log('关闭弹窗');
            }
        },
        handleCallClick() {
            // 打电话按钮的点击事件
            console.log('点击打电话按钮');
            // 这里添加您的打电话逻辑
            // this.hsdahd();
        },
        handleMessageClick() {
            // 发消息按钮的点击事件
            console.log('点击发消息按钮');
            // 这里添加您的发消息逻辑
        },
        handleWechatClick() {
            // 加微信按钮的点击事件
            console.log('点击加微信按钮');
            // 这里添加您的加微信逻辑
        }
    }
}
</script>
<style lang="scss" scoped>
.settings-container {
    min-height: 100vh;
    background-color: #f0f1f6;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow-x: hidden;
    box-sizing: border-box;
}

.header {
    height: 88rpx;
    background-color: #f0f1f6;
    width: 100%;
}

.position-info {
    display: flex;
    align-items: center;
    padding: 20rpx 30rpx;
    font-size: 28rpx;
    color: #666;
    width: 100%;
    box-sizing: border-box;

    text {
        margin-right: 10rpx;
    }
}

.scroll-view {
    flex: 1;
    height: calc(100vh - 88rpx - 200rpx);
    width: 100%;
    box-sizing: border-box;
    padding: 0 30rpx;
    overflow-x: hidden;
}

.signed-user {
    background-color: white;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    width: 100%;
    box-sizing: border-box;

    .signed-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 30rpx;
        width: 100%;

        .title-name {
            .username {
                font-size: 48rpx;
                font-weight: 600;
                display: block;
            }

            .subtitle {
                font-size: 28rpx;
                color: #83888d;
                margin-top: 10rpx;
                display: block;
            }
        }

        .title-img {
            width: 120rpx;
            height: 120rpx;
            border-radius: 60rpx;
            overflow: hidden;
            flex-shrink: 0;

            image {
                width: 100%;
                height: 100%;
            }
        }
    }
}

.user-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20rpx;
    margin-bottom: 30rpx;
    width: 100%;
    box-sizing: border-box;

    .info-item {
        background-color: #f8f9fa;
        border-radius: 16rpx;
        padding: 20rpx;
        text-align: center;

        .info-label {
            font-size: 26rpx;
            color: #999;
            display: block;
            margin-bottom: 10rpx;
        }

        .info-value {
            font-size: 30rpx;
            color: #333;
            font-weight: 500;
        }
    }
}

.user-photos {
    display: flex;
    margin-bottom: 30rpx;
    width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;

    image {
        width: 160rpx;
        height: 200rpx;
        border-radius: 12rpx;
        margin-right: 20rpx;
        object-fit: cover;
        flex-shrink: 0;
    }
}

.remark-section {
    width: 100%;
    box-sizing: border-box;

    .remark-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10rpx;
        width: 100%;

        text {
            font-size: 28rpx;
            color: #333;
        }

        .remark-date {
            font-size: 24rpx;
            color: #999;
        }
    }
}

.section-block {
    background-color: white;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    width: 100%;
    box-sizing: border-box;
}

.section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
}

.intention-item {
    display: flex;
    margin-bottom: 20rpx;
    width: 100%;
    box-sizing: border-box;

    .intention-label {
        font-size: 28rpx;
        color: #666;
        width: 150rpx;
        flex-shrink: 0;
    }

    .intention-value {
        font-size: 28rpx;
        color: #333;
        flex: 1;
        word-break: break-all;
    }
}

.education-item {
    width: 100%;

    .education-date {
        font-size: 26rpx;
        color: #999;
        display: block;
        margin-bottom: 10rpx;
    }

    .education-degree {
        font-size: 30rpx;
        color: #333;
    }
}

.skill-tags {
    display: flex;
    flex-wrap: wrap;
    width: 100%;

    .skill-tag {
        background-color: #f0f1f6;
        color: #666;
        font-size: 26rpx;
        padding: 10rpx 20rpx;
        border-radius: 30rpx;
        margin-right: 20rpx;
        margin-bottom: 20rpx;
    }
}

.bottom-status {
    text-align: center;
    color: #999;
    font-size: 26rpx;
    margin: 30rpx 0;
    width: 100%;
}

// 标记弹窗样式
.mark-popup-container {
    position: fixed;
    bottom: 280rpx;
    left: 30rpx;
    z-index: 1000;
    width: 320rpx;
}

.mark-popup {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    border-radius: 16rpx;
    padding: 30rpx 25rpx;
    box-sizing: border-box;
    box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.3);
    border: 1rpx solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.popup-arrow {
    position: absolute;
    bottom: -12rpx;
    left: 50rpx;
    width: 0;
    height: 0;
    border-left: 12rpx solid transparent;
    border-right: 12rpx solid transparent;
    border-top: 12rpx solid #4a5568;
    z-index: 1001;

    &::before {
        content: '';
        position: absolute;
        bottom: 1rpx;
        left: -12rpx;
        width: 0;
        height: 0;
        border-left: 12rpx solid transparent;
        border-right: 12rpx solid transparent;
        border-top: 12rpx solid rgba(255, 255, 255, 0.1);
    }
}

.popup-title {
    font-size: 28rpx;
    color: #ffffff;
    text-align: center;
    margin-bottom: 35rpx;
    font-weight: 500;
    line-height: 1.3;
    letter-spacing: 0.5rpx;
}

.popup-options {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.popup-option {
    display: flex;
    align-items: center;
    padding: 12rpx 8rpx;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 8rpx;

    &:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }

    &:active {
        opacity: 0.8;
        transform: scale(0.98);
    }
}

.option-radio {
    width: 34rpx;
    height: 34rpx;
    border: 2rpx solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;
    flex-shrink: 0;
    background-color: transparent;
    transition: border-color 0.2s ease;
}

.radio-dot {
    width: 18rpx;
    height: 18rpx;
    border-radius: 50%;
    background-color: transparent;
    transition: all 0.2s ease;

    &.checked {
        background-color: #00d4aa;
        box-shadow: 0 0 8rpx rgba(0, 212, 170, 0.3);
    }
}

.option-text {
    font-size: 28rpx;
    color: #ffffff;
    flex: 1;
    font-weight: 400;
    line-height: 1.3;
    letter-spacing: 0.3rpx;
}

.mark-button {
    cursor: pointer;
    user-select: none;

    &:active {
        opacity: 0.7;
    }
}

.action-buttons {
    display: flex;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    padding: 20rpx 30rpx;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    height: 200rpx;
    box-sizing: border-box;
    width: 100%;

    .action-btn {
        flex: 1;
        height: 80rpx;
        border-radius: 10rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        margin: 0 10rpx;
        border: none !important;
        transition: opacity 0.2s ease;

        &.call-btn {
            background-color: #f0f1f6;
            color: #333;
        }

        &.message-btn {
            background-color: #f0f1f6;
            color: #333;
        }

        &.wechat-btn {
            background-color: #00c9c2;
            color: white;
        }

        // 禁用状态样式
        &.u-button--disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    }
}

::v-deep .u-navbar__content {
    background-color: #f0f1f6 !important;
}

::v-deep .uni-button:after {
    border: 1px solid red !important;
}
</style>