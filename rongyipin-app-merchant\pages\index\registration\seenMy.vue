<template>
    <view class="nearby">
        <view class="regis-title" @click="show1 = true">
            {{ positionName || '全部职位' }} <u-icon name="arrow-down-fill" size="30"
                style="margin-top: 10rpx;margin-left: 10rpx;"></u-icon>
        </view>
        <view class="regis-invi" v-if="getList.length > 0">
            <view style="display: flex;">
                <image src="@/static/app/home/<USER>" alt=""/>
                <text style="margin-left: 10rpx;font-size: 35rpx;">今日剩余 0 次邀约</text>
            </view>
            <view style="display: flex;justify-content: center; margin-right: 20rpx;">去购买<u-icon
                    name="arrow-right"></u-icon></view>
        </view>
        <view class="regis-getlist" v-if="getList.length > 0">
            <view v-for="(item, index) in getList" :key="index" class="regis-listitem">
                <view class="listitem">
                    <view class="item-img">
                        <image src="@/static/app/home/<USER>" alt=""/>
                        <view style="margin-left: 30rpx;">
                            <p>
                                <span>{{ item.username
                                }}</span>
                                <span style="display: inline-block;margin-left: 30rpx;">{{ item.state }}</span>
                            </p>
                            <p style="margin-top: 10rpx;color: #a4aaae;">{{ item.sex_name }} {{ item.age }}</p>
                        </view>
                    </view>
                    <view>
                        <u-icon name="arrow-right"></u-icon>
                    </view>
                </view>
                <view class="list-but">
                    <view style="width: 30rpx;"></view>
                    <u-button class="cat-btn" text="邀请投递"></u-button>
                </view>
            </view>
        </view>
        <view v-else>
            <p style="text-align: center;margin-top: 90rpx;"><image style="height: 100rpx;height: 100rpx;"
                    src="@/static/app/home/<USER>" alt=""/></p>
            <p style="text-align: center;">暂无报名候选人</p>
        </view>

        <u-popup :show="show1" @close="close" @open="open" :round="20" mode="bottom">
            <view class="popup-content">
                <view class="popup-top">
                    <h2>切换职位</h2>
                    <uni-icons @click="show = false" type="closeempty" size="22"></uni-icons>
                </view>
                <view style="color: #afafb3;margin-top: 20rpx;">
                    已发布 {{ jobList.length || '0' }} 个职位，切换查看候选人简历
                </view>
                <view class="popup-taball">
                    <view class="taball">
                        <view>
                            <view style="font-size: 40rpx;color: black;font-weight: 600;">全部职位</view>
                        </view>
                        <view>
                            <u-button @click="selectJob(null)" class="code-btn" text="当前"
                                :class="{ activeJob: selectedJobId === null }"></u-button>
                        </view>


                    </view>
                </view>
                <view class="popup-botom">

                </view>
                <view class="popup-joblist" v-if="!loding">
                    <view class="taball" v-for="(item, index) in jobList" :key="index">
                        <view>
                            <view style="font-size: 40rpx;color: black;font-weight: 600;">{{ item.name }}</view>
                            <view style="color: #9a9ca4;margin-top: 10rpx; display: flex;align-items: center;">
                                <view style="padding: 3rpx 10rpx;margin-right: 10rpx;border: 1px solid #f5f5f5;">{{
                                    item.merge_status == 0 ? '已暂停' : item.merge_status == 1 ? '招聘中' : item.merge_status
                                        == 2
                                        ? '待审核' : '审核驳回' }}</view>
                            </view>
                        </view>
                        <view>
                            <u-button @click="selectJob(index, item)" class="code-btn" text="当前"
                                :class="{ activeJob: selectedJobId === index }"></u-button>
                        </view>


                    </view>
                </view>
                <view style="text-align: center;" v-else>
                    <u-loading-icon></u-loading-icon>
                    <view style="color: #9a9ca4;">加载中...</view>
                </view>
            </view>
        </u-popup>
    </view>
</template>
<script>
import { positionsApi, applyApi } from "@/utils/api"
export default {
    props: {
        seenList: {
            type: [Array, Object], // 允许接收数组或对象
            default: () => [] // 默认值为空数组

        },
        lookposition: {
            type: String,
            default: ''

        },
    },
    watch: {
        // seenList: {
        //     immediate: true, // 初始化时立即执行
        //     handler(newVal) {
        //         console.log(newVal, '$$$')
        //         uni.showLoading({
        //             title: '加载中'
        //         });
        //         this.getList = newVal
        //         setTimeout(function () {
        //             uni.hideLoading();
        //         }, 500);
        //     }
        // }
    },
    data() {
        return {
            show1: false,
            loding: false,
            getList: [

            ],
            jobList: [],
            selectedJobId: null,
            page: 1,
            size: 10,
            job_id:'',
            positionName:null
        };
    },
    mounted() {

    },
    methods: {
        released() {
            uni.navigateTo({
                url: '/pages/second/second'
            });
        },
        async open() {
            console.log(this.job_id)
            
            const params = {
                page: this.page,
                size: this.size,
                status: 100,
            }
            let res = await positionsApi.postJobList(params)
            this.jobList = res.data.data
            this.loding == false
        },
        close() {
            this.show1 = false
            uni.hideLoading()
            this.job_id= ''
            // console.log('close');
        },
        async selectJob(index, item) {
            uni.removeStorage({
                key: 'itemData',
                
            });
            this.selectedJobId = index
            this.show1 =false
            if (index == null) {
                const params = {
                    page: this.page,
                    size: this.size,
                }
                let res = await applyApi.getJobView(params)
                this.getList = res.data.data
                this.positionName ='全部职位'
            } else {
                const params = {
                    page: this.page,
                    size: this.size,
                    job_id: item.id
                }
                let res = await applyApi.getJobView(params)
                this.getList = res.data.data
                this.positionName = item.name
            }
            uni.hideLoading();
            
        },
    }
}
</script>
<style lang="scss" scoped>
.nearby {
    height: 100%;
    overflow-y: auto;
    position: relative;

}

.regis-released {
    width: 100%;
    height: 360rpx;
    position: absolute;
    bottom: 0rpx;
    background-color: #0dc9d5;
    border-radius: 30rpx 30rpx 0rpx 0rpx;

    .released-btn {
        width: 90%;
        border-radius: 20rpx;
        height: 120rpx;
    }
}

.regis-title {
    padding: 20rpx;
    display: flex;
}

.regis-invi {
    width: 96%;
    margin: 0 auto;
    padding: 20rpx 0rpx;
    // padding: 20rpx;
    display: flex;
    background-color: white;
    align-items: center;
    justify-content: space-between;
    border-radius: 20rpx;

    image {
        width: 50rpx;
        height: 50rpx;
        margin-left: 20rpx;
    }
}

.regis-list {
    height: calc(100% - 370rpx);
    overflow-y: auto;
    padding: 20rpx;

    .regis-listitem {
        height: 270rpx;
        background-color: white;
        margin-top: 20rpx;
        border-radius: 20rpx;
        padding: 20rpx 20rpx;
        margin-bottom: 30rpx;

        .listitem {
            height: 100rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20rpx;

            .item-img {
                display: flex;

                image {
                    width: 100rpx;
                    height: 100rpx;
                }
            }
        }

        .list-but {
            width: 100%;
            margin-top: 45rpx;
            display: flex;

            // justify-content: space-between;
            .cat-btn {
                width: 250rpx;
                background-color: #02bdc6;
                color: white;
                font-size: 40rpx;
                border-radius: 20rpx;
                margin-right: 30rpx;
            }

        }

    }

}

.regis-getlist {
    height: calc(100% - 230rpx);
    overflow-y: auto;
    // padding: 20rpx;

    .regis-listitem {
        height: 270rpx;
        background-color: white;
        margin-top: 20rpx;
        border-radius: 20rpx;
        padding: 20rpx 20rpx;
        margin-bottom: 30rpx;

        .listitem {
            height: 100rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20rpx;

            .item-img {
                display: flex;

                image {
                    width: 100rpx;
                    height: 100rpx;
                }
            }
        }

        .list-but {
            width: 100%;
            margin-top: 45rpx;
            display: flex;

            // justify-content: space-between;
            .cat-btn {
                width: 250rpx;
                background-color: #02bdc6;
                color: white;
                font-size: 40rpx;
                border-radius: 20rpx;
                margin-right: 30rpx;
            }

        }

    }
}

.popup-content {
    height: 1200rpx;
    padding: 30rpx 20rpx;
    background-color: #f6f7f9;

    .popup-top {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .code-btn::after {
        border: none !important;
    }

    // .popup-taball {
    //     display: flex;
    //     height: 80rpx;
    //     display: flex;
    //     align-items: center;


    // }

    .taball {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 40rpx 30rpx;
        background-color: white;
        border-radius: 20rpx;
        margin-top: 20rpx;

        .code-btn {
            width: 200rpx;
            margin-left: 30rpx;
            border: none !important;
            background-color: #f6f7f9;
            border-radius: 20rpx;
            color: #989fa4;
            font-weight: 600;
        }

        .activeJob {
            background-color: #02bdc4 !important;
            color: white;
            /* 高亮边框 */
        }
    }

    .popup-botom {
        width: 50%;
        margin: 0 auto;
        border-bottom: 1px solid #ccc;
        margin-top: 50rpx;
        margin-top: 30rpx;
    }

    .popup-joblist {
        height: calc(100% - 450rpx);
        overflow: hidden;
        overflow-y: auto;
        margin-top: 20rpx;
        margin-bottom: 30rpx;
    }
}

::v-deep .uni-button {
    margin-right: none !important;
}

::v-deep .u-button__text span {
    font-size: 33rpx;
}
</style>