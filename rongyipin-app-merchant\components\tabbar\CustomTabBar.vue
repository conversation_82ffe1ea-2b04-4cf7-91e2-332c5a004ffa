<template>
    <view class="tab-bar">
        <view v-for="(item, index) in tabBarList" :key="index" class="tab-bar-item" @click="switchTab(item.pagePath)">
            <image :src="currentPage === item.pagePath ? item.selectedIconPath : item.iconPath"></image>
            <text :style="{ color: currentPage === item.pagePath ? item.selectedColor : tabBarColor }">{{ item.text }}</text>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            tabBarColor: "#969ca1",
            tabBarList: [
                {
                    pagePath: "pages/index/index",
                    text: "候选人",
                    iconPath: "static/tabbar/candidates.png",
                    selectedIconPath: "static/tabbar/candidateschoi.png",
                    selectedColor: "pink"
                },
                {
                    pagePath: "/pages/position/position",
                    text: "职位",
                    iconPath: "static/tabbar/posicc.png",
                    selectedIconPath: "static/tabbar/posiblack.png",
                    selectedColor: "black"
                },
                {
                    pagePath: "/pages/publish/publish",
                    text: "发布",
                    iconPath: "static/tabbar/add.png",
                    selectedIconPath: "static/tabbar/add.png",
                    selectedColor: "black"
                },
                {
                    pagePath: "/pages/message/message",
                    text: "消息",
                    iconPath: "/static/tabbar/candidates.png",
                    selectedIconPath: "static/tabbar/candidateschoi.png",
                    selectedColor: "black"
                },
                {
                    pagePath: "/pages/user/user",
                    text: "我的",
                    iconPath: "static/tabbar/my.png",
                    selectedIconPath: "static/tabbar/mychoice.png",
                    selectedColor: "black"
                }
            ],
            currentPage: ""
        };
    },
    onLoad() {
        this.currentPage = getCurrentPages()[getCurrentPages().length - 1].route;
    },
    methods: {
        switchTab(path) {
            console.log(path)
            uni.switchTab({
                url:  path
            });
            this.currentPage = path;
        }
    }
};
</script>

<style scoped>
.tab-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    background-color: white;
    border-top: 1px solid #ccc;
    height: 100rpx;
    align-items: center;
    justify-content: space-around;
}

.tab-bar-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 12px;
}

.tab-bar-item image {
    width: 24px;
    height: 24px;
}
</style>    