<template>
    <view class="settings-container">
        <!-- 状态栏和导航栏 -->
        <view class="header">
            <u-navbar style="background: #f6f7f9;" height="44px" title="职位详情" @rightClick="handleBack" :autoBack="true"
                :leftIconSize="30" :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>
        <view class="search-bar">
            <view class="search-bild">
                <image src="@/static/app/position/serch.png" alt=""/>
                <input
                class="search-input"
                type="text"
                placeholder="请输入职位标题关键词"
                v-model="searchKeyword"
            />
            </view>
           <text @click="search" class="serchbtn" :class="{ active: searchKeyword != '' }">搜索</text>
        </view>
        <view class="setting-list">
            <view class="list-item" v-for="(item, index) in positionList" :key="index">
                <p @click="gotodetail(item)" class="list-itemtitle"
                    :class="item.merge_status == 1 ? 'list-black' : 'list-itemtitle'"
                    v-html="highlightKeyword(item.name)"></p>
                <p style="color: #a8a8a8;">{{ formatDate(item.createtime) }}</p>
            </view>
        </view>
    </view>
</template>

<script>
import {positionsApi} from "@/utils/api"
export default {
    data() {
        return {
            searchKeyword: '', // 搜索关键词
            positionList:[],
            total:0,
            page:1,
            size:10000000
        }
    },
    computed: {

    },
    async onLoad() {

    },
    methods: {
        // 高亮关键词方法
        highlightKeyword(text) {
            if (!this.searchKeyword || !this.searchKeyword.trim()) {
                return text;
            }

            const keyword = this.searchKeyword.trim();
            const regex = new RegExp(`(${keyword})`, 'gi');
            return text.replace(regex, '<span class="highlight">$1</span>');
        },
        gotodetail(item) {
			uni.navigateTo({
				url: `/pages/position/viewDetails?type=${item.type}&job_id=${item.id}`
			});
		},
        formatDate(datetime) {
			const date = new Date(datetime);
			const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份补零
			const day = String(date.getDate()).padStart(2, '0'); // 日期补零
			return `${month}月${day}日`;
		},
        handleBack() {
            // 返回上一页逻辑
            console.log('返回上一页');
        },
        async search() {
            if (!this.searchKeyword.trim()) {
                uni.showToast({
                    title: '请输入搜索关键词',
                    icon: 'none'
                });
                return;
            }
            const params ={
                page:this.page,
                size:this.size,
                name:this.searchKeyword
            }
            let res =await positionsApi.postJobList(params)
            this.positionList =res.data.data
            this.total =res.total
            // 搜索逻辑
            console.log('搜索关键词:', this.searchKeyword);
            uni.hideLoading();
        }
    }
}
</script>

<style lang="scss" scoped>

.header {
    height: 88rpx;
    /* display: flex;
    justify-content: space-between;
    align-items: center; */
    background-color: #fff;

    .navbar {
        height: 100%;
    }
}

.settings-container {
    height: 100vh;
    background-color: #f6f7f9;
    // padding: 20rpx
}
.search-bar{
    display: flex;
    width: 95%;
    margin: 0 auto;
    height: 80rpx;
    justify-content: space-between;
    align-items: center;
    .search-bild{
        width: 85%;
        display: flex;
        background-color: white;
        align-items: center;
        border-radius: 20rpx;
        image{
            width: 35rpx;
            height: 35rpx;
            padding: 20rpx;
        }
        input{
            height: 100%;
        }
    }
    .serchbtn{
        color: #858a96;
    }
    .active{
        color: #02bdc4;
    }
}
.setting-list{
    height: calc(100% - 250rpx);
    margin-top: 30rpx;
    .list-item{
        display: flex;
        align-items: center;
        justify-content: space-between;
        // height: 100rpx;
        background-color: white;
        margin-top: 20rpx;
        padding: 20rpx;
        .list-itemtitle{
            color: #505050;

            // 高亮关键词样式
            ::v-deep .highlight {
                color: #02bdc4;
                // padding: 2rpx 4rpx;
                // border-radius: 4rpx;
            }
        }

    }
}
::v-deep .u-navbar__content{
    background-color: #f6f7f9 !important;
}
::v-deep .uni-input-placeholder{
    color: #d5d4d9;

}
</style>