<template>
    <view class="settings-container">
        <!-- 状态栏和导航栏 -->
        <view class="header">
            <u-navbar height="44px" title="账号与安全" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>

        <!-- 设置列表 -->
        <view class="settings-listlate">
            <!-- 账号与安全 -->
            <view class="settings-item" @click="chanpassword">
                <text class="item-label">修改密码</text>
                <text class="item-arrow"><uni-icons type="right" size="16" color="#d2d2d2"></uni-icons></text>
            </view>
        </view>
        <!-- 我的联系方式 -->
        <view class="settings-item" style="margin-top: 20rpx;" @click="contactService"> 
            <view class="item-left">
                <text class="item-label">我的联系方式</text>
                <!-- <view class="red-dot"></view> -->
            </view>
            <text class="item-arrow"><uni-icons type="right" size="16" color="#d2d2d2"></uni-icons></text>
        </view>
         <!-- 我的联系方式 -->
        <view class="settings-item" style="margin-top: 20rpx;" @click="cancell">
            <view class="item-left">
                <text class="item-label">注销账号</text>
                <!-- <view class="red-dot"></view> -->
            </view>
            <text class="item-arrow"><uni-icons type="right" size="16" color="#d2d2d2"></uni-icons></text>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            show: false,
            title: '标题',
            content: '确定退出登录吗'
        }
    },
    methods: {
        chanpassword() {
            uni.navigateTo({
                url: './modify'
            });
        },
        contactService(){
            uni.navigateTo({ url: './information' });
        },
        cancell(){
            uni.navigateTo({
                url: './cancellation'
            });

        }
    }
}
</script>

<style>
::v-deep .u-tabbar__content__item-wrapper {
    margin-bottom: 60rpx;
}

.header {
    height: 88rpx;
    /* display: flex;
    justify-content: space-between;
    align-items: center; */
    background-color: #fff;

    .navbar {
        height: 100%;
    }
}

.settings-container {
    min-height: 100vh;
    background-color: #f9f8fd;
}

/* 导航栏样式 */
.nav-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 88rpx 32rpx 20rpx;
    background-color: #fff;
}

.nav-left {
    width: 80rpx;
}

.back-icon {
    font-size: 36rpx;
    color: #333;
}

.nav-title {
    font-size: 34rpx;
    font-weight: 500;
    flex: 1;
    text-align: center;
}

.nav-right {
    width: 80rpx;
}

/* 设置列表样式 */
.settings-list {
    margin-top: 20rpx;
    background-color: #fff;
}

.settings-listlate {
    margin-top: 88rpx;
    background-color: #fff;
}

.settings-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    background: #fff;
    border-bottom: 1rpx solid #f5f5f5;
}

.item-left {
    display: flex;
    align-items: center;
}

.item-label {
    font-size: 30rpx;
    color: #333;
    font-weight: 400;
}

.red-dot {
    width: 12rpx;
    height: 12rpx;
    background-color: #ff4d4f;
    border-radius: 50%;
    margin-left: 8rpx;
}

.item-right {
    display: flex;
    align-items: center;
}

.status-text {
    font-size: 28rpx;
    color: #999;
    margin-right: 8rpx;
}

.cache-size {
    font-size: 28rpx;
    color: #999;
    margin-right: 8rpx;
}

.item-arrow {
    color: #ccc;
    font-size: 32rpx;
}

/* 退出登录按钮样式 */
.logout-btn {
    margin: 40rpx 0rpx;
    background-color: #fff;
    color: #576b95;
    text-align: center;
    padding: 24rpx;
    border-radius: 8rpx;
    font-size: 32rpx;
}
</style>