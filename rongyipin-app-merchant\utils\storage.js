/**
 * 本地存储工具类
 */

// 默认缓存时间（7天）
const DEFAULT_CACHE_TIME = 60 * 60 * 24 * 7

/**
 * 设置本地存储
 * @param {String} key 键名
 * @param {*} value 值
 * @param {Number} expire 过期时间，单位秒，默认7天
 */
export const setStorage = (key, value, expire = DEFAULT_CACHE_TIME) => {
  if (value === undefined) {
    return
  }
  const data = {
    value,
    expire: expire !== null ? new Date().getTime() + expire * 1000 : null,
  }
  try {
    uni.setStorageSync(key, JSON.stringify(data))
  } catch (e) {
    console.error("uni is not defined. This might be running in an environment without uni-app support.")
  }
}

/**
 * 获取本地存储
 * @param {String} key 键名
 * @param {*} def 默认值
 */
export const getStorage = (key, def = null) => {
  try {
    const data = uni.getStorageSync(key)
    if (!data) {
      return def
    }
    try {
      const item = JSON.parse(data)
      // 判断是否过期
      if (item.expire && item.expire < new Date().getTime()) {
        removeStorage(key)
        return def
      }
      return item.value
    } catch (e) {
      return def
    }
  } catch (e) {
    console.error("uni is not defined. This might be running in an environment without uni-app support.")
    return def
  }
}

/**
 * 移除本地存储
 * @param {String} key 键名
 */
export const removeStorage = (key) => {
  try {
    uni.removeStorageSync(key)
  } catch (e) {
    console.error("uni is not defined. This might be running in an environment without uni-app support.")
  }
}

/**
 * 清空本地存储
 */
export const clearStorage = () => {
  try {
    uni.clearStorageSync()
  } catch (e) {
    console.error("uni is not defined. This might be running in an environment without uni-app support.")
  }
}

/**
 * 获取本地存储信息
 */
export const getStorageInfo = () => {
  try {
    return uni.getStorageInfoSync()
  } catch (e) {
    console.error("uni is not defined. This might be running in an environment without uni-app support.")
    return {}
  }
}

export default {
  setStorage,
  getStorage,
  removeStorage,
  clearStorage,
  getStorageInfo,
}
