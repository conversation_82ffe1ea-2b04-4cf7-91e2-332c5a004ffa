<template>
    <view class="regis-container">
        <!-- 顶部导航 -->
        <view class="regis-tab">
            <view class="tab-container">
                <view v-for="(tab, index) in tabs" :key="index" class="tab-item"
                    :class="{ active: activeTab === index }" @click="switchTab(index)">
                    <text>{{ tab.name }}</text>
                </view>
            </view>
            <view class="customer">客服</view>
        </view>
        <view class="regis-content" v-if="activeTab === 0">
            <!-- 职位选择器 -->
            <view class="regis0-title" @click="show = true">
                <text class="title-text">{{ jobTitle.name || '全部职位' }}</text>
                <u-icon name="arrow-down-fill" size="24" color="#666" style="margin-left: 8rpx;"></u-icon>
            </view>

            <!-- 筛选标签 -->
            <view class="regis0-tablate">
                <view class="regis0-tabitem" :class="{ laye: activelaye === item.value }"
                    v-for="(item, index) in tablaye" :key="index" @click="layeTab(item.value)">
                    {{ item.lable }}
                </view>
            </view>

            <!-- 推广提示卡片 -->
            <view class="regis0-machine">
                <view class="machine-content">
                    <view class="machine-icon">
                        <image src="@/static/app/position/machine.png" alt=""/>
                    </view>
                    <view class="machine-text">
                        <text>使用刷新道具加速招聘，收到报名更快</text>
                    </view>
                    <view class="machine-close" @click="closeMachine">
                        <u-icon name="close" color="#fff" size="24"></u-icon>
                    </view>
                </view>
            </view>
            <!-- 候选人列表 -->
            <scroll-view class="regis0-list-scroll" v-if="getList.length > 0" scroll-y="true" :refresher-enabled="true"
                :refresher-triggered="refreshing" @refresherrefresh="onRefresh" @scrolltolower="onLoadMore"
                :lower-threshold="100" :style="{ height: scrollViewHeight + 'px' }" @click="closeAllPopups">
                <view class="regis0-list">
                    <view v-for="(item, index) in getList" :key="index" class="regis0-list-box" @click.stop>
                        <!-- 状态标签 -->
                        <view class="regis0-list-top">
                            <view class="status-tag status-urgent">
                                <view class="status-dot"></view>
                                <text>已报名3天，建议立即联系</text>
                            </view>
                            <view class="status-tag status-high">
                                <view class="trend-icon">📈</view>
                                <text>入职意愿高</text>
                            </view>
                        </view>

                        <!-- 用户信息 -->
                        <view class="regis0-list-mid" @click="gotodetail(item)">
                            <view class="user-info">
                                <view class="mid-img">
                                    <image :src="item.avatarUrl" alt="" class="avatar-img"/>
                                </view>
                                <view class="mid-name">
                                    <text class="username">{{ item.username }}</text>
                                    <text class="apply-time">{{ formatTimestamp(item.apply_time) }} 报名</text>
                                </view>
                            </view>
                            <view class="arrow-icon">
                                <uni-icons type="right" size="20" color="#ccc"></uni-icons>
                            </view>
                        </view>

                        <!-- 用户标签 -->
                        <view class="regis0-tag">
                            <view class="tag-item">{{ item.sex_name }}</view>
                            <view class="tag-item">{{ item.age }}岁</view>
                            <view class="tag-item">专科</view>
                            <view class="tag-item">健康证</view>
                        </view>

                        <!-- 操作按钮 -->
                        <view class="regis0-click">
                            <view class="mark-button" @click.stop="toggleMarkPopup(item)"
                                style="display: flex;align-items: center;">
                                标记<u-icon style="margin-top: 10rpx;"
                                    :name="item.showPopup ? 'arrow-up-fill' : 'arrow-down-fill'"></u-icon>

                                <!-- 标记弹窗 -->
                                <view v-if="item.showPopup" class="mark-popup-container" @click.stop>
                                    <view class="mark-popup">
                                        <view class="popup-title">标记状态</view>
                                        <view class="popup-options">
                                            <view class="popup-option" @click="selectMarkStatus('contacted_suitable')">
                                                <view class="option-radio">
                                                    <view class="radio-dot"
                                                        :class="{ 'checked': markStatus === 'contacted_suitable' }">
                                                    </view>
                                                </view>
                                                <text class="option-text">已联系-合适</text>
                                            </view>
                                            <view class="popup-option"
                                                @click="selectMarkStatus('contacted_not_suitable')">
                                                <view class="option-radio">
                                                    <view class="radio-dot"
                                                        :class="{ 'checked': markStatus === 'contacted_not_suitable' }">
                                                    </view>
                                                </view>
                                                <text class="option-text">已联系-不合适</text>
                                            </view>
                                            <view class="popup-option" @click="selectMarkStatus('not_contacted')">
                                                <view class="option-radio">
                                                    <view class="radio-dot"
                                                        :class="{ 'checked': markStatus === 'not_contacted' }"></view>
                                                </view>
                                                <text class="option-text">未联系</text>
                                            </view>
                                            <view class="popup-option" @click="selectMarkStatus('pending_reply')">
                                                <view class="option-radio">
                                                    <view class="radio-dot"
                                                        :class="{ 'checked': markStatus === 'pending_reply' }"></view>
                                                </view>
                                                <text class="option-text">已联系</text>
                                            </view>
                                        </view>
                                        <!-- 小三角 -->
                                        <view class="popup-arrow"></view>
                                    </view>
                                </view>
                            </view>
                            <view class="action-buttons">
                                <u-button class="action-btn call-btn" text="打电话"></u-button>
                                <u-button class="action-btn message-btn" text="发消息" @click="messages(item)"></u-button>
                                <u-button class="action-btn wechat-btn" text="加微信"></u-button>
                            </view>
                        </view>
                    </view>

                    <!-- 加载状态提示 -->
                    <view class="loading-status">
                        <view v-if="loading && getList.length > 0" class="loading-more">
                            <u-loading-icon mode="spinner" color="#02bdc4" size="32"></u-loading-icon>
                            <text class="loading-text">加载中...</text>
                        </view>
                        <view v-else-if="!hasMore && getList.length > 0" class="no-more">
                            <text>- 没有更多了 -</text>
                        </view>
                    </view>
                </view>
            </scroll-view>
            <!-- 空状态 -->
            <view class="regis0-list" v-else>
                <view class="empty-state">
                    <view class="empty-icon">
                        <image src="@/static/app/home/<USER>" alt="" class="empty-img"/>
                    </view>
                    <text class="empty-text">暂无报名候选人</text>
                </view>

                <!-- 推荐卡片 -->
                <view class="regis-released" v-if="getList.length == 0">
                    <view class="released-header">
                        <image src="@/static/app/home/<USER>" alt="" class="released-bg"/>
                    </view>
                    <view class="released-content">
                        <text class="released-text">
                            <text class="highlight-number">？？？位</text>求职者，正在您的附近找工作
                        </text>
                        <u-button class="released-btn" @click="released" text="去看看"></u-button>
                    </view>
                </view>
            </view>
        </view>
        <view class="regis1-content" v-else-if="activeTab === 1">
            <nearbyJob :nearList="nearList" :Position="Position" :job_id="job_id"></nearbyJob>
        </view>
        <view class="regis-content" v-else-if="activeTab === 2">
            <seenMy :seenList="seenList" :looklate="looklate"></seenMy>
        </view>



        <u-popup :show="show" @close="close" @open="open" :round="20" mode="bottom">
            <view class="popup-content">
                <view class="popup-top">
                    <h2>切换职位</h2>
                    <uni-icons @click="show = false" type="closeempty" size="22"></uni-icons>
                </view>
                <view style="color: #afafb3;margin-top: 20rpx;">
                    已发布 {{ jobList.length || '0' }} 个职位，切换查看候选人简历
                </view>
                <view class="popup-taball">
                    <view class="taball">
                        <view>
                            <view style="font-size: 40rpx;color: black;font-weight: 600;">全部职位</view>
                            <view style="color: #9a9ca4;margin-top: 10rpx; display: flex;align-items: center;">
                                待处理候选人 {{ 0 }}
                            </view>
                        </view>
                        <view>
                            <u-button @click="selectJob(null)" class="code-btn" text="当前"
                                :class="{ activeJob: selectedJobId === null }"></u-button>
                        </view>


                    </view>
                </view>
                <view class="popup-botom" @click="show = true">
                    全部职位
                </view>
                <scroll-view class="popup-joblist-scroll" v-if="!loding" scroll-y="true"
                    @scrolltolower="onJobListLoadMore" :lower-threshold="100" style="height: 400rpx;">
                    <view class="popup-joblist">
                        <view class="taball" v-for="(item, index) in jobList" :key="index">
                            <view>
                                <view style="font-size: 40rpx;color: black;font-weight: 600;">{{ item.name }}</view>
                                <view style="color: #9a9ca4;margin-top: 10rpx; display: flex;align-items: center;">
                                    <view style="padding: 3rpx 10rpx;margin-right: 10rpx;border: 1px solid #f5f5f5;">{{
                                        item.merge_status == 0 ? '已暂停' : item.merge_status == 1 ? '招聘中' :
                                            item.merge_status
                                                == 2
                                                ? '待审核' : '审核驳回' }}</view> 待处理候选人 {{ item.sign_num }}
                                </view>
                            </view>
                            <view>
                                <u-button @click="selectJob(item)" class="code-btn" text="当前"
                                    :class="{ activeJob: selectedJobId === item.id }"></u-button>
                            </view>
                        </view>

                        <!-- 职位列表加载状态提示 -->
                        <view class="job-loading-status">
                            <view v-if="jobListLoading && jobList.length > 0" class="loading-more">
                                <u-loading-icon mode="spinner" color="#02bdc4" size="32"></u-loading-icon>
                                <text class="loading-text">加载中...</text>
                            </view>
                            <view v-else-if="!jobListHasMore && jobList.length > 0" class="no-more">
                                <text>- 没有更多职位了 -</text>
                            </view>
                        </view>
                    </view>
                </scroll-view>
                <view style="text-align: center;" v-else>
                    <u-loading-icon></u-loading-icon>
                    <view style="color: #9a9ca4;">加载中...</view>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
import { applyApi, positionsApi } from "@/utils/api"
import nearbyJob from "./nearbyJob.vue"
import seenMy from "./seenMy.vue"
export default {
    components: {
        nearbyJob,
        seenMy
    },
    props: {
        jobApplyData: {
            type: [Array, Object], // 允许接收数组或对象
            default: () => [] // 默认值为空数组
        },
        total: {
            type: Number,
            default: 0
        },
        activeTabs: {
            type: [Array, Object], // 允许接收数组或对象
            default: () => [] // 默认值为空数组
        },
    },
    watch: {
        jobApplyData: {
            immediate: true, // 初始化时立即执行
            handler(newVal) {
                this.getList = newVal.map(item => ({
                    ...item,
                    showPopup: false // 初始化弹窗状态为关闭
                }));
                console.log(this.getList, '^^^')
                this.getListlegth = newVal

                this.jobTitle = {}
                this.selectedJobId = null
                setTimeout(function () {
                    uni.hideLoading();
                }, 500);
            }
        },
        total: {
            immediate: true, // 初始化时立即执行
            handler(newVal) {
                this.currentTotal = newVal
            }
        },
        activeTabs: {
            immediate: true, // 初始化时立即执行
            handler(newVal) {
                console.log(newVal, '%%%')
                // 判断是否有有效的active属性
                if (newVal && newVal.active !== undefined && newVal.active !== null && newVal.active !== '') {
                    this.activeTab = newVal.active
                } else {
                    this.activeTab = 0
                }
            }
        },
    },
    data() {
        return {
            show: false,
            activeTab: 0, // 默认选中第一个 tab
            tabs: [{
                name: '已报名'
            }, {
                name: '附近求职者'
            }, {
                name: '看过我'
            }],
            page: 1,
            size: 10,
            getList: [],
            getListlegth: [],
            tablaye: [
                {
                    lable: '全部',
                    value: 100
                },
                {
                    lable: '未联系',
                    value: 0
                },
                {
                    lable: '已联系',
                    value: 1
                },
                {
                    lable: '合适',
                    value: 2
                },
                {
                    lable: '不合适',
                    value: 3
                },
            ],
            activelaye: 100,
            jobList: [],
            totallate: 0,
            selectedJobId: null,
            fullPartId: 1,
            jobTitle: {},
            nearList: [],
            loding: false,
            job: '',
            seenList: [],
            Position: [],
            // 下拉刷新和上拉加载相关
            loading: false, // 是否正在加载
            refreshing: false, // 是否正在刷新
            hasMore: true, // 是否有更多数据
            currentTotal: 0, // 当前总数据量（避免与prop冲突）
            scrollViewHeight: 400, // scroll-view高度
            // 职位列表分页相关
            pagelate: 1,
            pagesize: 10,
            totallate: 0,
            jobListLoading: false, // 职位列表是否正在加载
            jobListHasMore: true, // 职位列表是否还有更多数据
            looklate: {},
            job_id: null,
            showMarkPopup: false,
        };
    },
    onShow() {
        console.log('****')
    },
    mounted() {
        console.log('接收到的数据:', this.jobApplyData);
        this.calculateScrollViewHeight();
    },
    onReady() {
        this.calculateScrollViewHeight();
    },
    methods: {
        messages(item){
            let user = {
                ...item,
                user_id:item.id
            }
            console.log(user)
            const userInfo = encodeURIComponent(JSON.stringify(user))
			uni.navigateTo({
				url: `/pages/chat/chat?userInfo=${userInfo}`
			})
        },
        toggleMarkPopup(item) {
            console.log('点击标记按钮，当前状态:', item.showPopup);

            // 如果当前项已经显示弹窗，则关闭它
            if (item.showPopup) {
                item.showPopup = false;
                console.log('关闭当前弹窗');
                return;
            }

            // 关闭所有其他项的弹窗
            this.getList.forEach(listItem => {
                if (listItem !== item) {
                    listItem.showPopup = false;
                }
            });

            // 显示当前项的弹窗
            item.showPopup = true;

            // 设置默认选中状态
            const statusMap = {
                0: 'not_contacted',
                1: 'pending_reply',
                2: 'contacted_suitable',
                3: 'contacted_not_suitable'
            };

            this.markStatus = statusMap[item.apply_status] || 'not_contacted'; // 设置默认选中状态
            this.optionitem = item; // 保存当前选中的列表项

            console.log('切换后状态:', item.showPopup);
        },
        hideMarkPopup() {
            this.showMarkPopup = false;
        },
        // 关闭所有弹窗
        closeAllPopups() {
            this.getList.forEach(listItem => {
                listItem.showPopup = false;
            });
        },
        async selectMarkStatus(status) {
            this.markStatus = status;

            this.markStatus = status;

            // 根据选择的状态打印不同的信息
            const statusMap = {
                'contacted_suitable': { text: '已联系-合适', id: 2 },
                'contacted_not_suitable': { text: '已联系-不合适', id: 3 },
                'not_contacted': { text: '未联系', id: 0 },
                'pending_reply': { text: '已联系', id: 1 }
            };

            const selectedStatus = statusMap[status];

            // 获取当前数据的ID和选中的状态ID
            const currentDataId = this.optionitem.id;  // 当前数据的ID
            const selectedStatusId = selectedStatus.id;       // 选中的状态ID

            console.log('=== selectMarkStatus 数据信息 ===');
            console.log('当前数据ID (seeker_id):', currentDataId);
            console.log('选中的状态ID:', selectedStatusId);
            console.log('选中的状态文本:', selectedStatus.text);
            console.log('完整的当前数据对象:', this.optionitem);
            console.log('================================');

            let res = await applyApi.changeApply({ seeker_id: currentDataId, status: selectedStatusId })
            if (res.code == 200) {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });

                // 刷新列表数据
                await this.refreshListData();
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }

            // 关闭弹窗
            this.optionitem.showPopup = false;
        },

        gotodetail(item) {
            // uni.navigateTo({
            //     url: `./signeddetail/index?item=${encodeURIComponent(JSON.stringify(item))}`
            // });
            console.log(this.jobTitle, '%%%')
            if (this.jobTitle == '全部职位' || this.jobTitle.name == undefined) {
                uni.navigateTo({
                    url: `./signeddetail/index?seeker_id=${item.id}`
                });
            } else[
                uni.navigateTo({
                    url: `./signeddetail/index?seeker_id=${item.id}&job_id=${this.jobTitle.id}`
                })
            ]
        },
        released() {
            this.activeTab = 1
        },
        async selectJob(job) {
            this.page = 1;
            this.hasMore = true; // 重置hasMore状态
            this.activelaye = 100
            if (job == null) {
                this.selectedJobId = job
                this.job = job
                this.jobTitle = { name: '全部职位' }
                let reslate = await applyApi.getJobApply({
                    page: this.page,
                    size: this.size
                })

                if (reslate.code === 200) {
                    this.getList = (reslate.data.data || []).map(item => ({
                        ...item,
                        showPopup: false
                    }));
                    this.getListlegth = reslate.data.data || [];
                    this.currentTotal = reslate.data.total || 0; // 更新总数

                    // 重新判断是否还有更多数据
                    this.hasMore = this.getList.length >= this.size && this.getList.length < this.currentTotal;

                    console.log(`切换职位完成: 当前${this.getList.length}条，总共${this.currentTotal}条，还有更多: ${this.hasMore}`);
                }

                setTimeout(function () {
                    uni.hideLoading();
                }, 500);
            } else {
                this.selectedJobId = job.id; // 更新选中的职位 ID
                this.jobTitle = job
                let reslate = await applyApi.getJobApply({
                    page: this.page,
                    job_id: job.id,
                    size: this.size
                })

                if (reslate.code === 200) {
                    this.getList = (reslate.data.data || []).map(item => ({
                        ...item,
                        showPopup: false
                    }));
                    this.getListlegth = reslate.data.data || [];
                    this.currentTotal = reslate.data.total || 0; // 更新总数

                    // 重新判断是否还有更多数据
                    this.hasMore = this.getList.length >= this.size && this.getList.length < this.currentTotal;

                    console.log(`切换职位完成: 当前${this.getList.length}条，总共${this.currentTotal}条，还有更多: ${this.hasMore}`);
                }

                setTimeout(function () {
                    uni.hideLoading();
                }, 500);
            }
            this.show = false
        },
        formatTimestamp(timestamp) {
            const date = new Date(timestamp * 1000); // 将秒转换为毫秒
            const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需要加 1
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            return `${month}/${day} ${hours}:${minutes}`; // 返回格式化的字符串
        },
        async layeTab(index) {
            this.activelaye = index; // 更新选中的 tab
            this.page = 1;
            this.hasMore = true; // 重置hasMore状态

            console.log('当前jobTitle:', this.jobTitle);

            // 判断是否为空对象或全部职位
            if (!this.jobTitle.name || this.jobTitle.name === '全部职位' || !this.jobTitle.id) {
                console.log('执行全部职位的逻辑');
                let reslate = await applyApi.getJobApply({
                    page: this.page,
                    size: this.size,
                    status: this.activelaye
                })

                if (reslate.code === 200) {
                    this.getList = (reslate.data.data || []).map(item => ({
                        ...item,
                        showPopup: false
                    }));
                    this.getListlegth = reslate.data.data || [];
                    this.currentTotal = reslate.data.total || 0; // 更新总数

                    // 重新判断是否还有更多数据
                    this.hasMore = this.getList.length >= this.size && this.getList.length < this.currentTotal;

                    console.log(`筛选完成: 当前${this.getList.length}条，总共${this.currentTotal}条，还有更多: ${this.hasMore}`);
                }

                setTimeout(function () {
                    uni.hideLoading();
                }, 500);
            } else {
                console.log('执行特定职位的逻辑');
                let reslate = await applyApi.getJobApply({
                    page: this.page,
                    size: this.size,
                    job_id: this.jobTitle.id,
                    status: this.activelaye
                })

                if (reslate.code === 200) {
                    this.getList = (reslate.data.data || []).map(item => ({
                        ...item,
                        showPopup: false
                    }));
                    this.getListlegth = reslate.data.data || [];
                    this.currentTotal = reslate.data.total || 0; // 更新总数

                    // 重新判断是否还有更多数据
                    this.hasMore = this.getList.length >= this.size && this.getList.length < this.currentTotal;

                    console.log(`筛选完成: 当前${this.getList.length}条，总共${this.currentTotal}条，还有更多: ${this.hasMore}`);
                }

                setTimeout(function () {
                    uni.hideLoading();
                }, 500);
            }
        },
        async switchTab(index) {
            console.log(index)
            this.activeTab = index; // 更新选中的 tab
            this.activelaye = 100
            this.page = 1
            this.hasMore = true; // 重置hasMore状态

            if (this.activeTab === 0) {
                let res = await applyApi.getJobApply({
                    page: this.page,
                    size: this.size
                })

                if (res.code === 200) {
                    this.getList = (res.data.data || []).map(item => ({
                        ...item,
                        showPopup: false
                    }));
                    this.getListlegth = res.data.data || [];
                    this.currentTotal = res.data.total || 0; // 更新总数

                    // 重新判断是否还有更多数据
                    this.hasMore = this.getList.length >= this.size && this.getList.length < this.currentTotal;

                    console.log(`切换Tab完成: 当前${this.getList.length}条，总共${this.currentTotal}条，还有更多: ${this.hasMore}`);
                }
                this.looklate = {}
                uni.hideLoading()
            } else if (this.activeTab === 1) {
                const params = {
                    page: this.page,
                    size: this.size,
                    status: 1,
                }
                let ress = await positionsApi.postJobList(params)
                console.log(ress.data.data)
                if (ress.code == 200) {
                    if (ress.data.data.length > 0) {
                        this.job_id = ress.data.data[0].id
                        const param = {
                            page: this.page,
                            size: this.size,
                            job_id: ress.data.data[0].id
                        }

                        let res = await applyApi.getRecommend(param)
                        this.nearList = res.data.data
                        this.Position = ress.data.data
                        this.looklate = {}
                    }
                } else {
                    uni.showToast({
                        title: res.msg,
                        icon: 'none'
                    });
                }
                uni.hideLoading()
            } else if (this.activeTab === 2) {
                if (this.looklate.listid != '') {
                    const params = {
                        page: this.page,
                        size: this.size,
                        job_id: this.looklate.listid
                    }
                    let res = await applyApi.getJobView(params)
                    if (res.code == 200) {
                        this.seenList = res.data.data
                    } else {
                        uni.showToast({
                            title: res.msg,
                            icon: 'none'
                        });
                    }
                    uni.hideLoading()
                } else {
                    const params = {
                        page: this.page,
                        size: this.size
                    }
                    let res = await applyApi.getJobView(params)
                    if (res.code == 200) {
                        this.seenList = res.data.data
                    } else {
                        uni.showToast({
                            title: res.msg,
                            icon: 'none'
                        });
                    }
                    uni.hideLoading()
                }

            }
        },
        close() {
            this.show = false
        },
        async open() {
            this.loding = true
            // 重置职位列表分页状态
            this.pagelate = 1
            this.jobListHasMore = true

            const params = {
                page: this.pagelate,
                size: this.pagesize,
                status: 4,
            }
            let res = await positionsApi.postJobList(params)
            this.jobList = res.data.data || []
            this.totallate = res.data.total || 0

            // 判断是否还有更多职位数据
            this.jobListHasMore = this.jobList.length >= this.pagesize && this.jobList.length < this.totallate

            this.loding = false
            uni.hideLoading()
        },
        // 计算scroll-view高度
        calculateScrollViewHeight() {
            const systemInfo = uni.getSystemInfoSync();
            const windowHeight = systemInfo.windowHeight;
            // 减去顶部tab、职位选择器、筛选标签、推广卡片等高度
            this.scrollViewHeight = windowHeight - 400; // 根据实际情况调整
        },

        // 下拉刷新
        async onRefresh() {
            console.log('下拉刷新');
            this.refreshing = true;
            this.page = 1;
            this.hasMore = true;

            try {
                await this.loadDataForRefresh(); // 刷新数据
                uni.showToast({
                    title: '刷新成功',
                    icon: 'success',
                    duration: 1500
                });
            } catch (error) {
                console.error('刷新失败:', error);
                uni.showToast({
                    title: '刷新失败',
                    icon: 'none',
                    duration: 1500
                });
            } finally {
                this.refreshing = false;
            }
        },

        // 上拉加载更多
        async onLoadMore() {
            console.log('上拉加载更多');
            if (this.loading || !this.hasMore) {
                console.log('正在加载或没有更多数据');
                return;
            }

            this.loading = true;
            this.page++;

            try {
                await this.loadDataForLoadMore(); // 加载更多数据
            } catch (error) {
                console.error('加载更多失败:', error);
                this.page--; // 加载失败时回退页码
                uni.showToast({
                    title: '加载失败',
                    icon: 'none',
                    duration: 1500
                });
            } finally {
                this.loading = false;
            }
        },

        // 刷新时的数据加载
        async loadDataForRefresh() {
            let params = {
                page: this.page,
                size: this.size
            };

            // 根据当前状态添加参数
            if (this.activelaye !== 100) {
                params.status = this.activelaye;
            }

            // 判断是否为特定职位
            if (this.jobTitle && this.jobTitle.name && this.jobTitle.name !== '全部职位' && this.jobTitle.id) {
                params.job_id = this.jobTitle.id;
            }

            console.log('刷新请求参数:', params);
            let res = await applyApi.getJobApply(params);

            if (res.code === 200) {
                const newList = res.data.data || [];
                this.currentTotal = res.data.total || 0;

                // 刷新时替换数据
                this.getList = newList.map(item => ({
                    ...item,
                    showPopup: false
                }));
                this.getListlegth = newList;

                // 判断是否还有更多数据
                this.hasMore = newList.length >= this.size && this.getList.length < this.currentTotal;

                console.log(`刷新完成: 当前${this.getList.length}条，总共${this.currentTotal}条，还有更多: ${this.hasMore}`);
            } else {
                throw new Error(res.msg || '获取数据失败');
            }
        },

        // 加载更多时的数据加载
        async loadDataForLoadMore() {
            let params = {
                page: this.page,
                size: this.size
            };

            // 根据当前状态添加参数
            if (this.activelaye !== 100) {
                params.status = this.activelaye;
            }

            // 判断是否为特定职位
            if (this.jobTitle && this.jobTitle.name && this.jobTitle.name !== '全部职位' && this.jobTitle.id) {
                params.job_id = this.jobTitle.id;
            }

            console.log('加载更多请求参数:', params);
            let res = await applyApi.getJobApply(params);

            if (res.code === 200) {
                const newList = res.data.data || [];
                this.currentTotal = res.data.total || 0;

                // 加载更多时追加数据
                const newListWithPopup = newList.map(item => ({
                    ...item,
                    showPopup: false
                }));
                this.getList = [...this.getList, ...newListWithPopup];
                this.getListlegth = [...this.getListlegth, ...newList];

                // 判断是否还有更多数据
                this.hasMore = newList.length >= this.size && this.getList.length < this.currentTotal;

                console.log(`加载更多完成: 当前${this.getList.length}条，总共${this.currentTotal}条，还有更多: ${this.hasMore}`);
                uni.hideLoading()
            } else {
                throw new Error(res.msg || '获取数据失败');
                uni.hideLoading()
            }
        },

        // 职位列表上拉加载更多
        async onJobListLoadMore() {
            console.log('职位列表上拉加载更多');
            if (this.jobListLoading || !this.jobListHasMore) {
                console.log('职位列表正在加载或没有更多数据');
                return;
            }

            this.jobListLoading = true;
            this.pagelate++;

            try {
                const params = {
                    page: this.pagelate,
                    size: this.pagesize,
                    status: 4,
                }

                console.log('职位列表加载更多请求参数:', params);
                let res = await positionsApi.postJobList(params);

                if (res.code === 200) {
                    const newJobList = res.data.data || [];
                    this.totallate = res.data.total || 0;

                    // 追加新的职位数据
                    this.jobList = [...this.jobList, ...newJobList];

                    // 判断是否还有更多数据
                    this.jobListHasMore = newJobList.length >= this.pagesize && this.jobList.length < this.totallate;

                    console.log(`职位列表加载更多完成: 当前${this.jobList.length}条，总共${this.totallate}条，还有更多: ${this.jobListHasMore}`);
                } else {
                    throw new Error(res.msg || '获取职位数据失败');
                }
            } catch (error) {
                console.error('职位列表加载更多失败:', error);
                this.pagelate--; // 加载失败时回退页码
                uni.showToast({
                    title: '加载失败',
                    icon: 'none',
                    duration: 1500
                });
            } finally {
                this.jobListLoading = false;
            }
        },

        // 根据job_id查找并选中对应的职位
        findAndSelectJob(jobId) {
            console.log('查找职位ID:', jobId, '在jobList中:', this.jobList)

            if (!this.jobList || this.jobList.length === 0 || !jobId) {
                console.log('jobList为空或jobId无效')
                return
            }

            // 在jobList中查找对应的职位索引
            const targetIndex = this.jobList.findIndex(item => item.id == jobId)

            if (targetIndex !== -1) {
                this.selectedJobId = targetIndex // 设置高亮的索引
                console.log(`找到职位: 索引${targetIndex}, 职位名称: ${this.jobList[targetIndex].name}`)

                // 可选：自动选择该职位
                // this.selectJob(this.jobList[targetIndex])
            } else {
                console.log(`未找到ID为${jobId}的职位`)
                this.selectedJobId = null
            }
        },

        closeMachine() {
            // 关闭推广卡片的逻辑
            console.log('关闭推广卡片');
            // 这里可以添加隐藏卡片的逻辑
        },

        // 刷新列表数据的方法
        async refreshListData() {
            // 重置分页参数
            this.page = 1;
            this.hasMore = true;

            try {
                let params = {
                    page: this.page,
                    size: this.size
                };

                // 根据当前状态添加参数
                if (this.activelaye !== 100) {
                    params.status = this.activelaye;
                }

                // 判断是否为特定职位
                if (this.jobTitle && this.jobTitle.name && this.jobTitle.name !== '全部职位' && this.jobTitle.id) {
                    params.job_id = this.jobTitle.id;
                }

                console.log('刷新列表请求参数:', params);
                let res = await applyApi.getJobApply(params);

                if (res.code === 200) {
                    const newList = res.data.data || [];
                    this.currentTotal = res.data.total || 0;

                    // 刷新时替换数据并添加showPopup属性
                    this.getList = newList.map(item => ({
                        ...item,
                        showPopup: false
                    }));
                    this.getListlegth = newList;

                    // 判断是否还有更多数据
                    this.hasMore = newList.length >= this.size && this.getList.length < this.currentTotal;

                    console.log(`列表刷新完成: 当前${this.getList.length}条，总共${this.currentTotal}条，还有更多: ${this.hasMore}`);
                } else {
                    console.error('刷新列表失败:', res.msg);
                    uni.showToast({
                        title: '刷新失败',
                        icon: 'none'
                    });
                }
                uni.hideLoading()
            } catch (error) {
                console.error('刷新列表异常:', error);
                uni.showToast({
                    title: '刷新异常',
                    icon: 'none'
                });
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.regis-container {
    height: calc(100vh - 100rpx);
}

.regis-tab {
    display: flex;
    background-color: #02bdc4;
    height: 160rpx;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 30rpx 0 30rpx;
}

.tab-container {
    // width: 50%;
    display: flex;
    justify-content: space-around;
    // align-items: center;
    padding: 20rpx 0;
    border-radius: 10rpx;
}

.tab-item {
    // flex: 1;
    text-align: center;
    height: 70rpx;
    padding: 20rpx 0;
    font-size: 40rpx;
    padding: 10rpx;
    color: #71d9e6;
    border-bottom: 4rpx solid transparent;
    transition: all 0.3s ease;
}

.tab-item.active {
    color: white;
    border-radius: 10rpx 10rpx 0 0;
    border-bottom: 6rpx solid transparent;
    border-image: linear-gradient(to right,
            transparent 30%,
            #fff 30%,
            #fff 70%,
            transparent 70%);
    border-image-slice: 1;
}

.customer {
    color: white;
}

.regis-content {
    height: calc(100% - 168rpx);
    margin-top: -20rpx;
    border-radius: 20rpx 20rpx 0 0;
    background-color: #f0f1f6;
    padding: 20rpx;

    .regis0-title {
        width: 100%;
        display: flex;
        height: 80rpx;
        align-items: center;
        padding: 10rpx 20rpx;
        cursor: pointer;

        .title-text {
            font-size: 32rpx;
            font-weight: 600;
            color: #333;
        }
    }

    .regis0-tablate {
        height: 80rpx;
        // background-color: white;
        padding: 10rpx 20rpx;
        display: flex;
        justify-content: center;
        align-items: center;

        .regis0-tabitem {
            width: 20%;
            text-align: center;
            color: #5f5f72;
            border-radius: 20rpx;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .laye {
            background: white;
            color: black;
        }
    }

    .regis0-machine {
        width: 100%;
        background: linear-gradient(135deg, #00d4aa 0%, #00bcd4 100%);
        border-radius: 16rpx;
        margin-top: 20rpx;
        position: relative;
        overflow: hidden;

        .machine-content {
            display: flex;
            align-items: center;
            padding: 20rpx 30rpx;
            position: relative;
        }

        .machine-icon {
            width: 60rpx;
            height: 60rpx;
            margin-right: 20rpx;

            image {
                width: 100%;
                height: 100%;
            }
        }

        .machine-text {
            flex: 1;

            text {
                color: white;
                font-size: 28rpx;
                font-weight: 500;
            }
        }

        .machine-close {
            position: absolute;
            right: 0;
            top: 0;
            width: 60rpx;
            height: 50rpx;
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 0 16rpx 0 16rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
    }

    // 滚动容器样式
    .regis0-list-scroll {
        width: 100%;
        height: calc(100% - 360rpx) !important;
        margin-top: 10rpx;

    }

    .regis0-list {

        overflow: hidden;
        overflow-y: auto;
        // background-color: white;

        .regis0-list-box {
            background-color: white;
            border-radius: 20rpx;
            margin-top: 20rpx;
            padding: 20rpx;
            box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);

            .regis0-list-top {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 20rpx;

                .status-tag {
                    display: flex;
                    align-items: center;
                    padding: 8rpx 16rpx;
                    border-radius: 20rpx;
                    font-size: 24rpx;

                    &.status-urgent {
                        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
                        color: #1976d2;

                        .status-dot {
                            width: 12rpx;
                            height: 12rpx;
                            background-color: #1976d2;
                            border-radius: 50%;
                            margin-right: 8rpx;
                        }
                    }

                    &.status-high {
                        background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
                        color: #388e3c;

                        .trend-icon {
                            margin-right: 6rpx;
                            font-size: 20rpx;
                        }
                    }
                }
            }

            .regis0-list-mid {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 20rpx;
                cursor: pointer;

                .user-info {
                    display: flex;
                    align-items: center;
                    flex: 1;
                }

                .mid-img {
                    width: 100rpx;
                    height: 100rpx;
                    margin-right: 20rpx;
                    border-radius: 50%;
                    overflow: hidden;
                    border: 3rpx solid #f0f0f0;

                    .avatar-img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }
                }

                .mid-name {
                    flex: 1;

                    .username {
                        font-size: 36rpx;
                        font-weight: 600;
                        color: #333;
                        margin-bottom: 8rpx;
                        display: block;
                    }

                    .apply-time {
                        font-size: 26rpx;
                        color: #999;
                        display: block;
                    }
                }

                .arrow-icon {
                    padding: 10rpx;
                }
            }

            .regis0-tag {
                display: flex;
                flex-wrap: wrap;
                gap: 12rpx;
                margin-bottom: 30rpx;

                .tag-item {
                    padding: 8rpx 16rpx;
                    background-color: #f5f5f5;
                    border-radius: 20rpx;
                    font-size: 24rpx;
                    color: #666;
                    border: 1rpx solid #e0e0e0;
                }
            }

            .regis0-click {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding-top: 20rpx;
                border-top: 1rpx solid #f0f0f0;

                .mark-button {
                    position: relative;
                    display: flex;
                    align-items: center;
                    padding: 12rpx 20rpx;
                    // background-color: #f8f9fa;
                    border-radius: 25rpx;
                    cursor: pointer;

                    text {
                        font-size: 28rpx;
                        color: #666;
                    }

                    // 标记弹窗样式
                    .mark-popup-container {
                        position: absolute;
                        bottom: 80rpx;
                        left: 30rpx;
                        z-index: 1000;
                        width: 320rpx;

                        .mark-popup {
                            background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
                            border-radius: 16rpx;
                            padding: 30rpx 25rpx;
                            box-sizing: border-box;
                            box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.3);
                            border: 1rpx solid rgba(255, 255, 255, 0.1);
                            position: relative;

                            .popup-arrow {
                                position: absolute;
                                bottom: -12rpx;
                                left: 50rpx;
                                width: 0;
                                height: 0;
                                border-left: 12rpx solid transparent;
                                border-right: 12rpx solid transparent;
                                border-top: 12rpx solid #4a5568;
                                z-index: 1001;

                                &::before {
                                    content: '';
                                    position: absolute;
                                    bottom: 1rpx;
                                    left: -12rpx;
                                    width: 0;
                                    height: 0;
                                    border-left: 12rpx solid transparent;
                                    border-right: 12rpx solid transparent;
                                    border-top: 12rpx solid rgba(255, 255, 255, 0.1);
                                }
                            }

                            .popup-title {
                                font-size: 28rpx;
                                color: #ffffff;
                                text-align: center;
                                margin-bottom: 25rpx;
                                font-weight: 500;
                                line-height: 1.3;
                                letter-spacing: 0.5rpx;
                            }

                            .popup-options {
                                display: flex;
                                flex-direction: column;
                                gap: 20rpx;
                            }

                            .popup-option {
                                display: flex;
                                align-items: center;
                                // padding: 12rpx 8rpx;
                                cursor: pointer;
                                transition: all 0.2s ease;
                                border-radius: 8rpx;

                                &:hover {
                                    background-color: rgba(255, 255, 255, 0.05);
                                }

                                &:active {
                                    opacity: 0.8;
                                    transform: scale(0.98);
                                }
                            }

                            .option-radio {
                                width: 34rpx;
                                height: 34rpx;
                                border: 2rpx solid rgba(255, 255, 255, 0.4);
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                margin-right: 20rpx;
                                flex-shrink: 0;
                                background-color: transparent;
                                transition: border-color 0.2s ease;
                            }

                            .radio-dot {
                                width: 18rpx;
                                height: 18rpx;
                                border-radius: 50%;
                                background-color: transparent;
                                transition: all 0.2s ease;

                                &.checked {
                                    background-color: #00d4aa;
                                    box-shadow: 0 0 8rpx rgba(0, 212, 170, 0.3);
                                }
                            }

                            .option-text {
                                font-size: 28rpx;
                                color: #ffffff;
                                flex: 1;
                                font-weight: 400;
                                line-height: 1.3;
                                letter-spacing: 0.3rpx;
                            }
                        }
                    }
                }

                .action-buttons {
                    display: flex;
                    gap: 12rpx;

                    .action-btn {
                        padding: 12rpx 24rpx;
                        border-radius: 25rpx;
                        font-size: 26rpx;
                        border: none !important;

                        &.call-btn {
                            background-color: #00d4aa;
                            color: white;
                        }

                        &.message-btn {
                            background-color: #f0f1f6;
                            color: #333;
                        }

                        &.wechat-btn {
                            background-color: #f0f1f6;
                            color: #333;
                        }
                    }
                }
            }
        }

        // 加载状态样式
        .loading-status {
            padding: 40rpx 0;
            text-align: center;

            .loading-more {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .loading-text {
                    margin-top: 20rpx;
                    font-size: 28rpx;
                    color: #999;
                }
            }

            .no-more {
                padding: 20rpx 0;
                font-size: 28rpx;
                color: #999;
                text-align: center;
            }
        }

        // 空状态样式
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 80rpx 0;

            .empty-icon {
                margin-bottom: 20rpx;

                .empty-img {
                    width: 120rpx;
                    height: 120rpx;
                    opacity: 0.6;
                }
            }

            .empty-text {
                font-size: 28rpx;
                color: #999;
            }
        }

        // 推荐卡片样式
        .regis-released {
            background-color: white;
            border-radius: 20rpx;
            margin-top: 40rpx;
            overflow: hidden;
            box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

            .released-header {
                width: 100%;
                height: 150rpx;
                overflow: hidden;

                .released-bg {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .released-content {
                padding: 30rpx;
                text-align: center;

                .released-text {
                    font-size: 32rpx;
                    color: #333;
                    margin-bottom: 30rpx;
                    display: block;

                    .highlight-number {
                        color: #00d4aa;
                        font-weight: 600;
                    }
                }

                .released-btn {
                    width: 200rpx;
                    height: 70rpx;
                    border-radius: 35rpx;
                    background: linear-gradient(135deg, #00d4aa 0%, #00bcd4 100%);
                    color: white;
                    font-size: 28rpx;
                    border: none !important;
                }
            }
        }
    }

}

.regis1-content {
    height: calc(100% - 170rpx);
    margin-top: -20rpx;
    border-radius: 20rpx 20rpx 0 0;
    background-color: #f0f1f6;
    // padding: 20rpx;
}

// ::v-deep .uni-button::after {
//     border: none !important;
// }

.popup-content {
    height: 1200rpx;
    padding: 30rpx 20rpx;
    background-color: #f6f7f9;

    .popup-top {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .code-btn::after {
        border: none !important;
    }

    // .popup-taball {
    //     display: flex;
    //     height: 80rpx;
    //     display: flex;
    //     align-items: center;


    // }

    .taball {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 40rpx 30rpx;
        background-color: white;
        border-radius: 20rpx;
        margin-top: 20rpx;

        .code-btn {
            width: 200rpx;
            margin-left: 30rpx;
            border: none !important;
            background-color: #f6f7f9;
            border-radius: 20rpx;
            color: #989fa4;
            font-weight: 600;
        }

        .activeJob {
            background-color: #02bdc4 !important;
            color: white;
            /* 高亮边框 */
        }
    }

    .popup-botom {
        display: flex;
        padding: 30rpx 0;
        color: black;
    }

    .popup-joblist-scroll {
        width: 100%;
        height: calc(100% - 450rpx) !important;
    }

    .popup-joblist {}

    // 职位列表加载状态样式
    .job-loading-status {
        padding: 30rpx 0;
        text-align: center;

        .loading-more {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .loading-text {
                margin-top: 15rpx;
                font-size: 26rpx;
                color: #999;
            }
        }

        .no-more {
            padding: 15rpx 0;
            font-size: 26rpx;
            color: #999;
            text-align: center;
        }
    }
}
</style>