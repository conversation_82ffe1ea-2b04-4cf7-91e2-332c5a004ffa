<template>
    <view class="money-detail-page">
        <!-- 顶部导航栏 -->
        <u-navbar :autoBack="true" title="积分明细" :titleStyle="{
            color: '#222',
            fontWeight: 'bold',
            fontSize: '36rpx'
        }" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder>
        </u-navbar>

        <!-- 交易记录列表 -->
        <view class="transaction-list">
            <view class="transaction-item" v-for="(item, index) in transactionList" :key="index">
                <!-- 左侧图标 -->
                <view class="transaction-icon" :style="{ backgroundColor: item.iconBg }">
                    <image class="icon-image" :src="item.icon" mode="aspectFit"></image>
                </view>

                <!-- 中间内容 -->
                <view class="transaction-content">
                    <view class="transaction-title">{{ item.title }}</view>
                    <view class="transaction-time">{{ formatTime(item.createtime) }}</view>
                </view>

                <!-- 右侧金额信息 -->
                <view class="transaction-amount">
                    <view class="amount-change" :class="{ 'positive': item.changetype ===1 , 'negative': item.changetype === 2 }">
                        {{ item.changetype === 2 ? '-' : '+' }}{{ item.score }}
                    </view>
                    <view class="balance-info">积分余额: {{ item.balance }}</view>
                </view>
            </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more" v-if="hasMore" @click="loadMore">
            <text class="load-text">加载更多</text>
        </view>

        <!-- 没有更多数据 -->
        <view class="no-more" v-else-if="transactionList.length > 0">
            <text class="no-more-text">没有更多数据了</text>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="transactionList.length === 0 && !loading">
            <image class="empty-icon" src="/static/empty.png" mode="aspectFit"></image>
            <text class="empty-text">暂无交易记录</text>
        </view>

    </view>
</template>

<script>
import { addApi } from "@/utils/api.js"
export default {
    data() {
        return {
            // 交易记录列表
            transactionList: [],
            // 是否还有更多数据
            hasMore: true,
            // 是否正在加载
            loading: false,
            // 当前页码
            currentPage: 1,
            size: 15
        };
    },
    async onShow() {
        uni.showLoading({
            title: '加载中'
        });
        // 重置分页参数
        this.currentPage = 1;
        this.hasMore = true;
        this.transactionList = [];

        // 加载第一页数据
        this.loadTransactionData();
    },

    // 上拉加载更多
    onReachBottom() {

        this.loadMore();
    },
    methods: {
        // 加载交易数据
        async loadTransactionData() {
            this.loading = true;
            try {
                const params = {
                    page: this.currentPage,
                    size: this.size
                }
                const res = await addApi.userScoreList(params)
                if (res.code == 200) {
                    console.log(res, '^^^')
                    // 如果是第一页，直接赋值；如果是加载更多，则追加数据
                    if (this.currentPage === 1) {
                        this.transactionList = res.data.list
                    } else {
                        this.transactionList.push(...res.data.list)
                    }
                    // 判断是否还有更多数据
                    this.hasMore = res.data.list.length === this.size
                    uni.hideLoading();
                } else {
                    uni.showToast({
                        title: '获取数据失败',
                        icon: 'none',
                        duration: 2000
                    });
                    uni.hideLoading();
                }

                // 模拟数据加载完成
                setTimeout(() => {
                    this.loading = false;
                }, 500);
            } catch (error) {
                console.error('加载交易数据失败:', error);
                uni.showToast({
                    title: '加载失败',
                    icon: 'none'
                });
                this.loading = false;
            }
        },

        // 加载更多
        loadMore() {
            if (this.loading || !this.hasMore) return;
            this.currentPage++;
            this.loadTransactionData();
        },

        // 格式化时间戳为日期时间
        formatTime(timestamp) {
            if (!timestamp) return '';

            // 如果时间戳是秒级别的，需要转换为毫秒
            const time = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp;
            const date = new Date(time);

            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }
    }
}
</script>

<style lang="scss">
/* 全局背景色和基础字体 */
page {
    background-color: #f5f5f5;
    color: #333;
    font-size: 28rpx;
}

.money-detail-page {
    min-height: 100vh;
    background-color: #f5f5f5;
}

/* 交易记录列表 */
.transaction-list {
    // padding: 0 30rpx;
    padding-top: 20rpx;
}

.transaction-item {
    display: flex;
    align-items: center;
    padding: 30rpx 0;
    background-color: #fff;
    margin-bottom: 2rpx;

    &:first-child {
        border-top-left-radius: 16rpx;
        border-top-right-radius: 16rpx;
    }

    &:last-child {
        border-bottom-left-radius: 16rpx;
        border-bottom-right-radius: 16rpx;
        margin-bottom: 20rpx;
    }
}

/* 左侧图标 */
.transaction-icon {
    width: 80rpx;
    height: 80rpx;
    margin-left: 30rpx;
    margin-right: 24rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon-image {
        width: 48rpx;
        height: 48rpx;
    }
}

/* 中间内容 */
.transaction-content {
    flex: 1;

    .transaction-title {
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
        margin-bottom: 8rpx;
        line-height: 1.4;
    }

    .transaction-time {
        font-size: 26rpx;
        color: #999;
        line-height: 1.2;
    }
}

/* 右侧金额信息 */
.transaction-amount {
    text-align: right;
    margin-right: 30rpx;

    .amount-change {
        font-size: 32rpx;
        font-weight: 600;
        margin-bottom: 8rpx;
        line-height: 1.4;

        &.positive {
            color: #F39C12;
        }

        &.negative {
            color: #333;
        }
    }

    .balance-info {
        font-size: 24rpx;
        color: #999;
        line-height: 1.2;
    }
}

/* 加载更多 */
.load-more {
    padding: 40rpx;
    text-align: center;

    .load-text {
        color: #666;
        font-size: 28rpx;
    }
}

/* 没有更多数据 */
.no-more {
    padding: 40rpx;
    text-align: center;

    .no-more-text {
        color: #999;
        font-size: 26rpx;
    }
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 40rpx;

    .empty-icon {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 40rpx;
        opacity: 0.6;
    }

    .empty-text {
        color: #999;
        font-size: 28rpx;
    }
}
</style>