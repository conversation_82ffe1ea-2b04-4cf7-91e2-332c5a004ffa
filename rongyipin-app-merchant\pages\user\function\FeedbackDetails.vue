<template>
    <view class="feedback-detail-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" title="反馈详情" :autoBack="true" :leftIconSize="30" :leftIconColor="'#333'"
                safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>

        <!-- 内容区域 -->
        <scroll-view class="content-container" scroll-y>
            <!-- 反馈内容 -->
            <view class="feedback-content-section">
                <text class="feedback-description">{{feedback.content}}</text>
            </view>

            <!-- 图片展示区域 -->
            <view class="image-section" v-if="feedback.pics && feedback.pics.length > 0">
                <view class="image-item" v-for="(image, index) in feedback.pics" :key="index"
                    @click="previewImage(image)">
                    <image :src="image.pic" mode="aspectFill" class="feedback-image"></image>
                </view>
            </view>

            <!-- 占位图片区域（如果没有图片） -->
            <view class="image-section" v-else>
                <view class="image-placeholder">
                    <view class="placeholder-icon">📷</view>
                </view>
            </view>

            <!-- 时间信息 -->
            <view class="time-section">
                <text class="feedback-time">{{ feedback.create_at || '2025-06-06 15:00' }}</text>
            </view>

            <!-- 反馈进度 -->
            <view class="progress-section" v-if="feedback.status  ==0 && feedback.is_reply == 0">
                <text class="progress-title">反馈进度</text>
                <view class="progress-item">
                    <view class="progress-dot active"></view>
                    <view class="progress-content">
                        <text class="progress-status">反馈提交成功</text>
                        <text class="progress-time">{{ feedback.create_at }}</text>
                    </view>
                </view>
            </view>
            <view class="progress-section" v-if="feedback.status  ==1 && feedback.is_reply == 1">
                <text class="progress-title">反馈进度</text>
                <view class="progress-item">
                    <view class="progress-content">
                        <text class="progress-status">系统回复:  {{ feedback.reply }}</text>
                        <text class="progress-time">{{ feedback.create_at }}</text>
                    </view>
                </view>
            </view>
        </scroll-view>
    </view>
</template>

<script>
import { rider } from '@/utils/api.js'

export default {
    data() {
        return {
            feedbackDetail: {
                id: '',
                content: '',
                images: [],
                create_time: '',
                status: 'submitted' // submitted: 已提交, processing: 处理中, completed: 已完成
            },
            feedback: {}
        }
    },
    onLoad(options) {
        if (options.item) {
            
            // 先解码再转对象
            this.feedback = JSON.parse(decodeURIComponent(options.item))
            console.log(this.feedback)
        }
    },
    methods: {


        // 预览图片
        previewImage(current) {
            uni.previewImage({
                current: current,
                urls: this.feedbackDetail.images
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.feedback-detail-page {
    min-height: 100vh;
    background-color: #E8F8F5;
}

/* 顶部导航栏 */
.navbar {
}

/* 内容容器 */
.content-container {
    height: calc(100vh - 88rpx);
    padding: 40rpx 30rpx;
    box-sizing: border-box;
}

/* 反馈内容区域 */
.feedback-content-section {
    margin-bottom: 40rpx;

    .feedback-description {
        font-size: 32rpx;
        color: #333;
        line-height: 1.6;
        word-wrap: break-word;
        word-break: break-all;
    }
}

/* 图片展示区域 */
.image-section {
    margin-bottom: 40rpx;

    .image-item {
        margin-bottom: 20rpx;
        border-radius: 12rpx;
        overflow: hidden;

        .feedback-image {
            width: 200rpx;
            height: 200rpx;
            border-radius: 12rpx;
            background-color: #f5f5f5;
        }
    }

    .image-placeholder {
        width: 200rpx;
        height: 200rpx;
        background-color: #f5f5f5;
        border-radius: 12rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .placeholder-icon {
            font-size: 60rpx;
            color: #ccc;
        }
    }
}

/* 时间区域 */
.time-section {
    margin-bottom: 60rpx;

    .feedback-time {
        font-size: 28rpx;
        color: #999;
    }
}

/* 反馈进度区域 */
.progress-section {
    .progress-title {
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
        margin-bottom: 30rpx;
        display: block;
    }

    .progress-item {
        display: flex;
        align-items: flex-start;

        .progress-dot {
            width: 20rpx;
            height: 20rpx;
            border-radius: 50%;
            background-color: #ddd;
            margin-right: 20rpx;
            margin-top: 8rpx;
            flex-shrink: 0;

            &.active {
                background-color: #10d2c3;
            }
        }

        .progress-content {
            flex: 1;

            .progress-status {
                font-size: 30rpx;
                color: #333;
                display: block;
                margin-bottom: 8rpx;
            }

            .progress-time {
                font-size: 26rpx;
                color: #999;
                display: block;
            }
        }
    }
}
</style>
