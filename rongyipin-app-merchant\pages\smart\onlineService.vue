<template>
    <view class="online-service-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" title="在线咨询" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
                <template #right>
                    <u-icon name="close" size="20" color="#333" @click="closePage"></u-icon>
                </template>
            </u-navbar>
        </view>

        <!-- 在线客服状态栏 -->
        <view class="service-status">
            <text class="status-text">在线客服</text>
        </view>

        <!-- 消息列表 -->
        <scroll-view class="message-list" scroll-y :scroll-into-view="lastMessageId" :scroll-with-animation="true">
            <!-- 历史消息分割线 -->
            <view class="history-divider">
                <text class="divider-text">以上为历史消息</text>
            </view>

            <!-- 时间显示 -->
            <view class="time-display">
                <text class="time-text">今天 {{ currentTime }}</text>
            </view>

            <!-- 小团团为您服务 -->
            <view class="service-notice">
                <text class="notice-text">小团团为您服务</text>
            </view>

            <!-- 循环渲染消息 -->
            <view v-for="(msg, index) in messages" :key="index" :id="'msg-' + index">
                <!-- 普通文本消息 -->
                <view v-if="msg.type === 'text'" class="message-item" :class="{ 'user-message': msg.isUser }">
                    <view v-if="!msg.isUser" class="bot-avatar">
                        <image class="avatar-img" src="/static/logo.png" />
                    </view>
                    <view class="message-bubble" :class="{ 'user-bubble': msg.isUser, 'bot-bubble': !msg.isUser }">
                        <text class="message-text">{{ msg.text }}</text>
                    </view>
                </view>

                <!-- 智能推荐问题卡片 -->
                <view v-if="msg.type === 'smart-questions'" class="smart-questions-card">
                    <view class="bot-avatar">
                        <image class="avatar-img" src="/static/logo.png" />
                    </view>
                    <view class="questions-container">
                        <view class="card-header">
                            <text class="card-title">热门问题</text>
                        </view>

                        <!-- 分类标签 -->
                        <scroll-view class="category-tabs" scroll-x show-scrollbar="false">
                            <view v-for="cat in questionCategories" :key="cat" class="tab-item"
                                :class="{ 'active': cat === currentCategory }" @click="switchCategory(cat)">
                                {{ cat }}
                            </view>
                        </scroll-view>

                        <!-- 问题列表 -->
                        <view class="question-list">
                            <view v-for="item in displayedQuestions" :key="item.id" class="question-item"
                                @click="selectQuestion(item.question)">
                                <text class="question-text">{{ item.question }}</text>
                                <u-icon name="arrow-right" size="14" color="#999"></u-icon>
                            </view>
                        </view>

                        <!-- 换一换 -->
                        <view class="refresh-btn" @click="refreshQuestions">
                            <u-icon name="reload" size="14" color="#999"></u-icon>
                            <text class="refresh-text">换一换</text>
                        </view>
                    </view>
                </view>
            </view>
        </scroll-view>

        <!-- 底部工具栏 -->
        <view class="bottom-toolbar">
            <!-- 快捷功能按钮 -->
            <view class="quick-actions">
                <view class="action-item" @click="openImagePicker">
                    <u-icon name="photo" size="24" color="#666"></u-icon>
                </view>
                <view class="action-item" @click="openVoiceInput">
                    <u-icon name="mic" size="24" color="#666"></u-icon>
                </view>
                <view class="action-item" @click="openMoreActions">
                    <u-icon name="plus-circle" size="24" color="#666"></u-icon>
                </view>
                <view class="action-item" @click="openEmoji">
                    <u-icon name="emoji" size="24" color="#666"></u-icon>
                </view>
            </view>

            <!-- 输入框和发送按钮 -->
            <view class="input-section">
                <input class="message-input" type="text" v-model="userInput" placeholder="输入消息..." 
                    @confirm="sendMessage" @focus="onInputFocus" @blur="onInputBlur" />
                <view class="send-btn" :class="{ 'active': userInput.trim() }" @click="sendMessage">
                    <text class="send-text">发送</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            userInput: '',
            messages: [],
            lastMessageId: '',
            currentTime: '',
            currentCategory: '推荐',
            questionCategories: ['推荐', '找工作', '账户信息', '企业服务'],
            categoryOffsets: {},
            isInputFocused: false,
            // 智能问答数据
            smartQuestions: {
                '推荐': [
                    { id: 1, question: '未成年可以做兼职吗？' },
                    { id: 2, question: '近期有兼职记录可以注销账户吗？' },
                    { id: 3, question: '消息界面如何发图片？' },
                    { id: 4, question: '如何做兼职' },
                    { id: 5, question: '为何提示账户异常' },
                    { id: 6, question: '如何申请退款？' },
                    { id: 7, question: '工作时间安排' }
                ],
                '找工作': [
                    { id: 8, question: '如何搜索合适的工作？' },
                    { id: 9, question: '投递简历后多久有回复？' },
                    { id: 10, question: '面试需要准备什么？' },
                    { id: 11, question: '工作地点可以更改吗？' },
                    { id: 12, question: '薪资待遇如何计算？' }
                ],
                '账户信息': [
                    { id: 13, question: '如何修改个人信息？' },
                    { id: 14, question: '忘记密码怎么办？' },
                    { id: 15, question: '如何注销账户？' },
                    { id: 16, question: '实名认证流程' },
                    { id: 17, question: '账户安全设置' }
                ],
                '企业服务': [
                    { id: 18, question: '如何发布招聘信息？' },
                    { id: 19, question: '企业认证流程' },
                    { id: 20, question: '招聘费用说明' },
                    { id: 21, question: '简历筛选技巧' },
                    { id: 22, question: '面试邀请发送' }
                ]
            },
            // 智能回答数据
            smartAnswers: {
                '未成年可以做兼职吗？': '根据相关法律法规，16周岁以上的未成年人可以从事一些轻体力劳动，但需要监护人同意，并且不能影响学业。建议选择适合的兼职工作。',
                '近期有兼职记录可以注销账户吗？': '有兼职记录的账户可以申请注销，但需要先处理完所有未完成的工作订单和结算事宜。具体流程请联系客服协助处理。',
                '消息界面如何发图片？': '在聊天界面点击输入框左侧的"+"号，选择"图片"选项，然后从相册选择或拍照即可发送图片。',
                '如何做兼职': '您可以通过以下步骤开始兼职：1.完善个人资料 2.浏览兼职岗位 3.投递简历 4.等待面试通知 5.开始工作。',
                '为何提示账户异常': '账户异常可能由以下原因导致：1.登录地点异常 2.操作频繁 3.信息不完整。请联系客服进行账户验证。'
            }
        };
    },
    computed: {
        displayedQuestions() {
            const pageSize = 5;
            const questionsInCategory = this.smartQuestions[this.currentCategory] || [];
            const offset = this.categoryOffsets[this.currentCategory] || 0;
            return questionsInCategory.slice(offset, offset + pageSize);
        }
    },
    onLoad() {
        this.initializeChat();
        this.updateCurrentTime();
        // 初始化分类偏移量
        this.questionCategories.forEach(cat => {
            this.$set(this.categoryOffsets, cat, 0);
        });
    },
    methods: {
        // 初始化聊天
        initializeChat() {
            // 添加欢迎消息
            this.addMessage('您好，我是智能机器人团团，请问有什么可以帮助您？ 😊', false);

            // 添加智能推荐问题卡片
            setTimeout(() => {
                this.messages.push({
                    type: 'smart-questions'
                });
                this.scrollToBottom();
            }, 500);
        },

        // 更新当前时间
        updateCurrentTime() {
            const now = new Date();
            const hours = now.getHours().toString().padStart(2, '0');
            const minutes = now.getMinutes().toString().padStart(2, '0');
            this.currentTime = `${hours}:${minutes}`;
        },

        // 发送消息
        sendMessage() {
            const message = this.userInput.trim();
            if (!message) return;

            // 添加用户消息
            this.addMessage(message, true);
            this.userInput = '';

            // 模拟机器人回复
            setTimeout(() => {
                const answer = this.getSmartAnswer(message);
                this.addMessage(answer, false);
            }, 800);
        },

        // 选择问题
        selectQuestion(question) {
            this.addMessage(question, true);

            setTimeout(() => {
                const answer = this.getSmartAnswer(question);
                this.addMessage(answer, false);
            }, 500);
        },

        // 获取智能回答
        getSmartAnswer(question) {
            // 优先匹配完全相同的问题
            if (this.smartAnswers[question]) {
                return this.smartAnswers[question];
            }

            // 关键词匹配
            const keywords = {
                '兼职': '您可以在首页浏览各种兼职岗位，选择适合自己的工作。如需帮助，请联系客服。',
                '账户': '关于账户问题，建议您检查个人信息是否完整，如有疑问请联系客服处理。',
                '工作': '我们提供多种类型的工作机会，您可以根据自己的时间和技能选择合适的岗位。',
                '认证': '请按照系统提示完成实名认证，上传清晰的身份证照片即可。',
                '退款': '退款申请需要提供相关凭证，客服会在1-3个工作日内处理完成。'
            };

            for (const [keyword, answer] of Object.entries(keywords)) {
                if (question.includes(keyword)) {
                    return answer;
                }
            }

            return '抱歉，团团暂时无法回答您的问题，您可以尝试换个问法，或者联系人工客服获得帮助。';
        },

        // 添加消息
        addMessage(text, isUser) {
            this.messages.push({
                type: 'text',
                text: text,
                isUser: isUser,
                timestamp: new Date().getTime()
            });
            this.scrollToBottom();
        },

        // 滚动到底部
        scrollToBottom() {
            this.$nextTick(() => {
                this.lastMessageId = 'msg-' + (this.messages.length - 1);
            });
        },

        // 切换分类
        switchCategory(category) {
            this.currentCategory = category;
        },

        // 换一换问题
        refreshQuestions() {
            const pageSize = 5;
            const category = this.currentCategory;
            const questionsInCategory = this.smartQuestions[category] || [];
            const totalQuestions = questionsInCategory.length;

            let newOffset = (this.categoryOffsets[category] || 0) + pageSize;
            if (newOffset >= totalQuestions) {
                newOffset = 0; // 回到开头
            }

            this.$set(this.categoryOffsets, category, newOffset);
        },

        // 关闭页面
        closePage() {
            uni.navigateBack();
        },

        // 输入框聚焦
        onInputFocus() {
            this.isInputFocused = true;
        },

        // 输入框失焦
        onInputBlur() {
            this.isInputFocused = false;
        },

        // 打开图片选择器
        openImagePicker() {
            uni.chooseImage({
                count: 1,
                success: (res) => {
                    // 处理图片上传逻辑
                    console.log('选择图片:', res.tempFilePaths[0]);
                    this.addMessage('[图片]', true);
                    setTimeout(() => {
                        this.addMessage('收到您的图片，团团正在为您分析...', false);
                    }, 500);
                }
            });
        },

        // 打开语音输入
        openVoiceInput() {
            uni.showToast({
                title: '语音功能开发中',
                icon: 'none'
            });
        },

        // 打开更多操作
        openMoreActions() {
            uni.showActionSheet({
                itemList: ['发送位置', '联系人工客服', '意见反馈'],
                success: (res) => {
                    const actions = ['发送位置', '联系人工客服', '意见反馈'];
                    const selectedAction = actions[res.tapIndex];

                    if (selectedAction === '联系人工客服') {
                        this.addMessage('我要联系人工客服', true);
                        setTimeout(() => {
                            this.addMessage('正在为您转接人工客服，请稍候...', false);
                        }, 500);
                    }
                }
            });
        },

        // 打开表情选择
        openEmoji() {
            uni.showToast({
                title: '表情功能开发中',
                icon: 'none'
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.online-service-page {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #f5f5f5;
}

.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
}

.service-status {
    margin-top: 88rpx; // 导航栏高度
    background: linear-gradient(135deg, #4cd964, #5ac777);
    padding: 30rpx;
    text-align: center;

    .status-text {
        color: white;
        font-size: 32rpx;
        font-weight: 500;
    }
}

.message-list {
    flex: 1;
    padding: 20rpx;
    padding-bottom: 200rpx; // 为底部工具栏留出空间
}

.history-divider {
    text-align: center;
    margin: 40rpx 0;

    .divider-text {
        color: #999;
        font-size: 24rpx;
        position: relative;

        &::before,
        &::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 100rpx;
            height: 1rpx;
            background-color: #ddd;
        }

        &::before {
            right: 100%;
            margin-right: 20rpx;
        }

        &::after {
            left: 100%;
            margin-left: 20rpx;
        }
    }
}

.time-display {
    text-align: center;
    margin: 20rpx 0;

    .time-text {
        background-color: rgba(0, 0, 0, 0.1);
        color: #666;
        font-size: 24rpx;
        padding: 8rpx 16rpx;
        border-radius: 12rpx;
    }
}

.service-notice {
    text-align: center;
    margin: 20rpx 0;

    .notice-text {
        background-color: rgba(76, 217, 100, 0.1);
        color: #4cd964;
        font-size: 24rpx;
        padding: 8rpx 16rpx;
        border-radius: 12rpx;
    }
}

.message-item {
    display: flex;
    margin-bottom: 30rpx;
    align-items: flex-start;

    &.user-message {
        justify-content: flex-end;
    }
}

.bot-avatar {
    width: 80rpx;
    height: 80rpx;
    margin-right: 20rpx;
    flex-shrink: 0;

    .avatar-img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: #4cd964;
    }
}

.message-bubble {
    max-width: 65%;
    padding: 24rpx 30rpx;
    border-radius: 20rpx;
    position: relative;

    &.bot-bubble {
        background-color: white;
        border-top-left-radius: 8rpx;

        .message-text {
            color: #333;
        }
    }

    &.user-bubble {
        background-color: #4cd964;
        border-top-right-radius: 8rpx;

        .message-text {
            color: white;
        }
    }
}

.message-text {
    font-size: 30rpx;
    line-height: 1.4;
    word-break: break-all;
}

.smart-questions-card {
    display: flex;
    margin-bottom: 30rpx;
    align-items: flex-start;
}

.questions-container {
    background-color: white;
    border-radius: 20rpx;
    border-top-left-radius: 8rpx;
    padding: 30rpx;
    max-width: 65%;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.card-header {
    margin-bottom: 20rpx;

    .card-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
    }
}

.category-tabs {
    white-space: nowrap;
    margin-bottom: 30rpx;

    .tab-item {
        display: inline-block;
        padding: 16rpx 24rpx;
        margin-right: 16rpx;
        font-size: 28rpx;
        color: #666;
        background-color: #f8f8f8;
        border-radius: 30rpx;
        transition: all 0.3s;

        &.active {
            background: linear-gradient(135deg, #4cd964, #5ac777);
            color: white;
            font-weight: 500;
        }
    }
}

.question-list {
    .question-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx 0;
        border-bottom: 1rpx solid #f0f0f0;
        transition: background-color 0.2s;

        &:last-child {
            border-bottom: none;
        }

        &:active {
            background-color: #f8f8f8;
        }

        .question-text {
            font-size: 28rpx;
            color: #333;
            flex: 1;
        }
    }
}

.refresh-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20rpx 0;
    margin-top: 10rpx;

    .refresh-text {
        margin-left: 8rpx;
        font-size: 26rpx;
        color: #999;
    }
}

.bottom-toolbar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    border-top: 1rpx solid #e0e0e0;
    padding: 20rpx;
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.quick-actions {
    display: flex;
    margin-bottom: 20rpx;

    .action-item {
        width: 80rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;
        background-color: #f8f8f8;
        border-radius: 50%;
        transition: background-color 0.2s;

        &:active {
            background-color: #e8e8e8;
        }
    }
}

.input-section {
    display: flex;
    align-items: center;

    .message-input {
        flex: 1;
        height: 80rpx;
        padding: 0 30rpx;
        background-color: #f8f8f8;
        border-radius: 40rpx;
        font-size: 28rpx;
        border: none;
        outline: none;
    }

    .send-btn {
        margin-left: 20rpx;
        height: 80rpx;
        line-height: 80rpx;
        padding: 0 40rpx;
        background-color: #e0e0e0;
        color: #999;
        border-radius: 40rpx;
        font-size: 28rpx;
        text-align: center;
        transition: all 0.3s;

        &.active {
            background-color: #4cd964;
            color: white;
        }

        .send-text {
            font-weight: 500;
        }
    }
}
</style>
