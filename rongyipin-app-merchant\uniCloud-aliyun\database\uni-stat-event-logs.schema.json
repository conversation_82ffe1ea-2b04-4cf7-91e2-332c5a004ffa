// 应用事件日志表
{
	"bsonType": "object",
	"description": "记录上报的事件日志",
	"required": [],
	"permission": {
		"read": "'READ_UNI_STAT_EVENT_LOGS' in auth.permission",
		"create": false,
		"update": false,
		"delete": false
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"appid": {
			"bsonType": "string",
			"description": "客户端上报的应用ID"
		},
		"version": {
			"bsonType": "string",
			"description": "客户端上报的应用版本号"
		},
		"platform": {
			"bsonType": "string",
			"foreignKey": "uni-stat-app-platforms.code",
			"description": "客户端上报的平台code"
		},
		"channel": {
			"bsonType": "string",
			"description": "客户端上报的渠道code\/场景值"
		},
		"device_id": {
			"bsonType": "string",
			"description": "客户端携带的设备标识"
		},
		"uid": {
			"bsonType": "string",
			"description": "用户编号, 对应uni-id-users._id"
		},
		"session_id": {
			"bsonType": "string",
			"description": "访问会话日志ID，对应uni-stat-session-logs._id",
			"foreignKey": "uni-stat-session-logs._id"
		},
		"page_id": {
			"bsonType": "string",
			"description": "页面ID，对应uni-stat-pages._id",
			"foreignKey": "uni-stat-pages._id"
		},
		"event_key": {
			"bsonType": "string",
			"description": "客户端上报的key"
		},
		"param": {
			"bsonType": "string",
			"description": "事件参数"
		},
		"sdk_version": {
			"bsonType": "string",
			"description": "基础库版本号"
		},
		"platform_version": {
			"bsonType": "string",
			"description": "平台版本，如微信、支付宝宿主App版本号"
		},
		"device_os": {
			"bsonType": "int",
			"description": "设备系统编号，1：安卓，2：iOS，3：PC"
		},
		"device_os_version": {
			"bsonType": "string",
			"description": "设备系统版本"
		},
		"device_net": {
			"bsonType": "string",
			"description": "设备网络型号wifi\/3G\/4G\/"
		},
		"device_vendor": {
			"bsonType": "string",
			"description": "设备供应商 "
		},
		"device_model": {
			"bsonType": "string",
			"description": "设备型号"
		},
		"device_language": {
			"bsonType": "string",
			"description": "设备语言包"
		},
		"device_pixel_ratio": {
			"bsonType": "string",
			"description": "设备像素比 "
		},
		"device_window_width": {
			"bsonType": "string",
			"description": "设备窗口宽度 "
		},
		"device_window_height": {
			"bsonType": "string",
			"description": "设备窗口高度"
		},
		"device_screen_width": {
			"bsonType": "string",
			"description": "设备屏幕宽度"
		},
		"device_screen_height": {
			"bsonType": "string",
			"description": "设备屏幕高度"
		},
		"create_time": {
			"bsonType": "timestamp",
			"description": "创建时间"
		}
	}
}
