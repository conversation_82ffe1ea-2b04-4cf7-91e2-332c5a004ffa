<template>
    <view class="container">
        <u-navbar :autoBack="true" title="工作地点" :titleStyle="{
            color: '#222',
            fontWeight: 'bold',
            fontSize: '36rpx'
        }" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>
        <view class="search-input">
            <view class="current-city" @click="selectPosition">{{ currentCity }} <u-icon size="20"
                    name="arrow-down-fill"></u-icon></view>
            <u--input placeholder="请输入内容" border="surround" v-model="value" @focus="change"></u--input>
        </view>

        <view class="city-list-section">
            <scroll-view class="city-list" scroll-y>
                <u-radio-group v-model="value" iconSize="20">
                    <view class="city-item" v-for="(city, index) in cityList" :key="index">

                        <u-radio :name="city.title" :size="30" :iconSize="20" activeColor="#039885"></u-radio>
                        <view class="content" @click="selectCitylate(city)">
                            <p class="city-title" :class="{ active: value === city.title }">{{ city.title }}</p>
                            <p class="city-address">{{ city.address }}</p>
                        </view>

                    </view>
                </u-radio-group>
            </scroll-view>
        </view>
        <u-popup :show="show" :round="20" mode="bottom" @close="close" @open="open">
            <view class="popup-title">选择职位所在城市</view>
            <view class="popup-container">
                <!-- 加载中状态 -->
                <template v-if="loading">
                    <view class="loading-container">
                        <u-loading-icon mode="circle" size="28"></u-loading-icon>
                        <text class="loading-text">加载中...</text>
                    </view>
                </template>
                <template v-else>
                    <view class="province-list">
                        <scroll-view scroll-y>
                            <view v-for="(province, index) in provinces" :key="index" class="province-item"
                                :class="{ active: selectedProvince === index }" @click="selectProvince(index)">
                                {{ province.name }}
                            </view>
                        </scroll-view>
                    </view>
                    <view class="city-list">
                        <scroll-view scroll-y>
                            <view v-for="(city, cIndex) in provinces[selectedProvince].children" :key="cIndex"
                                class="city-item" @click="selectCity(city)">
                                <text :class="{ checked: city === selectedCity }">{{ city.name }}</text>
                                <u-icon v-if="city === selectedCity" name="checkmark" size="20" color="#00C2B3" />
                            </view>
                        </scroll-view>
                    </view>
                </template>

            </view>
            <view class="popup-footer">
                <u-button type="primary" @click="confirmCity">完成</u-button>
            </view>
        </u-popup>
    </view>
</template>

<script>
import { homeApi } from '@/utils/api';
export default {
    data() {
        return {
            currentCity: '',
            cityList: [],
            value: '', // 当前选中的城市
            show: false,
            provinces: [],
            selectedProvince: 0,
            selectedCity: '',
            loading: true,
        };
    },
    onShow() {

    },
    onLoad() {
        this.getCurrentCity();
        this.loading = true;
    },
    mounted() {
    },
    methods: {
        // 根据城市名查找对应的省份索引和城市对象
        findProvinceAndCity(cityName) {
            console.log(cityName, '这是打印');
            // 去掉可能存在的"市"字
            const targetCity = cityName;

            for (let i = 0; i < this.provinces.length; i++) {
                const province = this.provinces[i];
                const city = province.children.find(city =>
                    city.name === targetCity
                );

                if (city) {
                    return {
                        provinceIndex: i,
                        cityObj: city
                    };
                }
            }
            return null;
        },
        getCurrentCity() {
            uni.showLoading({
                title: '定位中...'
            });
            // #ifdef H5
            homeApi.getCityLists({
                key: 'QMEBZ-QAUKB-HWGUE-NCFVQ-SKKBH-ULBYV',
                // ip: '*************',
            }).then(res => {
                console.log(res, 'city');
                this.currentCity = res.result.ad_info.city

                uni.hideLoading();

                this.surrounding(res.result.location.lat, res.result.location.lng)
            })
            // #endif

            // #ifdef APP-PLUS
            uni.request({
                url: 'https://apis.map.qq.com/ws/location/v1/ip', //仅为示例，并非真实接口地址。
                data: {
                    key: 'QMEBZ-QAUKB-HWGUE-NCFVQ-SKKBH-ULBYV',
                },
                methods: 'GET',
                success: (res) => {
                    console.log('这是定位',res)
                    if (res.data.message == 'Success') {
                        this.currentCity = res.data.result.ad_info.city

                        uni.hideLoading();

                        this.surrounding(res.data.result.location.lat, res.data.result.location.lng)
                    }
                    else {
                        uni.showToast({
                            title: '定位失败',
                            icon: 'none'
                        });
                    }
                }
            });
            // #endif
        },
        surrounding(latitude, longitude) {
            uni.showLoading({
                title: '加载中...'
            });
            // #ifdef H5
            homeApi.getSurroun({
                key: 'QMEBZ-QAUKB-HWGUE-NCFVQ-SKKBH-ULBYV',
                page_index: '1',
                page_size: '10',
                boundary: `nearby(${latitude},${longitude},1000)`
            }).then(res => {
                this.cityList = res.data
                uni.hideLoading();
            })
            // #endif

            // #ifdef APP-PLUS
            uni.request({
                url: 'https://apis.map.qq.com/ws/place/v1/search', //仅为示例，并非真实接口地址。
                data: {
                    key: 'QMEBZ-QAUKB-HWGUE-NCFVQ-SKKBH-ULBYV',
                    page_index: '1',
                    page_size: '10',
                    boundary: `nearby(${latitude},${longitude},1000)`
                },
                methods: 'GET',
                success: (res) => {
                    if (res.data.message == 'Success') {
                        this.cityList = res.data.data
                        uni.hideLoading();
                    }
                    else {
                        uni.showToast({
                            title: '加载失败',
                            icon: 'none'
                        });
                    }
                }
            });
            // #endif

        },
        selectCitylate(city) {
            console.log(city, '这是城市的选择');
            this.value = city.title
            this.$store.commit('setCationdetail', city);
            // 其他选中逻辑
            uni.navigateBack()
        },
        // radioGroupChange(name) {
        //     console.log(name, '!!!!!!');
        //     this.value = name
        //     this.$store.commit('setcation', this.value);
        //     // 其他选中逻辑
        //     uni.navigateBack()
        // },
        change() {
            console.log(this.currentCity)
            this.$store.commit('setCurrentCity', this.currentCity);
            uni.navigateTo({
                url: './serch'
            })
        },
        selectPosition() {
            this.open()
        },
        async open() {
            this.show = true;
            // 没有缓存则加载数据
            this.loading = true;
            try {
                const res = await homeApi.getcitylate();
                if (res.code === 200) {
                    this.provinces = res.data;
                    this.locateCurrentCity();
                }
            } catch (error) {
                uni.showToast({
                    title: '加载失败，请重试',
                    icon: 'none'
                });
            } finally {
                this.loading = false;
            }
        },
        // 定位到当前城市
        locateCurrentCity() {
            if (!this.currentCity || !this.provinces.length) return;

            const result = this.findProvinceAndCity(this.currentCity);
            if (result) {
                console.log(result, '这是打印');
                this.selectedProvince = result.provinceIndex;
                this.selectedCity = result.cityObj;

                // 确保滚动到选中的省份和城市
                this.$nextTick(() => {
                    // 获取省份列表和城市列表的scroll-view引用
                    const provinceScroll = this.$refs.provinceScroll;
                    const cityScroll = this.$refs.cityScroll;

                    if (provinceScroll) {
                        provinceScroll.scrollTo({
                            top: result.provinceIndex * 48, // 假设每个省份项高度为48px
                            behavior: 'smooth'
                        });
                    }
                });
            }
        },
        preloadData() {
            if (!this.provincesCache) {
                homeApi.getcitylate().then(res => {
                    if (res.code === 200) {
                        this.provincesCache = res.data;
                    }
                });
            }
        },
        close() {
            this.show = false
            this.selectedPosition = null
            this.selectedAddress = ''
            this.showAddress = false
        },
        selectProvince(index) {
            this.selectedProvince = index;
            // 如果切换省份，清空已选城市
            if (!this.provinces[index].children.includes(this.selectedCity)) {
                this.selectedCity = null;
            }
        },
        selectCity(city) {
            this.selectedCity = city;
        },
        confirmCity() {
            if (this.selectedCity) {
                console.log(this.selectedCity, '这是打印');
                this.currentCity = this.selectedCity.name;
                // 其他确认逻辑...
                this.surrounding(this.selectedCity.gcj02_lat, this.selectedCity.gcj02_lng)
                this.close();
            } else {
                uni.showToast({
                    title: '请选择城市',
                    icon: 'none'
                });
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.container {
    /* padding: 20rpx; */
    height: 100vh;
}

.search-input {
    // padding: 30rpx;
    width: 93%;
    height: 80rpx;
    margin: 0 auto;
    display: flex;
    border: 1px solid #ccc;
    border-radius: 20rpx;
    margin-top: 30rpx;

    .current-city {
        display: flex;
        align-items: center;
        padding: 0 20rpx;

        .u-icon {
            margin-left: 10rpx;
        }
    }
}

::v-deep .u-input {
    border-left: 1px solid #ccc;
    // border-radius: 20rpx;
    margin-left: 5rpx;
}

.city-list-section {
    padding: 30rpx;
    height: calc(100vh - 230rpx);
    overflow-y: scroll;

    ::v-deep .u-radio-group--row {
        display: inline;
    }

    .city-item {
        width: 100%;
        height: 130rpx;
        display: flex;
        align-items: center;

        .content {
            flex: 1;
            margin-left: 20rpx;
        }

        // border-bottom: 1px solid #ccc;
        .city-title {
            font-size: 30rpx;
            color: #333;
        }

        .city-title.active {
            color: #50b1b2;
            /* 被选中时变绿色 */
        }

        .city-address {
            font-size: 27rpx;
            color: #999;
            margin-top: 10rpx;
        }
    }
}

.popup-title {
    width: 100%;
    padding: 30rpx;
    width: 100%;
    font-size: 37rpx;
    color: #333;
    font-weight: bold;
}

.popup-container {
    display: flex;
    height: 1000rpx;
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
    overflow: hidden;

    .loading-container {
        width: 100%;
        height: 200rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .loading-text {
            margin-top: 20rpx;
            font-size: 28rpx;
            color: #999;
        }
    }

    .province-list {
        width: 45%;
        background-color: #f5f7fa;
        overflow-y: auto;
    }

    .city-list {
        flex: 1;
        background-color: #fff;
        overflow-y: auto;
    }

    .province-item,
    .city-item {
        padding: 24rpx;
        text-align: center;
    }

    .province-item.active {
        color: #00C2B3;
        font-weight: bold;
        background-color: #fff;
    }

    .city-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx;
    }

    .city-item text.checked {
        color: #00C2B3;
        font-weight: bold;
    }


}

.popup-footer {
    padding: 20rpx;
    border-top: 1px solid #f0f0f0;
    background-color: #fff;

    ::v-deep .u-button {
        background-color: #50b1b2;
        border: 1px solid #50b1b2;
    }
}

@keyframes skeleton-loading {
    0% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0 50%;
    }
}
</style>