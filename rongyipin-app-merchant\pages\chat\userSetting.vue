<template>
    <view class="user-setting-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" title="" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>

        <!-- 内容区域 -->
        <view class="content-container">
            <!-- 用户信息头部 -->
            <view class="user-header">
                <view class="user-avatar">
                    <image :src="userInfo.avatarUrl" class="avatar-image"></image>
                </view>
                <view class="user-info">
                    <text class="user-name">{{ userInfo.username || '王大壮' }}</text>
                    <text class="user-title">{{ userInfo.job_info.name || 'UI设计师' }}</text>
                </view>
                <!-- <view class="header-arrow">
                    <u-icon name="arrow-right" size="16" color="#999"></u-icon>
                </view> -->
            </view>

            <!-- 设置选项列表 -->
            <view class="setting-list">
                <!-- 标签和备注 -->
                <view class="setting-item" @click="handleTagsAndNotes">
                    <text class="setting-label">标签和备注</text>
                    <view class="setting-right">
                        <u-icon name="arrow-right" size="26" color="#999"></u-icon>
                    </view>
                </view>

                <!-- 置顶联系人 -->
                <view class="setting-item">
                    <text class="setting-label">置顶联系人</text>
                    <view class="setting-right">
                        <u-switch v-model="settings.isTop" :activeColor="'#007AFF'" size="30"></u-switch>
                    </view>
                </view>

                <!-- 标记为不合适 -->
                <view class="setting-item">
                    <text class="setting-label">标记为不合适</text>
                    <view class="setting-right">
                        <u-switch v-model="settings.isInappropriate" :activeColor="'#007AFF'" size="30"></u-switch>
                    </view>
                </view>
            </view>

            <!-- 删除联系人按钮 -->
            <view class="delete-section">
                <view class="delete-btn" @click="handleDeleteContact">
                    <text class="delete-text">删除联系人</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import {chat} from "@/utils/api.js"
export default {
    data() {
        return {
            // 用户信息
            userInfo: {
                id: '',
                name: '王大壮',
                title: 'UI设计师',
                avatar: ''
            },
            // 设置选项
            settings: {
                isTop: false,        // 置顶联系人
                isInappropriate: false  // 标记为不合适
            }
        }
    },
    onLoad(options) {
        console.log('用户设置页面参数:', options)
        if (options && options.userInfo) {
            try {
                this.userInfo = JSON.parse(decodeURIComponent(options.userInfo))
                this.settings.isTop =this.userInfo.recruiter_sort==0?false:true
                this.settings.isInappropriate =this.userInfo.unseemliness_status
                console.log('用户信息:', this.userInfo)
            } catch (e) {
                console.error('用户信息解析失败:', e)
            }
        }

        // 加载用户设置
        this.loadUserSettings()
    },

    methods: {
        // 加载用户设置
        async loadUserSettings() {
            try {
                // 这里可以调用API获取用户的设置信息
                // const response = await api.getUserSettings(this.userInfo.id)
                // if (response.code === 200) {
                //     this.settings = response.data
                // }
                console.log('加载用户设置')
            } catch (error) {
                console.error('加载用户设置失败:', error)
            }
        },

        // 处理标签和备注点击
        handleTagsAndNotes() {
            console.log('点击标签和备注')
            // 跳转到标签和备注页面
            uni.navigateTo({
                url: '/pages/chat/tagsAndNotes?userInfo=' + encodeURIComponent(JSON.stringify(this.userInfo))
            })
        },

        // 处理删除联系人
        handleDeleteContact() {
            uni.showModal({
                title: '删除联系人',
                content: '确定要删除该联系人吗？删除后将无法恢复。',
                confirmText: '删除',
                confirmColor: '#FF3B30',
                success: (res) => {
                    if (res.confirm) {
                        this.deleteContact()
                    }
                }
            })
        },

        // 删除联系人
        async deleteContact() {
            try {
                uni.showLoading({
                    title: '删除中...'
                })

                // 调用删除API
                // const response = await api.deleteContact(this.userInfo.id)
                // if (response.code === 200) {
                //     uni.showToast({
                //         title: '删除成功',
                //         icon: 'success'
                //     })
                //     setTimeout(() => {
                //         uni.navigateBack()
                //     }, 1500)
                // }

                // 模拟删除成功
                setTimeout(() => {
                    uni.hideLoading()
                    uni.showToast({
                        title: '删除成功',
                        icon: 'success'
                    })
                    setTimeout(() => {
                        uni.navigateBack()
                    }, 1500)
                }, 1000)

            } catch (error) {
                console.error('删除联系人失败:', error)
                uni.hideLoading()
                uni.showToast({
                    title: '删除失败',
                    icon: 'none'
                })
            }
        },

        // 监听设置变化
        async onSettingChange(key, value) {
            console.log(`设置变化: ${key} = ${value}`)
            if(key=='isTop'){
                const params ={
                    conversation_id:this.userInfo.id,
                    status:value ==false?0:1
                }
                const res = await chat.pinmessage(params)
                if(res.code==200){
                    uni.showToast({
                        title: '操作成功',
                        icon: 'success'
                    })
                    uni.hideLoading()
                }else{
                    uni.showToast({
                        title: res.msg,
                        icon: 'error'
                    })
                    uni.hideLoading()
                }
                console.log('置顶结果:',res)
            }
            // 这里可以调用API保存设置
            // api.updateUserSetting(this.userInfo.id, key, value)
        },

        // 获取头像文字（取姓名的最后一个字符）
        getAvatarText(name) {
            if (!name) return '王'
            return name.slice(-1)
        }
    },

    // 监听数据变化
    watch: {
        'settings.isTop'(newVal) {
            this.onSettingChange('isTop', newVal)
        },
        'settings.isInappropriate'(newVal) {
            this.onSettingChange('isInappropriate', newVal)
        }
    }
}
</script>

<style lang="scss" scoped>
.user-setting-page {
    min-height: 100vh;
    background-color: #f8f8f8;
}

/* 顶部导航栏 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background-color: #fff;
}

/* 内容容器 */
.content-container {
    margin-top: 88rpx;
    padding: 0;
}

/* 用户信息头部 */
.user-header {
    display: flex;
    align-items: center;
    background-color: #fff;
    padding: 40rpx 30rpx;
    margin-bottom: 20rpx;

    .user-avatar {
        margin-right: 30rpx;

        .avatar-circle {
            width: 100rpx;
            height: 100rpx;
            border-radius: 50%;
            background-color: #FF9500;
            display: flex;
            align-items: center;
            justify-content: center;

            .avatar-text {
                font-size: 36rpx;
                color: #fff;
                font-weight: 600;
            }
        }

        .avatar-image {
            width: 100rpx;
            height: 100rpx;
            border-radius: 50%;
        }
    }

    .user-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8rpx;

        .user-name {
            font-size: 36rpx;
            color: #333;
            font-weight: 600;
        }

        .user-title {
            font-size: 28rpx;
            color: #666;
        }
    }

    .header-arrow {
        margin-left: 20rpx;
    }
}

/* 设置选项列表 */
.setting-list {
    background-color: #fff;
    margin-bottom: 40rpx;
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
        border-bottom: none;
    }

    .setting-label {
        font-size: 32rpx;
        color: #333;
    }

    .setting-right {
        display: flex;
        align-items: center;
    }
}

/* 删除联系人区域 */
.delete-section {
    padding: 0 30rpx;
    margin-top: 60rpx;

    .delete-btn {
        background-color: #fff;
        border-radius: 12rpx;
        padding: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .delete-text {
            font-size: 32rpx;
            color: #FF3B30;
            font-weight: 500;
        }

        &:active {
            background-color: #f8f8f8;
        }
    }
}

/* 点击效果 */
.user-header:active,
.setting-item:active {
    background-color: #f8f8f8;
}
</style>
