<template>
    <view class="invoice-page">
        <!-- 1. 自定义导航栏 -->
        <u-navbar :autoBack="true" title="我的发票" :titleStyle="{
            color: '#222',
            fontWeight: 'bold',
            fontSize: '36rpx'
        }" :leftIconSize="22" bgColor="#ffffff" :leftIconColor="'#222'" fixed safeAreaInsetTop placeholder></u-navbar>

        <!-- 2. Tab切换 -->
        <view class="tab-switcher">
            <view class="tab-container">
                <view class="tab-item" :class="{ 'active': activeTab === 'toInvoice' }" @click="switchTab('toInvoice')">
                    去开票
                </view>
                <view class="tab-item" :class="{ 'active': activeTab === 'history' }" @click="switchTab('history')">
                    开票记录
                </view>
            </view>
        </view>

        <!-- 3. 可开票列表 (仅在 "去开票" tab下显示) -->
        <view class="order-list-container" v-if="activeTab === 'toInvoice'">
            <view class="order-card" v-for="(order, index) in orderList" :key="order.id"
                :class="{ 'selected': order.selected }" @click="toggleSelectOrder(order)">
                <view class="card-header">
                    {{ order.created_at }}
                </view>
                <view class="card-body">
                    <view class="checkbox-wrapper">
                        <!-- 自定义复选框 -->
                        <view class="custom-checkbox" :class="{ 'checked': order.selected }"></view>
                    </view>
                    <view class="order-details">
                        <view class="order-title">{{ order.product_name }}</view>
                        <view class="order-number">订单号：{{ order.order_id }}</view>
                    </view>
                    <view class="order-amount">¥ {{ order.amount }}元</view>
                </view>
            </view>
        </view>

        <!-- 开票记录的占位内容 -->
        <view class="empty-placeholder" v-if="activeTab === 'history'">
            <view class="history-card" v-for="item in invoiceHistory" :key="item.id" style="white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        " @click="goToInvoiceDetail(item)">
                <view class="card-header">
                    {{ item.create_time }}
                </view>
                <view class="card-body">
                    <view class="invoice-details" style="width: 100%;overflow: hidden; text-overflow: ellipsis;">
                        <view class="info-line order-number"
                            style="width: 100%;overflow: hidden; text-overflow: ellipsis;width: 100%;">开票主体：{{
                            item.company_name }}</view>
                        <view class="info-line" style="width: 100%;">发票抬头：{{ item.type ==1?'个人':'企业' }}</view>
                        <view class="info-line" style="width: 100%;">开票金额：<text class="amount">¥ {{ item.price }}</text>
                        </view>
                    </view>
                    <view class="download-button">{{ item.status ==0?'待开票':'已开票' }}</view>
                </view>
            </view>
        </view>


        <!-- 4. 底部固定操作栏 -->
        <view class="footer-bar">
            <view class="select-all" @click="toggleSelectAll">
                <view class="custom-checkbox" :class="{ 'checked': isAllSelected }"></view>
                <text>全选</text>
            </view>
            <view class="total-info">
                <text>共：</text>
                <text class="total-amount">¥ {{ (totalAmount || 0).toFixed(2) }}</text>
            </view>
            <button class="apply-button" @click="applyForInvoice">去申请</button>
        </view>

    </view>
</template>

<script>
import { invoice } from "@/utils/api"
export default {
    data() {
        return {
            // 当前激活的tab
            activeTab: 'toInvoice',
            // 可开票订单列表
            orderList: [],
            invoiceHistory: []
        };
    },
    computed: {
        // 计算总金额
        totalAmount() {
            const total = this.orderList.reduce((sum, order) => {
                // 确保 amount 是数字类型
                const amount = parseFloat(order.amount) || 0;
                return sum + (order.selected ? amount : 0);
            }, 0);

            // 确保返回的是数字类型
            return Number(total) || 0;
        },
        // 判断是否已全选
        isAllSelected() {
            // 如果列表为空，则不为全选状态
            if (this.orderList.length === 0) return false;
            // every方法测试一个数组内的所有元素是否都能通过某个指定函数的测试
            return this.orderList.every(order => order.selected);
        }
    },
    async onShow() {
        this.invoicing()

        this.record()
    },
    methods: {
        goToInvoiceDetail(item) {

            const itemStr = encodeURIComponent(JSON.stringify(item));
            uni.navigateTo({
                url: '/pages/user/function/invoicingDetail?item=' + itemStr
            });
        },
        async invoicing() {
            const res = await invoice.getOrderList()
            if (res.code === 200) {
                console.log(res.data.list)
                // 为每个订单添加 selected 属性，默认为 false，并确保 amount 是数字类型
                this.orderList = res.data.list.map(order => ({
                    ...order,
                    selected: false,
                    amount: parseFloat(order.amount) || 0
                }));
                console.log(this.orderList)
                uni.hideLoading();
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }
        },
        async record() {
            const res = await invoice.getInvoiceList()
            console.log(res, '***')
            if (res.code === 200) {
                console.log(res.data)
                this.invoiceHistory = res.data
                uni.hideLoading()
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }
        },
        // 返回上一页
        navigateBack() {
            uni.navigateBack();
        },
        // 切换Tab
        switchTab(tabName) {
            this.activeTab = tabName;
        },
        // 选中/取消选中单个订单
        toggleSelectOrder(order) {
            if (!order || !order.hasOwnProperty('selected')) {
                console.log('订单对象无效');
                return;
            }

            order.selected = !order.selected;

            // 输出选中的订单ID和状态
            console.log('订单ID:', order.order_id, '选中状态:', order.selected);

            // 获取所有选中的订单ID
            const selectedIds = this.orderList.filter(item => item.selected && item.id).map(item => item.id);
            console.log('当前选中的订单IDs:', selectedIds);

            // 输出当前总金额
            console.log('当前总金额:', this.totalAmount);
        },
        // 全选/取消全选
        toggleSelectAll() {
            const currentSelectState = this.isAllSelected;
            this.orderList.forEach(order => {
                order.selected = !currentSelectState;
            });

            // 输出全选后的状态
            const selectedIds = this.orderList.filter(item => item.selected).map(item => item.order_id);
            console.log('全选操作后，选中的订单IDs:', selectedIds);
            console.log('全选操作后，总金额:', this.totalAmount);
        },
        // 申请开票
        applyForInvoice() {
            const selectedOrders = this.orderList.filter(order => order.selected);
            if (selectedOrders.length === 0) {
                uni.showToast({
                    title: '请至少选择一个订单',
                    icon: 'none'
                });
                return;
            }

            // 获取选中订单的ID数组
            const selectedIds = selectedOrders.map(order => order.order_id);

            console.log('申请开票的订单:', selectedOrders);
            console.log('选中的订单IDs:', selectedIds);
            console.log('总金额:', this.totalAmount);

            // 跳转到申请页面，传递选中的订单ID和总金额
            const idsParam = selectedIds.join(',');
            const amountParam = this.totalAmount.toFixed(2);

            uni.navigateTo({
                url: `/pages/user/function/application?ids=${idsParam}&amount=${amountParam}&count=${selectedOrders.length}`
            });

            console.log('跳转参数:', {
                ids: idsParam,
                amount: amountParam,
                count: selectedOrders.length
            });
        },

        // 格式化金额显示
        formatAmount(amount) {
            const num = parseFloat(amount) || 0;
            return num.toFixed(2);
        }
    }
}
</script>

<style lang="scss">
// 页面整体样式
.invoice-page {
    background-color: #e6f7f2;
    min-height: 100vh;
    padding-bottom: 180rpx; // 为底部固定栏预留空间
}

// 1. 自定义导航栏
.custom-nav-bar {
    background-color: #e6f7f2;
    padding: 0 30rpx;
    // 适配状态栏高度
    padding-top: var(--status-bar-height);
    height: 90rpx; // 自定义导航栏内容高度
    display: flex;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 99;

    .nav-content {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .back-arrow {
            font-size: 40rpx;
            font-weight: bold;
            width: 100rpx;
        }

        .title {
            font-size: 34rpx;
            font-weight: bold;
            color: #333;
        }

        .right-link {
            font-size: 26rpx;
            color: #888;
            width: 150rpx;
            text-align: right;
        }
    }
}

// 2. Tab切换
.tab-switcher {
    display: flex;
    justify-content: center;
    padding: 20rpx 0;
    background-color: #e6f7f2;

    .tab-container {
        display: flex;
        background-color: #fff;
        border-radius: 40rpx;
        overflow: hidden;
        border: 1rpx solid #eee;

        .tab-item {
            padding: 16rpx 60rpx;
            font-size: 28rpx;
            color: #333;
            transition: all 0.3s ease;

            &.active {
                background-color: #333;
                color: #fff;
            }
        }
    }
}

// 3. 订单列表
.order-list-container {
    padding: 0 30rpx;

    .order-card {
        background-color: #fff;
        border-radius: 20rpx;
        padding: 30rpx;
        margin-top: 20rpx;
        border: 4rpx solid transparent; // 预留边框位置
        transition: border-color 0.2s;

        &.selected {
            border-color: #3c9cff;
        }

        .card-header {
            font-size: 28rpx;
            color: #666;
            margin-bottom: 20rpx;
        }

        .card-body {
            display: flex;
            align-items: center;

            .order-details {
                flex: 1;
                margin-left: 20rpx;

                .order-title {
                    font-size: 30rpx;
                    color: #333;
                    font-weight: 500;
                    margin-bottom: 10rpx;
                }

                .order-number {
                    font-size: 24rpx;
                    color: #999;
                }
            }

            .order-amount {
                font-size: 32rpx;
                color: #333;
                font-weight: bold;
            }
        }
    }
}

// 自定义复选框样式
.custom-checkbox {
    width: 36rpx;
    height: 36rpx;
    border-radius: 50%;
    border: 2rpx solid #ccc;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.2s;

    &.checked {
        background-color: #3c9cff;
        border-color: #3c9cff;

        // 用伪元素画内部的对勾
        &::after {
            content: '✓';
            color: #fff;
            font-size: 24rpx;
            font-weight: bold;
            line-height: 1;
        }
    }
}

// 空状态占位
.empty-placeholder {
    width: 95%;
    margin: 0 auto;

    // display: flex;
    // justify-content: center;
    // align-items: center;
    // padding-top: 200rpx;
    // color: #999;
    .history-card {
        background-color: #fff;
        border-radius: 16rpx;
        margin-bottom: 20rpx;
        padding: 24rpx;
        // 卡片顶部的浅绿色装饰线
        border-top: 6rpx solid #e6f7f2;

        .card-header {
            font-size: 28rpx;
            color: #333;
            margin-bottom: 20rpx;
            font-weight: 500;
        }

        .card-body {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            .invoice-details {
                .info-line {
                    font-size: 26rpx;
                    color: #666;
                    line-height: 1.6;

                    // max-width: 100%;
                    .amount {
                        color: #e54d42;
                        font-weight: bold;
                    }

                    // 订单号溢出隐藏样式
                    &.order-number {}
                }
            }

            .download-button {
               position: absolute;
                right: 30rpx;
                top: 20rpx;
                // width: 100rpx;
                // height: 50rpx;
                font-size: 25rpx;
                color: #52c41a;
                // 移除按钮默认边框
                &::after {
                    border: none;
                }
            }
        }
    }
}

// 4. 底部固定操作栏
.footer-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 120rpx;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30rpx;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    // 适配iPhone X等底部安全区域
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    .select-all {
        display: flex;
        align-items: center;
        font-size: 28rpx;
        color: #666;

        .custom-checkbox {
            margin-right: 16rpx;
        }
    }

    .total-info {
        font-size: 28rpx;

        .total-amount {
            font-size: 34rpx;
            font-weight: bold;
            color: #e54d42;
        }
    }

    .apply-button {
        margin: 0;
        padding: 0 60rpx;
        height: 80rpx;
        line-height: 80rpx;
        font-size: 30rpx;
        color: #fff;
        background-color: #e54d42;
        border-radius: 40rpx;

        &::after {
            border: none;
        }
    }
}
</style>