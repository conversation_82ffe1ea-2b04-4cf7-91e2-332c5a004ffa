// 在线客服功能测试脚本

// 测试数据验证
function validateSmartQuestions(smartQuestions) {
    console.log('=== 测试智能问题数据 ===');
    
    const categories = Object.keys(smartQuestions);
    console.log('问题分类:', categories);
    
    categories.forEach(category => {
        const questions = smartQuestions[category];
        console.log(`${category} 分类下有 ${questions.length} 个问题:`);
        questions.forEach((q, index) => {
            console.log(`  ${index + 1}. ${q.question}`);
        });
    });
    
    return categories.length > 0;
}

// 测试智能回答功能
function testSmartAnswers(smartAnswers, testQuestions) {
    console.log('\n=== 测试智能回答功能 ===');
    
    testQuestions.forEach(question => {
        const answer = smartAnswers[question] || '未找到匹配答案';
        console.log(`问题: ${question}`);
        console.log(`回答: ${answer}\n`);
    });
}

// 测试关键词匹配
function testKeywordMatching(question, keywords) {
    console.log('\n=== 测试关键词匹配 ===');
    
    for (const [keyword, answer] of Object.entries(keywords)) {
        if (question.includes(keyword)) {
            console.log(`问题 "${question}" 匹配关键词 "${keyword}"`);
            console.log(`回答: ${answer}`);
            return answer;
        }
    }
    
    console.log(`问题 "${question}" 未匹配任何关键词`);
    return '抱歉，团团暂时无法回答您的问题，您可以尝试换个问法，或者联系人工客服获得帮助。';
}

// 模拟页面数据
const mockData = {
    smartQuestions: {
        '推荐': [
            { id: 1, question: '未成年可以做兼职吗？' },
            { id: 2, question: '近期有兼职记录可以注销账户吗？' },
            { id: 3, question: '消息界面如何发图片？' },
            { id: 4, question: '如何做兼职' },
            { id: 5, question: '为何提示账户异常' }
        ],
        '找工作': [
            { id: 8, question: '如何搜索合适的工作？' },
            { id: 9, question: '投递简历后多久有回复？' },
            { id: 10, question: '面试需要准备什么？' }
        ]
    },
    
    smartAnswers: {
        '未成年可以做兼职吗？': '根据相关法律法规，16周岁以上的未成年人可以从事一些轻体力劳动，但需要监护人同意，并且不能影响学业。',
        '近期有兼职记录可以注销账户吗？': '有兼职记录的账户可以申请注销，但需要先处理完所有未完成的工作订单和结算事宜。'
    },
    
    keywords: {
        '兼职': '您可以在首页浏览各种兼职岗位，选择适合自己的工作。如需帮助，请联系客服。',
        '账户': '关于账户问题，建议您检查个人信息是否完整，如有疑问请联系客服处理。',
        '工作': '我们提供多种类型的工作机会，您可以根据自己的时间和技能选择合适的岗位。'
    }
};

// 运行测试
function runTests() {
    console.log('🚀 开始测试在线客服功能...\n');
    
    // 测试1: 验证问题数据
    const isValidData = validateSmartQuestions(mockData.smartQuestions);
    console.log(`数据验证结果: ${isValidData ? '✅ 通过' : '❌ 失败'}`);
    
    // 测试2: 测试智能回答
    const testQuestions = [
        '未成年可以做兼职吗？',
        '近期有兼职记录可以注销账户吗？',
        '这是一个未知问题'
    ];
    testSmartAnswers(mockData.smartAnswers, testQuestions);
    
    // 测试3: 测试关键词匹配
    const testKeywordQuestions = [
        '我想找兼职工作',
        '账户登录不了',
        '有什么工作推荐吗',
        '这是完全不相关的问题'
    ];
    
    testKeywordQuestions.forEach(question => {
        testKeywordMatching(question, mockData.keywords);
    });
    
    console.log('\n✅ 测试完成！');
}

// 页面功能测试
function testPageFunctions() {
    console.log('\n=== 测试页面功能 ===');
    
    // 模拟分页功能
    function testPagination(questions, pageSize = 5, offset = 0) {
        const result = questions.slice(offset, offset + pageSize);
        console.log(`分页测试 - 偏移量: ${offset}, 页面大小: ${pageSize}`);
        console.log('结果:', result.map(q => q.question));
        return result;
    }
    
    const recommendQuestions = mockData.smartQuestions['推荐'];
    testPagination(recommendQuestions, 3, 0);
    testPagination(recommendQuestions, 3, 3);
    
    // 模拟时间格式化
    function formatTime() {
        const now = new Date();
        const hours = now.getHours().toString().padStart(2, '0');
        const minutes = now.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
    }
    
    console.log('当前时间:', formatTime());
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
    window.onlineServiceTest = {
        runTests,
        testPageFunctions,
        mockData
    };
    
    console.log('测试工具已加载到 window.onlineServiceTest');
    console.log('使用 window.onlineServiceTest.runTests() 开始测试');
}

// 如果在 Node.js 环境中运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        runTests,
        testPageFunctions,
        mockData
    };
}

// 自动运行测试
runTests();
testPageFunctions();
