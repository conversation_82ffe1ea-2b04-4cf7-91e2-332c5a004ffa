// /**
//  * API接口管理
//  */
import request from "./request"

// 用户相关接口
export const userApi = {
  // 登录
  login: (data) => request.post("/login/login", data),
  // 获取验证码
  Captcha: (data) => request.post("/common/phoneSend", data),
  //重置密码
  resetPwd: (data) => request.post("/login/resetpwd", data),
  //充值密码校验验证码
  phoneCheck: (data) => request.post("/common/phoneCheck", data),
  //修改登陆手机号
  mobileEdit: (data) => request.post("/login/mobileEdit", data),
}
//选择职位接口
export const jobApi = {
  // 获取职位列表
  getJobList: () => request.get("/common/getJobType"),
}
//发布页接口
export const dictApi = {
  // 获取字典数据
  getDict: () => request.get("/common/getAllDictionary"),
  //获取用户信息
  getUserInfo: () => request.get("/login/getUserInfo"),
  //确认发布接口
  postjobInsert: (data) => request.post("/job/jobInsert", data),
}
// // 地图定位接口
export const homeApi = {
  getMap: (params) => request.get("/map/ws/place/v1/suggestion", params),
  getCityLists: (params) => request.get("/map/ws/location/v1/ip", params,),
  getcitylate: () => request.get("/common/getarea"),
  getSurroun: (params) => request.get("/map/ws/place/v1/search", params),

  //逆地址解析
  getGeocoder: (params) => request.get("/map/ws/geocoder/v1", params)

}
//企业认证接口
export const fileApi = {
  //上传接口
  postUpload: (params) => request.post("/common/upload", params),
  //获取行业字典
  getCompany: () => request.get("/common/getCompanyIndustry",),
  //修改公司基本信息企业认证
  getCompanySave: (params) => request.post("/company/companySave", params),
  //企业认证
  getCompanyAuth: (params) => request.post("/company/companyAuth", params),
}

//获取用户企业基本信息
export const priseApi = {
  getCompanyInfoe: () => request.get("/company/getCompanyInfo"),
  //获取用户基本信息
  getUserInfo: () => request.get("/login/getUserInfo"),
  //获取职位类型技能字典
  getJobTypeSkill: (params) => request.post("/common/getJobTypeSkill", params),
  //其他基础信息修改 
  userBasicEdit: (params) => request.post("/login/userBasicEdit", params),
}

//聊天相关接口
export const chatApi = {
  //获取聊天历史记录
  // getChatHistory: (params) => request.post("/chat/getChatHistory", params),
  // //发送消息
  // sendMessage: (params) => request.post("/chat/sendMessage", params),
  // //获取会话列表
  // getConversationList: () => request.get("/chat/getConversationList"),
  // //标记消息已读
  // markMessageRead: (params) => request.post("/chat/markMessageRead", params),
}

//获取企业职位列表
export const positionsApi = {
  postJobList: (params) => request.post("/job/jobList", params),
  //上架职位
  postJobStatus: (params) => request.post("/job/jobStatus", params),
  //获取详情
  postJobInfo: (params) => request.post("/job/getJobInfo", params),
  //新详情 /api/job/jobdetail
  postJobInfonew: (params) => request.post("/job/jobdetail", params),
  //修改接口
  postJobUpdate: (params) => request.post("/job/jobUpdate", params),
}

//已报名列表
export const applyApi = {
  //获取已报名候选人列表
  getJobApply: (params) => request.post("/talent/getJobApplyTalentList", params),
  //获取附近求职者列表
  getRecommend: (params) => request.post("/talent/getNearTalentList", params),
  //获取看过我求职者列表
  getJobView: (params) => request.post("/talent/getJobViewTalentList", params),

  //详情
  getTalent: (params) => request.post("/talent/getTalentResumeInfo", params),
  //修改已报名人才标记
  changeApply: (params) => request.post("/talent/changeApplyTalentStatus", params),
}

//已报名列表
export const realApi = {
  //获取实名认证
  getverify: (params) => request.post("/login/verifyIdentity", params),
  //更换名字
  usernameEdit: (params) => request.post("/login/usernameEdit", params),
  //修改微信号
  wechatEdit: (params) => request.post("/login/contactEdit", params),

  //解除绑定身份证号
  checkIdentity: (params) => request.post("/login/checkIdentity", params),
  //修改负责改为名称修改

  jobPositionEdit: (params) => request.post("/login/jobPositionEdit", params),
}

//聊天
export const chat = {
  chatList: (params) => request.post('/message/list', params),
  //聊天列表
  chatInfo: (params) => request.post('/message/info', params),
  //交换信息
  checkExchange: (params) => request.get('/message/checkExchange', params),
  //消息置顶
  pinmessage: (params) => request.post('/message/pinmessage', params),
  //更新已读状态 
  update: (params) => request.post('/message/update', params)
}

//获取主页列表
export const home = {
  //根据市code获取区列表
  getAreaByCityId: (params) => request.post('/common/getAreaByCityId', params),
  getRecommendTalentList: (params) => request.post('/talent/getRecommendTalentList', params),
  //收藏人才
  colletTalent: (params) => request.post('/talent/colletTalent', params),
  //收藏人才列表 
  getColletTalentList: (params) => request.post('/talent/getColletTalentList', params),
}

//增值模块
export const addApi = {
  activeInvite: (params) => request.post('/addservice/activeInvite', params),
  //在线畅聊套餐列表
  getOnlineChatPackage: (params) => request.post('/addservice/getOnlineChatPackage', params),
  //套餐购买  
  productBuy: (params) => request.post('/recharge/productBuy', params),
  //职位置顶服务套餐列表 
  getJobTopServicePackage: (params) => request.post('/addservice/getJobTopServicePackage', params),
  //电话直拨 
  getTelDirectPackage: (params) => request.post('/addservice/getTelDirectPackage', params),
  //急招套餐 
  getJobUrgentPackage: (params) => request.post('/addservice/getJobUrgentPackage', params),
  //职位刷新 
  getJobRefreshPackage: (params) => request.post('/addservice/getJobRefreshPackage', params),
  //用户积分余额明细
  userScoreList: (params) => request.post('/recharge/userScoreList', params),
  //积分列表 
  getPointsList: (params) => request.get('/addservice/getPointsList', params),
  //余额充值列表  
  getRechargeList: (params) => request.get('/recharge/getRechargeList', params),
  //获取用户剩余权益  
  getUserAddserviceNum: (params) => request.get('/addservice/getUserAddserviceNum', params),
  //积分充值 
  balanceRecharge: (params) => request.post('/recharge/balanceRecharge', params)
}
//系统相关参数
export const sysApi = {
  getSysConfig: (params) => request.post('/common/getSysConfig', params),
}
//反馈
export const rider = {
  feedback: (params) => request.post('/my/feedback', params),
  //反馈历史列表
  feedbackList: (params) => request.post('/my/feedbackList', params)
}
//发票
export const invoice = {
  //发票列表
  getOrderList: (params) => request.get('/invoice/getOrderList', params),
  //申请开票 
  applyInvoice: (params) => request.post('/invoice/applyInvoice', params), 
  //开票记录 
  getInvoiceList: (params) => request.get('/invoice/getInvoiceList', params), 
}

export const interview ={
  //面试列表
  interview:(params)=>request.post('/my/interview', params),
  //面试详情
  InterviewInfo:(params)=>request.get('/my/InterviewInfo', params),
  //面试签到
  CheckIn:(params)=>request.get('/my/CheckIn', params),
}
export const cancel = {
  //用户注销
  cancelOrder:(params)=>request.get('/login/accountLogOff', params),
}

export const recru ={
  jobDataCount:(params)=>request.get('/my/jobDataCount', params),
}
//简历展示
export const resume ={
  //简历展示
  ReumeAttachmentInfo:(params)=>request.get('/talent/ReumeAttachmentInfo', params),
}

// // 统一导出
// export default {
//   user: userApi,
//   home: homeApi,
//   discover: discoverApi,
//   message: messageApi,
// }
