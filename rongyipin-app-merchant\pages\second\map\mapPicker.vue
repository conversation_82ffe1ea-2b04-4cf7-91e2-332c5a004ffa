<template>
    <view class="map-picker-container">
        <!-- 导航栏 -->
        <u-navbar :autoBack="true" title="选择工作地址" :titleStyle="{
            color: '#222',
            fontWeight: 'bold',
            fontSize: '36rpx'
        }" :leftIconSize="32" :leftIconColor="'#222'" rightText bgColor="#fff" fixed safeAreaInsetTop placeholder>
            <template #right>
                <text class="confirm-btn" @click="confirmLocation">确定</text>
            </template>
        </u-navbar>

        <!-- 搜索框 - 使用cover-view在真机上显示 -->
        <view class="search-container">
            <view class="search-box">
                <view class="search-icon">🔍</view>
                <view class="search-input-wrapper">
                    <input class="search-input" type="text" placeholder="搜索地点" v-model="searchKeyword"
                        @input="onSearchInput" @confirm="searchLocation" />
                </view>
                <view v-if="searchKeyword" class="clear-btn" @tap="clearSearch">
                    <view class="clear-icon">✕</view>
                </view>
            </view>
        </view>

        <!-- 搜索结果列表 - 使用cover-view在真机上显示 -->
        <view v-if="showSearchResults" class="search-results">
            <scroll-view class="results-list" :scroll-y="true">
                <view v-for="(item, index) in searchResults" :key="index" class="result-item"
                    @tap="selectSearchResult(item)">
                    <view class="result-title">{{ item.title }}</view>
                    <view class="result-address">{{ item.address }}</view>
                </view>
            </scroll-view>
        </view>

        <!-- 地图容器 -->
        <view class="map-container" :class="{ 'with-search': showSearchResults }">
            <map id="map" class="map" :longitude="longitude" :latitude="latitude" :scale="scale" :markers="markers"
                @regionchange="onRegionChange" @tap="onMapTap" show-location enable-3D enable-overlooking enable-zoom
                enable-scroll enable-rotate enable-satellite></map>

            <!-- 中心点标记 -->
            <cover-view class="center-marker">
                <cover-view class="marker-icon">📍</cover-view>
            </cover-view>

            <!-- 定位按钮 - 使用cover-view在真机上显示 -->
            <cover-view class="location-btn" @tap="getCurrentLocation">

                <!-- 方案1: 使用本地图片 -->
                <cover-image @tap="getCurrentLocation" style="width: 80%;height: 80%;" class="location-icon"
                    src="@/static/app/authentication/positioni.png"></cover-image>

                <!-- 方案2: 使用CSS绘制的图标 -->
                <!-- <cover-view class="location-icon-css"></cover-view> -->

                <!-- 方案3: 使用Base64图标 -->
                <!-- <cover-image class="location-icon" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDOC4xMyAyIDUgNS4xMyA1IDlDNSAxNC4yNSAxMiAyMiAxMiAyMlMxOSAxNC4yNSAxOSA5QzE5IDUuMTMgMTUuODcgMiAxMiAyWk0xMiAxMS41QzEwLjYyIDExLjUgOS41IDEwLjM4IDkuNSA5UzEwLjYyIDYuNSAxMiA2LjVTMTQuNSA3LjYyIDE0LjUgOVMxMy4zOCAxMS41IDEyIDExLjVaIiBmaWxsPSIjMTBkMmMzIi8+Cjwvc3ZnPgo="></cover-image> -->

                <!-- 方案4: 使用Unicode图标 -->
                <!-- <cover-view class="location-icon-text">📍</cover-view> -->
            </cover-view>

            <cover-view class="address-card">
            <cover-view class="card-header">
                <cover-image @tap="getCurrentLocation" style="width: 50rpx;height: 50rpx;" class="location-icon"
                    src="@/static/app/authentication/positioni.png"></cover-image>
                <cover-view class="card-title">选中位置</cover-view>
            </cover-view>
            <cover-view class="address-info">
                <cover-view class="address-name">{{ selectedAddress.name || '获取地址中...' }}</cover-view>
                <cover-view class="address-detail">{{ selectedAddress.address || '' }}</cover-view>
            </cover-view>
        </cover-view>
        </view>

        <!-- 地址信息卡片 -->
        
    </view>
</template>

<script>
import { homeApi } from '@/utils/api';

export default {
    data() {
        return {
            // 地图相关
            longitude: 116.397128,
            latitude: 39.916527,
            scale: 16,
            markers: [],

            // 搜索相关
            searchKeyword: '',
            searchResults: [],
            showSearchResults: false,
            searchTimer: null,

            // 地址信息
            selectedAddress: {
                name: '',
                address: '',
                latitude: 0,
                longitude: 0,
                adcode: 0
            },

            // 腾讯地图key
            mapKey: 'QMEBZ-QAUKB-HWGUE-NCFVQ-SKKBH-ULBYV',

            // 定时器
            mapCenterTimer: null
        }
    },

    onLoad() {
        this.getCurrentLocation();
    },

    onShow() {
        // 页面显示时启动地图监听（真机备用方案）
        // #ifdef APP-PLUS
        console.log('页面显示，启动地图中心点监听');
        setTimeout(() => {
            this.startMapCenterMonitor();
        }, 2000); // 延迟2秒启动，确保地图加载完成
        // #endif
    },

    onHide() {
        // 页面隐藏时停止监听
        this.stopMapCenterMonitor();
    },

    onUnload() {
        // 页面卸载时清理资源
        this.stopMapCenterMonitor();
    },

    methods: {
        // 获取当前位置
        async getCurrentLocation() {
            uni.showLoading({
                title: '定位中...'
            });

            try {
                // #ifdef H5
                // H5环境：通过IP定位
                await this.getLocationByIP();
                // #endif

                // #ifdef APP-PLUS
                // App环境：使用GPS定位
                await this.getLocationByGPS();
                // #endif

            } catch (error) {
                console.error('定位失败:', error);
                this.useDefaultLocation();
            } finally {
                uni.hideLoading();
            }
        },

        // H5环境：IP定位
        async getLocationByIP() {
            try {
                const res = await homeApi.getCityLists({
                    key: this.mapKey,
                    // ip: '*************', // 可以指定IP进行测试
                });

                console.log('IP定位结果:', res);

                if (res && res.status === 0) {
                    this.latitude = res.result.location.lat;
                    this.longitude = res.result.location.lng;
                    console.log('IP定位成功:', this.latitude, this.longitude);
                    await this.reverseGeocode(this.latitude, this.longitude);
                } else {
                    throw new Error('IP定位失败');
                }
            } catch (error) {
                console.error('IP定位异常:', error);
                throw error;
            }
        },

        // App环境：GPS定位
        async getLocationByGPS() {
            return new Promise((resolve, reject) => {
                // 执行定位
                uni.getLocation({
                    type: 'gcj02', // 返回可以用于uni.openLocation的经纬度
                    geocode: true, // 解析地址信息
                    altitude: true, // 传入 true 会返回高度信息
                    highAccuracyExpireTime: 3000,
                    isHighAccuracy:true,
                    success: async (res) => {
                        console.log('GPS定位成功:', res);
                        this.latitude = res.latitude;
                        this.longitude = res.longitude;

                        // 如果系统返回了地址信息，直接使用
                        if (res.address) {
                            console.log('使用系统返回的详细地址:', res.address);
                            await this.reverseGeocode(this.latitude, this.longitude);
                            resolve();
                        } else {
                            uni.showToast({
                                title: '获取地址失败',
                                icon: 'none'
                            });
                        }
                    },
                    fail: (error) => {
                        console.error('GPS定位失败:', error);

                        // 如果GPS定位失败，尝试网络定位
                        this.getLocationByNetwork()
                            .then(resolve)
                            .catch(reject);
                    }
                });
            });
        },

        // App环境备用：网络定位
        async getLocationByNetwork() {
            return new Promise((resolve, reject) => {
                uni.request({
                    url: 'https://apis.map.qq.com/ws/location/v1/ip',
                    data: {
                        key: this.mapKey,
                    },
                    success: async (res) => {
                        console.log('网络定位结果:', res.data);

                        if (res.data && res.data.status === 0) {
                            this.latitude = res.data.result.location.lat;
                            this.longitude = res.data.result.location.lng;

                            try {
                                await this.reverseGeocode(this.latitude, this.longitude);
                                resolve();
                            } catch (error) {
                                reject(error);
                            }
                        } else {
                            reject(new Error('网络定位失败'));
                        }
                    },
                    fail: (error) => {
                        console.error('网络定位请求失败:', error);
                        reject(error);
                    }
                });
            });
        },

        // 使用默认位置（北京）
        useDefaultLocation() {
            console.log('使用默认位置');
            this.latitude = 39.916527;
            this.longitude = 116.397128;
            this.currentAddress = '北京市';
            this.currentCity = '北京市';

            uni.showToast({
                title: '定位失败，使用默认位置',
                icon: 'none'
            });

            // 尝试获取默认位置的详细地址
            this.reverseGeocode(this.latitude, this.longitude).catch(() => {
                console.log('默认位置地址解析失败');
            });
        },

        // 地图区域变化（拖拽时触发）
        onRegionChange(e) {
            console.log('onRegionChange 事件触发:', e);
            console.log('事件类型:', e.type);
            console.log('事件详情:', JSON.stringify(e));

            // 真机上可能需要监听不同的事件类型
            if (e.type === 'end' || e.type === 'drag') {
                console.log('开始获取地图中心点坐标...');

                // 添加延迟，确保地图渲染完成
                setTimeout(() => {
                    this.getCenterLocationWithRetry();
                }, 100);
            }
        },

        // 带重试机制的获取中心点坐标
        getCenterLocationWithRetry(retryCount = 0) {
            const maxRetries = 3;
            const mapContext = uni.createMapContext('map', this);

            console.log(`尝试获取中心点坐标，第 ${retryCount + 1} 次`);

            mapContext.getCenterLocation({
                success: (res) => {
                    console.log('获取中心点坐标成功:', res);
                    if (res.latitude && res.longitude) {
                        this.latitude = res.latitude;
                        this.longitude = res.longitude;
                        console.log('更新坐标:', this.latitude, this.longitude);
                        this.reverseGeocode(res.latitude, res.longitude);
                    } else {
                        console.warn('获取到的坐标为空');
                        if (retryCount < maxRetries) {
                            setTimeout(() => {
                                this.getCenterLocationWithRetry(retryCount + 1);
                            }, 200);
                        }
                    }
                },
                fail: (error) => {
                    console.error('获取中心点坐标失败:', error);
                    if (retryCount < maxRetries) {
                        console.log(`重试获取中心点坐标，第 ${retryCount + 2} 次`);
                        setTimeout(() => {
                            this.getCenterLocationWithRetry(retryCount + 1);
                        }, 500);
                    } else {
                        console.error('多次重试后仍然失败');
                        uni.showToast({
                            title: '获取位置失败',
                            icon: 'none'
                        });
                    }
                }
            });
        },

        // 逆地理编码（坐标转地址）
        async reverseGeocode(lat, lng) {
            try {
                uni.showLoading({
                    title: '获取地址中...'
                });
                // #ifdef H5
                const res = await homeApi.getGeocoder({
                    location: `${lat},${lng}`,
                    key: this.mapKey,
                    get_poi: 1
                });

                console.log('逆地址解析',res);

                if (res && res.status === 0) {
                    const result = res.result;
                    console.log(this.selectedAddress)
                    this.selectedAddress = {
                        name: result.formatted_addresses?.recommend || result.address,
                        address: result.address,
                        latitude: lat,
                        longitude: lng,
                        adcode: result.ad_info.adcode
                    };
                } else {
                    // 如果API失败，使用默认地址
                    this.selectedAddress = {
                        name: `位置 ${lat.toFixed(4)}, ${lng.toFixed(4)}`,
                        address: '详细地址获取中...',
                        latitude: lat,
                        longitude: lng
                    };
                }
                // #endif

                // #ifdef APP-PLUS
                // App环境：使用腾讯地图SDK进行逆地理编码
                uni.request({
                    url: 'https://apis.map.qq.com/ws/geocoder/v1',
                    data: {
                        location: `${lat},${lng}`,
                        key: this.mapKey,
                        get_poi: 1
                    },
                    methods: 'GET',
                    success: async (res) => {
                        console.log('网络定位结果:', res.data);

                        if (res) {
                            console.log('定位结果:', res);
                            const result = res.data.result;
                            console.log('定位结果11:', result);
                            this.selectedAddress = {
                                name: result.formatted_addresses?.recommend || result.address,
                                address: result.address,
                                latitude: lat,
                                longitude: lng,
                                adcode: result.ad_info.adcode
                            };
                            console.log('result',this.selectedAddress )
                        } else {
                            reject(new Error('网络定位失败'));
                        }
                    },
                    fail: (error) => {
                        console.error('网络定位请求失败:', error);
                        reject(error);
                    }
                });
                // #endif
            } catch (error) {
                console.error('逆地理编码失败:', error);
                // 设置默认地址
                this.selectedAddress = {
                    name: `位置 ${lat.toFixed(4)}, ${lng.toFixed(4)}`,
                    address: '地址获取失败，请重试',
                    latitude: lat,
                    longitude: lng
                };
                uni.showToast({
                    title: '获取地址失败',
                    icon: 'none'
                });
            } finally {
                uni.hideLoading();
            }
        },

        // 搜索输入
        onSearchInput() {
            if (this.searchTimer) {
                clearTimeout(this.searchTimer);
            }

            this.searchTimer = setTimeout(() => {
                if (this.searchKeyword.trim()) {
                    this.searchLocation();
                } else {
                    this.showSearchResults = false;
                }
            }, 500);
        },

        // 搜索地点
        async searchLocation() {
            if (!this.searchKeyword.trim()) return;
            // #ifdef H5
            try {
                const res = await homeApi.getMap({
                    key: this.mapKey,
                    keyword: this.searchKeyword,
                    region: '全国'
                });

                if (res.data && res.data.length > 0) {
                    this.searchResults = res.data;
                    this.showSearchResults = true;
                } else {
                    this.searchResults = [];
                    this.showSearchResults = false;
                }
                uni.hideLoading()
            } catch (error) {
                console.error('搜索失败:', error);
                uni.showToast({
                    title: '搜索失败',
                    icon: 'none'
                });
            }
            // #endif

            // #ifdef APP-PLUS
            uni.request({
                url: 'https://apis.map.qq.com/ws/place/v1/suggestion',
                data: {
                    key: this.mapKey,
                    keyword: this.searchKeyword,
                    region: '全国'
                },
                success: (res) => {
                    if (res.data && res.data.status === 0) {
                        this.searchResults = res.data.data;
                        this.showSearchResults = true;
                    } else {
                        this.searchResults = [];
                    }
                }
            })
            // #endif
        },

        // 选择搜索结果
        selectSearchResult(item) {
            this.latitude = item.location.lat;
            this.longitude = item.location.lng;
            console.log('选择定位',item )
            this.selectedAddress = {
                name: item.title,
                address: item.address,
                latitude: item.location.lat,
                longitude: item.location.lng,
                adcode: item.adcode
            };
            this.showSearchResults = false;
            this.searchKeyword = '';
        },

        // 清除搜索
        clearSearch() {
            this.searchKeyword = '';
            this.showSearchResults = false;
        },

        // 地图点击
        onMapTap(e) {
            console.log('地图点击事件:', e);
            // 可以在这里处理地图点击事件
        },

        // 地图更新事件
        onMapUpdated(e) {
            console.log('地图更新事件:', e);
        },

        // 锚点点击事件
        onAnchorPointTap(e) {
            console.log('锚点点击事件:', e);
        },

        // 启动地图中心点监听（备用方案）
        startMapCenterMonitor() {
            // 清除之前的定时器
            if (this.mapCenterTimer) {
                clearInterval(this.mapCenterTimer);
            }

            let lastLatitude = this.latitude;
            let lastLongitude = this.longitude;

            this.mapCenterTimer = setInterval(() => {
                const mapContext = uni.createMapContext('map', this);
                mapContext.getCenterLocation({
                    success: (res) => {
                        // 检查坐标是否发生变化
                        if (Math.abs(res.latitude - lastLatitude) > 0.0001 ||
                            Math.abs(res.longitude - lastLongitude) > 0.0001) {

                            console.log('检测到地图中心点变化:', res);
                            lastLatitude = res.latitude;
                            lastLongitude = res.longitude;

                            this.latitude = res.latitude;
                            this.longitude = res.longitude;
                            this.reverseGeocode(res.latitude, res.longitude);
                        }
                    },
                    fail: (error) => {
                        console.error('定时获取地图中心点失败:', error);
                    }
                });
            }, 1000); // 每秒检查一次
        },

        // 停止地图中心点监听
        stopMapCenterMonitor() {
            if (this.mapCenterTimer) {
                clearInterval(this.mapCenterTimer);
                this.mapCenterTimer = null;
            }
        },

        // 确认选择位置
        confirmLocation() {
            if (!this.selectedAddress.name) {
                uni.showToast({
                    title: '请选择一个位置',
                    icon: 'none'
                });
                return;
            }
            console.log(this.selectedAddress, '这是选择的地址');
            // 将选中的地址信息存储到store或传递给上一页
            this.$store.commit('setCity', this.selectedAddress);

            uni.navigateBack();
        }
    }
}
</script>

<style lang="scss" scoped>
.map-picker-container {
    height: 100vh;
    background-color: #fff;
    position: relative;
}

.confirm-btn {
    color: #10d2c3;
    font-size: 32rpx;
    font-weight: 500;
}

// 搜索容器
.search-container {
    // position: absolute;
    // top: 108rpx;
    // left: 0;
    // right: 0;
    z-index: 15;
    padding: 20rpx;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10rpx);
}

.search-box {
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 25rpx;
    padding: 0 30rpx;
    height: 80rpx;

    .search-input-wrapper {
        width: 90%;
        margin-left: 20rpx;
        font-size: 28rpx;
        color: #333;
    }

    .clear-btn {
        margin-left: 20rpx;
        padding: 10rpx;
    }
}

// 搜索结果
// 搜索结果
.search-results {
    // position: absolute;
    // top: 188rpx;
    // left: 20rpx;
    // right: 20rpx;
    height: 600rpx; /* 建议给一个固定高度，或者确保其有可计算的高度 */
    background-color: #fff;
    border-radius: 12rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
    z-index:9999;
    .results-list {
        /* 让 scroll-view 撑满父容器的高度 */
        height: 100%;
        width: 100%;
        // overflow: hidden;
        // overflow-y: auto;
    }

    .result-item {
        padding: 30rpx;
        border-bottom: 1rpx solid #f0f0f0;

        &:last-child {
            border-bottom: none;
        }

        .result-title {
            font-size: 30rpx;
            color: #333;
            font-weight: 500;
            margin-bottom: 10rpx;
        }

        .result-address {
            font-size: 26rpx;
            color: #666;
        }
    }
}

// 地图容器
.map-container {
    height: calc(100vh - 300rpx);
    position: relative;

    &.with-search {
        height: calc(100vh - 500rpx);
    }

    .map {
        width: 100%;
        height: 100%;
    }

    // 中心点标记
    .center-marker {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -100%);
        z-index: 10; // 提高z-index确保在最上层
        pointer-events: none; // 不阻止地图交互

        .marker-icon {
            font-size: 60rpx;
            color: #10d2c3;
            text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
            animation: bounce 2s infinite; // 添加跳动动画
        }
    }

    // 定位按钮
    .location-btn {
        position: absolute;
        right: 30rpx;
        bottom: 240rpx;
        width: 120rpx;
        height: 120rpx;
        background-color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
        z-index: 10;

        &:active {
            transform: scale(0.95);
        }

        // 图片图标样式
        .location-icon {
            width: 48rpx;
            height: 48rpx;
        }

        // CSS绘制的定位图标
        .location-icon-css {
            width: 32rpx;
            height: 32rpx;
            position: relative;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 24rpx;
                height: 24rpx;
                background-color: #10d2c3;
                border-radius: 50% 50% 50% 0;
                transform: translateX(-50%) rotate(-45deg);
                border: 4rpx solid #fff;
                box-shadow: 0 2rpx 8rpx rgba(16, 210, 195, 0.3);
            }

            &::after {
                content: '';
                position: absolute;
                top: 6rpx;
                left: 50%;
                transform: translateX(-50%);
                width: 12rpx;
                height: 12rpx;
                background-color: #fff;
                border-radius: 50%;
            }
        }

        // 文字图标样式
        .location-icon-text {
            font-size: 32rpx;
            color: #10d2c3;
        }
    }
}

// 地址信息卡片
.address-card {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    border-radius: 44rpx 44rpx 0 0;
    // padding: 40rpx 30rpx;
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
    z-index: 3000;
    height: 200rpx;
    width: 100%;

    .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        padding: 0rpx 30rpx;
        margin-top: 10rpx;

        .card-title {
            margin-left: 15rpx;
            font-size: 28rpx;
            color: #666;

        }
    }

    .address-info {
        padding: 0rpx 30rpx;

        .address-name {
            font-size: 32rpx;
            color: #333;
            font-weight: 500;
            margin-bottom: 10rpx;
        }

        .address-detail {
            font-size: 26rpx;
            color: #666;
            line-height: 1.4;
        }
    }
}

// 动画效果
@keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateY(0);
    }

    40% {
        transform: translateY(-10rpx);
    }

    60% {
        transform: translateY(-5rpx);
    }
}
</style>
