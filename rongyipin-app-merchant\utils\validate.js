/**
 * 表单验证工具类
 */

// 验证手机号
export const isPhone = (value) => {
    return /^1[3-9]\d{9}$/.test(value)
  }
  
  // 验证邮箱
  export const isEmail = (value) => {
    return /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value)
  }
  
  // 验证URL
  export const isUrl = (value) => {
    return /^(https?:\/\/)([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/.test(
      value,
    )
  }
  
  // 验证身份证
  export const isIdCard = (value) => {
    return /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(value)
  }
  
  // 验证密码（6-20位，包含字母和数字）
  export const isPassword = (value) => {
    return /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,20}$/.test(value)
  }
  
  // 验证是否为空
  export const isEmpty = (value) => {
    if (value === null || value === undefined || value === "") {
      return true
    }
    if (Array.isArray(value) && value.length === 0) {
      return true
    }
    if (typeof value === "object" && Object.keys(value).length === 0) {
      return true
    }
    return false
  }
  
  // 验证是否为数字
  export const isNumber = (value) => {
    return !isNaN(Number.parseFloat(value)) && isFinite(value)
  }
  
  // 验证是否为整数
  export const isInteger = (value) => {
    return Number.isInteger(Number(value))
  }
  
  // 验证是否为正整数
  export const isPositiveInteger = (value) => {
    return /^[1-9]\d*$/.test(value)
  }
  
  // 验证是否为中文
  export const isChinese = (value) => {
    return /^[\u4e00-\u9fa5]+$/.test(value)
  }
  
  export default {
    isPhone,
    isEmail,
    isUrl,
    isIdCard,
    isPassword,
    isEmpty,
    isNumber,
    isInteger,
    isPositiveInteger,
    isChinese,
  }
  