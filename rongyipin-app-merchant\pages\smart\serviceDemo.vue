<template>
    <view class="demo-page">
        <view class="header">
            <text class="title">在线客服演示</text>
        </view>
        
        <view class="demo-content">
            <view class="demo-item" @click="openOnlineService">
                <view class="demo-icon">💬</view>
                <view class="demo-info">
                    <text class="demo-title">在线咨询</text>
                    <text class="demo-desc">智能客服，快速解答您的问题</text>
                </view>
                <u-icon name="arrow-right" size="16" color="#999"></u-icon>
            </view>
            
            <view class="demo-item" @click="openSmartChat">
                <view class="demo-icon">🤖</view>
                <view class="demo-info">
                    <text class="demo-title">智能问答</text>
                    <text class="demo-desc">AI助手，提供专业建议</text>
                </view>
                <u-icon name="arrow-right" size="16" color="#999"></u-icon>
            </view>
        </view>
        
        <view class="features">
            <text class="features-title">功能特色</text>
            <view class="feature-list">
                <view class="feature-item">
                    <text class="feature-icon">⚡</text>
                    <text class="feature-text">快速响应，实时解答</text>
                </view>
                <view class="feature-item">
                    <text class="feature-icon">🎯</text>
                    <text class="feature-text">智能推荐，精准匹配</text>
                </view>
                <view class="feature-item">
                    <text class="feature-icon">📱</text>
                    <text class="feature-text">多媒体支持，交互丰富</text>
                </view>
                <view class="feature-item">
                    <text class="feature-icon">🔒</text>
                    <text class="feature-text">安全可靠，隐私保护</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    methods: {
        openOnlineService() {
            uni.navigateTo({
                url: '/pages/smart/onlineService'
            });
        },
        
        openSmartChat() {
            uni.navigateTo({
                url: '/pages/smart/chat'
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.demo-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 40rpx;
}

.header {
    text-align: center;
    margin-bottom: 60rpx;
    
    .title {
        color: white;
        font-size: 48rpx;
        font-weight: 600;
    }
}

.demo-content {
    margin-bottom: 60rpx;
}

.demo-item {
    display: flex;
    align-items: center;
    background-color: white;
    border-radius: 20rpx;
    padding: 40rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
    
    &:active {
        transform: scale(0.98);
    }
}

.demo-icon {
    font-size: 60rpx;
    margin-right: 30rpx;
}

.demo-info {
    flex: 1;
    
    .demo-title {
        display: block;
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 10rpx;
    }
    
    .demo-desc {
        font-size: 26rpx;
        color: #666;
    }
}

.features {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 20rpx;
    padding: 40rpx;
    
    .features-title {
        display: block;
        color: white;
        font-size: 32rpx;
        font-weight: 600;
        margin-bottom: 30rpx;
        text-align: center;
    }
}

.feature-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30rpx;
}

.feature-item {
    display: flex;
    align-items: center;
    
    .feature-icon {
        font-size: 40rpx;
        margin-right: 20rpx;
    }
    
    .feature-text {
        color: white;
        font-size: 26rpx;
        opacity: 0.9;
    }
}
</style>
