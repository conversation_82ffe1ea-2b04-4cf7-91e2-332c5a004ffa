<template>
    <view class="name-edit-container">
        <!-- 导航栏 -->
        <view class="navbar">
            <view class="nav-left" @click="goBack">
                <text class="nav-icon">‹</text>
            </view>
            <view class="nav-title">
                <text class="title-text">姓名</text>
            </view>
            <view class="nav-right" @click="saveName">
                <text class="save-text">保存</text>
            </view>
        </view>

        <!-- 内容区域 -->
        <view class="content">
            <!-- 输入区域 -->
            <view class="input-section">
                <view class="input-label">
                    <text class="label-text">对外显示名称</text>
                </view>
                <view class="input-wrapper">
                    <input class="name-input" type="text" v-model="displayName" placeholder="请输入姓名" :focus="true" />
                </view>
            </view>

            <!-- 底部按钮 -->
            <view class="bottom-section">
                <view class="change-real-name-btn" @click="changeRealName">
                    <text class="btn-text">我要更换实名绑定</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { realApi } from "@/utils/api"
export default {
    data() {
        return {
            displayName: '', // 显示名称
            originalName: '' // 原始名称，用于比较是否有变化
        }
    },
    onLoad() {
        // 获取用户信息
        const userInfo = uni.getStorageSync('usinfo');
        if (userInfo && userInfo.username) {
            this.displayName = userInfo.username;
            this.originalName = userInfo.username;
        }
    },
    methods: {
        // 返回上一页
        goBack() {
            // 检查是否有未保存的更改
            if (this.displayName !== this.originalName) {
                uni.showModal({
                    title: '提示',
                    content: '您有未保存的更改，确定要离开吗？',
                    success: (res) => {
                        if (res.confirm) {
                            uni.navigateBack();
                        }
                    }
                });
            } else {
                uni.navigateBack();
            }
        },

        // 保存姓名
        async saveName() {
            if (!this.displayName.trim()) {
                uni.showToast({
                    title: '请输入姓名',
                    icon: 'none'
                });
                return;
            }

            // 更新本地存储的用户信息


            // 这里可以添加调用API更新服务器数据的逻辑
            // 例如：this.updateNameToServer(this.displayName);
            const res = await realApi.usernameEdit({ username: this.displayName.trim() })
            if (res.code == 200) {
                let userInfo = uni.getStorageSync('usinfo') || {};
                userInfo.username = this.displayName.trim();
                uni.setStorageSync('usinfo', userInfo);
                uni.showToast({
                    title: '保存成功',
                    icon: 'success'
                });

                // 延迟返回上一页
                setTimeout(() => {
                    uni.navigateBack();
                }, 1500);
            } else {
                uni.showToast({
                    title: res.msg,
                    // icon: 'success'
                });
            }



        },

        // 更换实名绑定
        changeRealName() {
            uni.showModal({
                title: '更换实名绑定',
                content: '更换实名绑定需要重新进行身份验证，是否继续？',
                success: (res) => {
                    if (res.confirm) {
                        // 跳转到实名认证页面
                        uni.navigateTo({
                            url: './changeReal/emitRealname'
                        });
                    }
                }
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.name-edit-container {
    min-height: 100vh;
    background: #f8f9fa;
}

/* 导航栏样式 */
.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    padding: 0 30rpx;
    background: #ffffff;
    border-bottom: 1rpx solid #f0f0f0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-left,
.nav-right {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-icon {
    font-size: 40rpx;
    color: #333333;
    font-weight: 500;
}

.nav-title {
    flex: 1;
    text-align: center;
}

.title-text {
    font-size: 32rpx;
    color: #333333;
    font-weight: 500;
}

.save-text {
    font-size: 28rpx;
    color: #4ECDC4;
    font-weight: 500;
}

/* 内容区域 */
.content {
    flex: 1;
    padding: 40rpx 30rpx;
}

/* 输入区域 */
.input-section {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 40rpx 30rpx;
    margin-bottom: 40rpx;
}

.input-label {
    margin-bottom: 20rpx;
}

.label-text {
    font-size: 28rpx;
    color: #666666;
    line-height: 40rpx;
}

.input-wrapper {
    border-bottom: 1rpx solid #f0f0f0;
    padding-bottom: 20rpx;
}

.name-input {
    width: 100%;
    font-size: 32rpx;
    color: #333333;
    line-height: 44rpx;
    padding: 0;
    border: none;
    outline: none;
    background: transparent;
}

.name-input::placeholder {
    color: #cccccc;
}

/* 底部区域 */
.bottom-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 40rpx 30rpx;
    padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
    background: #f8f9fa;
}

.change-real-name-btn {
    width: 100%;
    height: 88rpx;
    background: #4ECDC4;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(78, 205, 196, 0.3);
}

.btn-text {
    font-size: 32rpx;
    color: #ffffff;
    font-weight: 500;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
    .navbar {
        height: 80rpx;
        padding: 0 24rpx;
    }

    .content {
        padding: 32rpx 24rpx;
    }

    .input-section {
        padding: 32rpx 24rpx;
    }

    .bottom-section {
        padding: 32rpx 24rpx;
        padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
    }
}
</style>