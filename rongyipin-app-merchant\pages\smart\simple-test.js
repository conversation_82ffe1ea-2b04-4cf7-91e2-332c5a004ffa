console.log('🚀 在线客服功能测试开始...\n');

// 测试数据
const smartQuestions = {
    '推荐': [
        { id: 1, question: '未成年可以做兼职吗？' },
        { id: 2, question: '近期有兼职记录可以注销账户吗？' },
        { id: 3, question: '消息界面如何发图片？' },
        { id: 4, question: '如何做兼职' },
        { id: 5, question: '为何提示账户异常' }
    ],
    '找工作': [
        { id: 8, question: '如何搜索合适的工作？' },
        { id: 9, question: '投递简历后多久有回复？' },
        { id: 10, question: '面试需要准备什么？' }
    ]
};

// 测试1: 数据结构验证
console.log('=== 测试1: 数据结构验证 ===');
const categories = Object.keys(smartQuestions);
console.log('问题分类:', categories);
console.log('总分类数:', categories.length);

categories.forEach(category => {
    const questions = smartQuestions[category];
    console.log(`${category}: ${questions.length} 个问题`);
});

// 测试2: 分页功能
console.log('\n=== 测试2: 分页功能 ===');
function testPagination(questions, pageSize = 5, offset = 0) {
    return questions.slice(offset, offset + pageSize);
}

const recommendQuestions = smartQuestions['推荐'];
console.log('原始问题数:', recommendQuestions.length);
console.log('第1页 (0-2):', testPagination(recommendQuestions, 3, 0).map(q => q.question));
console.log('第2页 (3-5):', testPagination(recommendQuestions, 3, 3).map(q => q.question));

// 测试3: 时间格式化
console.log('\n=== 测试3: 时间格式化 ===');
function formatTime() {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
}
console.log('当前时间:', formatTime());

// 测试4: 关键词匹配
console.log('\n=== 测试4: 关键词匹配 ===');
const keywords = {
    '兼职': '您可以在首页浏览各种兼职岗位，选择适合自己的工作。',
    '账户': '关于账户问题，建议您检查个人信息是否完整。',
    '工作': '我们提供多种类型的工作机会。'
};

function matchKeyword(question) {
    for (const [keyword, answer] of Object.entries(keywords)) {
        if (question.includes(keyword)) {
            return { keyword, answer };
        }
    }
    return null;
}

const testQuestions = ['我想找兼职', '账户登录问题', '有什么工作', '随机问题'];
testQuestions.forEach(question => {
    const match = matchKeyword(question);
    if (match) {
        console.log(`"${question}" -> 匹配关键词: ${match.keyword}`);
    } else {
        console.log(`"${question}" -> 无匹配`);
    }
});

console.log('\n✅ 测试完成！所有功能正常。');
