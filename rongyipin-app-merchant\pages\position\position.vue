<template>
	<view class="job-management-page">
		<!-- 顶部导航栏 -->
		<view class="custom-navbar">
			<view class="navbar-content">
				<view class="navbar-title">职位管理</view>
			</view>
		</view>

		<!-- 状态标签栏 -->
		<view class="status-tabs">
			<view class="status-tab" v-for="(tab, index) in statusTabs" :key="index"
				:class="{ 'active': selectedStatusIndex === index }" @click="switchStatusTab(index)">
				{{ tab.label }}
			</view>
		</view>

		<!-- 职位列表 -->

		<!-- 职位列表容器 -->
		<scroll-view class="job-list-container" scroll-y="true" :refresher-enabled="true"
			:refresher-triggered="refreshing" @refresherrefresh="onRefresh" @scrolltolower="onLoadMore"
			:lower-threshold="100">

			<view class="job-list">
				<view class="job-item" v-for="(item, index) in positionList" :key="index" @click="gotodetail(item)">
					<view class="job-header">
						<view class="job-title-section">
							<text class="job-title">{{ item.name }}</text>

						</view>
						<view class="job-action">
							<view class="job-status-badge" :class="getStatusClass(item.merge_status)">
								{{ getStatusText(item.merge_status) }}
							</view>
						</view>
					</view>
					<view class="job-info">
						<text class="job-type">{{ item.typename }}</text>
						<text class="job-meta">{{ formatDate(item.createtime) }}</text>
					</view>
				</view>

			</view>

			<!-- 加载状态提示 -->
			<view class="loading-status">
				<view v-if="loading && positionList.length > 0 && !refreshing" class="loading-more">
					<text class="loading-text">加载中...</text>
				</view>
				<view v-else-if="!hasMore && positionList.length > 0 && !loading" class="no-more">
					<text>- 没有更多了 -</text>
				</view>
				<view v-else-if="positionList.length === 0 && !loading && !refreshing" class="empty-data">
					<text class="empty-text">暂无职位数据</text>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import { positionsApi, priseApi } from "@/utils/api"
export default {
	data() {
		return {
			positionList: [],
			// 状态标签数据
			statusTabs: [
				{ label: '全部', value: 100 },
				{ label: '开放中', value: 1 },
				{ label: '审核失败', value: 3 },
				{ label: '已关闭', value: 0 }
			],
			selectedStatusIndex: 0,
			// 保留原有的弹窗选择数据（如果需要的话）
			popselect: [
				{ lable: '全部', value: 100 },
				{ lable: '已下线', value: 0 },
				{ lable: '招聘中', value: 1 },
				{ lable: '待审核', value: 2 },
				{ lable: '审核驳回', value: 3 }
			],
			hasMore: true, // 是否有更多数据
			loading: false, // 是否正在加载
			refreshing: false, // 是否正在刷新
			scrollViewHeight: 600, // scroll-view高度
			positions: {
				lable: '全部',
				value: 100
			},
			positionType: {
				lable: '全职',
				value: 1
			}
			, // 审核状态切换
			page: 1,
			pagesize: 10,
			total: 0,
			type: 1,
			listName: '',
			tabslecd: [],
			morelate: false,
			activePopupId: null,
			userInfo: false
		}
	},
	async onShow() {
		this.initData()	
	},
	async onLoad() {
		this.calculateScrollViewHeight()
		this.initData()
	},
	async mounted() {
		this.calculateScrollViewHeight()
	},
	onReady() {
		this.calculateScrollViewHeight()
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 切换状态标签
		switchStatusTab(index) {
			this.selectedStatusIndex = index;
			this.page = 1;
			this.hasMore = true;
			this.positionList = [];
			this.getList();
		},

		// 获取状态样式类
		getStatusClass(status) {
			switch (status) {
				case 1: return 'status-active';
				case 0: return 'status-pending';
				case 2: return 'status-review';
				case 3: return 'status-rejected';
				default: return 'status-default';
			}
		},

		// 获取状态文本
		getStatusText(status) {
			switch (status) {
				case 1: return '开放中';
				case 0: return '已下架';
				case 2: return '待审核';
				case 3: return '审核驳回';
				default: return '';
			}
		},
		// 获取操作样式类
		getActionClass(status) {
			switch (status) {
				case 1: return 'action-active';
				case 0: return 'action-pending';
				case 2: return 'action-review';
				case 3: return 'action-rejected';
				default: return 'action-default';
			}
		},

		// 发送页面
		lookme(item) {
			uni.setStorageSync('targetTab', 2);
			uni.setStorageSync('itemData', {
				id: item.id,
				name: item.name
			});
			uni.switchTab({
				url: `/pages/index/index`
			});
		},
		// 计算scroll-view高度
		calculateScrollViewHeight() {
			const systemInfo = uni.getSystemInfoSync()
			const windowHeight = systemInfo.windowHeight
			// 减去导航栏、筛选栏、提示栏等高度
			this.scrollViewHeight = windowHeight - 280 // 根据实际情况调整
		},

		// 初始化数据
		async initData() {
			this.page = 1
			this.hasMore = true
			this.positionList = []
			await this.getList()
		},

		// 下拉刷新
		async onRefresh() {
			console.log('下拉刷新')
			this.refreshing = true
			this.page = 1
			this.hasMore = true

			try {
				await this.getList(true) // 传入true表示是刷新操作
				uni.showToast({
					title: '刷新成功',
					icon: 'success',
					duration: 1500
				})
			} catch (error) {
				uni.showToast({
					title: '刷新失败',
					icon: 'none',
					duration: 1500
				})
			} finally {
				this.refreshing = false
			}
		},

		// 上拉加载更多
		async onLoadMore() {
			console.log('上拉加载更多')
			if (this.loading || !this.hasMore) {
				return
			}

			this.loading = true
			this.page++

			try {
				await this.getList(false) // 传入false表示是加载更多操作
			} catch (error) {
				this.page-- // 加载失败时回退页码
				uni.showToast({
					title: '加载失败',
					icon: 'none',
					duration: 1500
				})
			} finally {
				this.loading = false
			}
		},

		serchdata() {
			uni.navigateTo({
				url: '/pages/position/jobserch'
			});
		},
		gotodetail(item) {
			uni.navigateTo({
				url: `/pages/position/viewDetails?type=${item.type}&job_id=${item.id}`
			});
		},
		togglePopup(id) {
			// 如果点击的弹窗已经显示，则关闭；否则显示对应的弹窗
			this.activePopupId = this.activePopupId === id ? null : id;
		},
		//跳转详情
		emitdetail(item) {

			uni.navigateTo({
				url: `/pages/position/detail?type=${item.type}&job_id=${item.id}` // 将序列化后的对象作为参数传递
			});
		},
		//职位删除
		async deletionPosts(value) {
			const params = {
				status: 2,
				job_id: value.id
			}
			let res = await positionsApi.postJobStatus(params)
			if (res.code == 200) {
				this.getList()
			} else {
				uni.showToast({
					title: res.msg,
					icon: 'none'
				});
			}
		},
		//职位下架
		async suspended(value) {
			const params = {
				status: 0,
				job_id: value.id
			}
			let res = await positionsApi.postJobStatus(params)
			if (res.code == 200) {
				this.getList()
			} else {
				uni.showToast({
					title: res.msg,
					icon: 'none'
				});
			}
		},
		//职位上架
		async startHiring(value) {
			const params = {
				status: 1,
				job_id: value.id
			}
			let res = await positionsApi.postJobStatus(params)
			if (res.code == 200) {
				this.getList()
				uni.hideLoading()
			} else {
				uni.showToast({
					title: res.msg,
					icon: 'none'
				});
				uni.hideLoading()
			}
		},
		formatDate(datetime) {
			const date = new Date(datetime * 1000); // 转为毫秒

			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hour = String(date.getHours()).padStart(2, '0');
			const minute = String(date.getMinutes()).padStart(2, '0');
			const second = String(date.getSeconds()).padStart(2, '0');
			return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
		},
		async getList(isRefresh = false) {
			// 只在首次加载时显示全屏loading
			if (this.page === 1 && !isRefresh) {
				uni.showLoading({
					title: '加载中'
				});
			}

			const params = {
				// type: this.type,
				page: this.page,
				size: this.pagesize,
				status: this.statusTabs[this.selectedStatusIndex].value
			}

			try {
				let res = await positionsApi.postJobList(params)

				if (res.code == 200) {
					const newList = Array.isArray(res.data.data) ? res.data.data : [];
					console.log('这是列表', newList)
					this.total = res.data.total || 0;

					if (isRefresh || this.page === 1) {
						// 刷新或首次加载，替换数据
						this.positionList = newList;
					} else {
						// 加载更多，追加数据
						this.positionList = [...this.positionList, ...newList];
					}

					// 判断是否还有更多数据
					const totalLoaded = this.positionList.length;
					this.hasMore = totalLoaded < this.total;

					console.log(`职位列表加载完成: 当前${totalLoaded}条，总共${this.total}条，还有更多: ${this.hasMore}`);
					console.log(`loading状态: ${this.loading}, refreshing状态: ${this.refreshing}`);

					// 确保隐藏全屏loading
					if (this.page === 1 && !isRefresh) {
						uni.hideLoading();
					}
					uni.hideLoading();
				} else {
					uni.showToast({
						title: res.msg || '获取数据失败',
						icon: 'none'
					});
					// 确保隐藏全屏loading
					if (this.page === 1 && !isRefresh) {
						uni.hideLoading();
					}
					throw new Error(res.msg || '获取数据失败');
				}
			} catch (err) {
				console.error('获取职位列表失败:', err);
				uni.showToast({
					title: '数据加载失败，请稍后重试',
					icon: 'none'
				});
				// 确保隐藏全屏loading
				if (this.page === 1 && !isRefresh) {
					uni.hideLoading();
				}
				throw err;
			} finally {
				// 确保loading状态被重置
				console.log('getList执行完成，重置loading状态');
			}
		},
		open() {
			// console.log('open');
		},
		close() {
			this.show = false
			// console.log('close');
		},
		navigateToFaq(type) {
			// 处理常见问题导航
			console.log('Navigate to FAQ:', type)
		},
		async navigateToPublish() {
			let res = await priseApi.getCompanyInfoe()
			uni.hideLoading()
			if (res.data.is_auth == 0) {
				uni.navigateTo({
					url: '/pages/audits/index'
				})
			} else if (res.data.is_auth == 2) {

			} else {
				uni.navigateTo({
					url: '/pages/second/second'
				})
			}
			// uni.navigateTo({
			// 	url: '../second/second'
			// });
		}
	}
}
</script>

<style lang="scss" scoped>
.job-management-page {
	min-height: 100vh;
	background: #f8f9fa;
	font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 顶部导航栏 */
.custom-navbar {
	// background: linear-gradient(135deg, #1CBBB4 0%, #0081ff 100%);
	position: relative;
	z-index: 999;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
	color: black;
	.navbar-content {
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 32rpx;
		
		.navbar-title {
			font-size: 36rpx;
			// font-weight: 600;
			color:black;
			text-align: center;
			text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
		}

		.navbar-right {
			.settings-btn {
				width: 48rpx;
				height: 48rpx;
				border-radius: 50%;
				background: rgba(255, 255, 255, 0.2);
				padding: 8rpx;
				backdrop-filter: blur(4px);
			}
		}
	}
}

/* 状态标签栏 */
.status-tabs {
	display: flex;
	background: #ffffff;
	border-bottom: 1rpx solid #e9ecef;
	padding: 0 30rpx;

	.status-tab {
		flex: 1;
		text-align: center;
		padding: 30rpx 0;
		font-size: 28rpx;
		color: #666666;
		position: relative;
		transition: all 0.3s ease;

		&.active {
			color: #333333;
			font-weight: 600;

			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 40rpx;
				height: 4rpx;
				background: #007AFF;
				border-radius: 2rpx;
			}
		}
	}
}

/* 职位列表容器 */
.job-list-container {
	flex: 1;
	height: calc(100vh - 176rpx);
	background: #f8f9fa;
}

.job-list {
	padding: 20rpx 30rpx;
}

/* 职位项样式 */
.job-item {
	background: #ffffff;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}
}

.job-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
}

.job-title-section {
	display: flex;
	align-items: center;
	flex: 1;
	min-width: 0;
}

.job-title {
	font-size: 32rpx;
	color: #333333;
	font-weight: 600;
	margin-right: 16rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	max-width: 300rpx;
}

.job-status-badge {
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
	font-weight: 500;
	flex-shrink: 0;

	&.status-active {
		background: #e8f5e8;
		color: #52c41a;
	}

	&.status-pending {
		background: #fff7e6;
		color: #fa8c16;
	}

	&.status-review {
		background: #e6f7ff;
		color: #1890ff;
	}

	&.status-rejected {
		background: #fff2f0;
		color: #ff4d4f;
	}

	&.status-default {
		background: #f5f5f5;
		color: #999999;
	}
}

.job-action {
	flex-shrink: 0;
}

.action-text {
	font-size: 26rpx;
	font-weight: 500;

	&.action-active {
		color: #52c41a;
	}

	&.action-pending {
		color: #fa8c16;
	}

	&.action-review {
		color: #1890ff;
	}

	&.action-rejected {
		color: #ff4d4f;
	}

	&.action-default {
		color: #999999;
	}
}

.job-info {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.job-type {
	font-size: 24rpx;
	color: #666666;
}

.job-meta {
	font-size: 24rpx;
	color: #999999;
}

/* 加载状态样式 */
.loading-status {
	padding: 40rpx 0;
	text-align: center;

	.loading-more {
		.loading-text {
			font-size: 28rpx;
			color: #999999;
		}
	}

	.no-more {
		font-size: 28rpx;
		color: #999999;
	}

	.empty-data {
		padding: 100rpx 0;

		.empty-text {
			font-size: 32rpx;
			color: #666666;
		}
	}
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
	.job-title {
		font-size: 30rpx;
		max-width: 250rpx;
	}

	.status-tab {
		font-size: 26rpx;
		padding: 25rpx 0;
	}
}
</style>