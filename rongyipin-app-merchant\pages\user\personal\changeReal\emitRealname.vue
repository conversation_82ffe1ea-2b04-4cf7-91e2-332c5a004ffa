<template>
    <view class="verify-identity-container">
        <!-- 顶部导航 -->
        <view class="nav-bar">
            <view class="nav-content">
                <view class="nav-left" @click="goBack">
                    <text class="nav-back">‹</text>
                </view>
                <view class="nav-center">
                    <text class="nav-title">核实身份</text>
                </view>
                <view class="nav-right"></view>
            </view>
        </view>

        <!-- 页面内容 -->
        <view class="content">
            <!-- 副标题 -->
            <view class="subtitle">
                <text>解绑实名认证需核实您的身份信息</text>
            </view>

            <!-- 表单区域 -->
            <view class="form">
                <!-- 证件类型 -->
                <view class="form-item">
                    <text class="label">证件类型</text>
                    <view class="picker-wrapper" @click="showDocumentTypePicker">
                        <text class="picker-text">{{ selectedDocumentType.name }}</text>
                        <text class="picker-arrow">›</text>
                    </view>
                </view>

                <!-- 证件号码 -->
                <view class="form-item">
                    <text class="label">证件号码</text>
                    <input
                        class="input"
                        type="text"
                        placeholder="请输入证件号码"
                        v-model="documentNumber"
                    />
                </view>

                <!-- 说明文本 -->
                <view class="notice">
                    <text class="notice-label">说明</text>
                    <view class="notice-content">
                        <text class="notice-text">
                            更换个人实名需要先验证当前实名信息，在进行新实名认证，操作成功后完成替换，请您知悉：
                        </text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 底部按钮 -->
        <view class="footer">
            <button class="submit-btn" :class="{ 'active': isFormValid }" :disabled="!isFormValid" @click="submit">
                下一步
            </button>
        </view>

        <!-- 证件类型选择器 -->
        <u-picker
            :show="showPicker"
            :columns="documentTypeColumns"
            @confirm="onDocumentTypeConfirm"
            @cancel="showPicker = false"
            keyName="name"
        ></u-picker>
    </view>
</template>

<script>
import { realApi } from "@/utils/api"
export default {
    data() {
        return {
            documentNumber: '',
            selectedDocumentType: { id: 1, name: '身份证' },
            showPicker: false,
            documentTypeColumns: [
                [
                    { id: 1, name: '身份证' },
                    // { id: 2, name: '护照' },
                    // { id: 3, name: '港澳通行证' },
                    // { id: 4, name: '台湾通行证' }
                ]
            ]
        };
    },
    computed: {
        isFormValid() {
            return this.documentNumber.trim() !== '' && this.selectedDocumentType.id;
        }
    },
    methods: {
        goBack() {
            uni.navigateBack()
        },
        showDocumentTypePicker() {
            this.showPicker = true;
        },
        onDocumentTypeConfirm(value) {
            this.selectedDocumentType = value.value[0];
            this.showPicker = false;
        },
        async submit() {
            if (!this.isFormValid) {
                uni.showToast({
                    title: '请填写完整信息',
                    icon: 'none'
                });
                return;
            }

            // 根据证件类型进行不同的验证
            if (this.selectedDocumentType.id === 1) {
                // 身份证验证
                const idCardRegex = /^(^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)$)$/;
                if (!idCardRegex.test(this.documentNumber)) {
                    uni.showToast({
                        title: '身份证号格式不正确',
                        icon: 'none'
                    });
                    return;
                }
            }

            uni.showLoading({ title: '验证中...' });

            try {
                const params = {
                    identity: this.documentNumber
                }

                let res = await realApi.checkIdentity(params)
                uni.hideLoading();

                if (res.code == 200) {
                    uni.showToast({
                        title: '验证成功',
                        icon: 'success'
                    });
                    setTimeout(() => {
                        // 跳转到下一步页面
                        uni.navigateTo({
                            url: '/pages/user/personal/changeReal/emitPhone'
                        });
                    }, 1500);
                } else {
                    uni.showToast({
                        title: res.msg || '验证失败',
                        icon: 'none'
                    });
                }
            } catch (error) {
                uni.hideLoading();
                uni.showToast({
                    title: '网络错误，请重试',
                    icon: 'none'
                });
            }
        }
    }
}
</script>

<style scoped>
.verify-identity-container {
    background: #f5f5f5;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 导航栏 */
.nav-bar {
    background: #fff;
    height: 88rpx;
    display: flex;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    border-bottom: 1rpx solid #f0f0f0;
}

.nav-content {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32rpx;
}

.nav-left {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-back {
    font-size: 48rpx;
    color: #333;
    font-weight: 300;
}

.nav-center {
    flex: 1;
    text-align: center;
}

.nav-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
}

.nav-right {
    width: 80rpx;
}

/* 内容区域 */
.content {
    flex: 1;
    padding-top: 88rpx;
    padding-bottom: 120rpx;
}

.subtitle {
    padding: 40rpx 32rpx 20rpx;
    text-align: center;
}

.subtitle text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
}

/* 表单 */
.form {
    background: #fff;
    margin: 20rpx 24rpx;
    border-radius: 16rpx;
    padding: 32rpx 24rpx;
}

.form-item {
    margin-bottom: 40rpx;
}

.form-item:last-child {
    margin-bottom: 0;
}

.label {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 16rpx;
    display: block;
}

.picker-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 20rpx;
    background: #f8f9fa;
    border-radius: 12rpx;
    border: 1rpx solid #e9ecef;
}

.picker-text {
    font-size: 28rpx;
    color: #333;
}

.picker-arrow {
    font-size: 32rpx;
    color: #999;
    transform: rotate(90deg);
}

.input {
    /* width: 100%; */
    padding: 24rpx 20rpx;
    background: #f8f9fa;
    border-radius: 12rpx;
    border: 1rpx solid #e9ecef;
    font-size: 28rpx;
    color: #333;
}

.input::placeholder {
    color: #999;
}

/* 说明区域 */
.notice {
    margin-top: 32rpx;
    padding: 24rpx;
    background: #fff9e6;
    border-radius: 12rpx;
    border-left: 6rpx solid #faad14;
}

.notice-label {
    font-size: 26rpx;
    color: #d48806;
    font-weight: 600;
    display: block;
    margin-bottom: 12rpx;
}

.notice-content {
    padding-left: 20rpx;
}

.notice-text {
    font-size: 24rpx;
    color: #8c6e00;
    line-height: 1.6;
}

/* 底部按钮 */
.footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 24rpx 32rpx;
    padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
    border-top: 1rpx solid #f0f0f0;
}

.submit-btn {
    width: 100%;
    height: 88rpx;
    background: #d9d9d9;
    color: #999;
    border: none;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 600;
    transition: all 0.3s;
}

.submit-btn.active {
    background: #4ECDC4;
    color: #fff;
}

.submit-btn:disabled {
    background: #d9d9d9;
    color: #999;
}
</style>