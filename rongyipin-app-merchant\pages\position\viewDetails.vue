<template>
    <view class="page-container">
        <view class="header-section">
            <u-navbar :autoBack="true" :titleStyle="{
                color: '#222',
                fontWeight: 'bold',
                fontSize: '36rpx'
            }" :leftIconSize="30" bgColor="#ffffff" :leftIconColor="'#222'" fixed safeAreaInsetTop
                placeholder></u-navbar>
        </view>

        <!-- 可滚动内容区域 -->
        <scroll-view class="scroll-content" scroll-y="true">
            <view class="container">
                <!-- 头部信息 -->
                <view class="header">
                    <view class="title-section">
                        <text class="job-title">{{ jobData.name }}</text>
                        <text class="salary">{{ jobData.min_salary }}-{{ jobData.max_salary }}</text>
                    </view>

                    <view class="meta-info">
                        <view class="meta-tag" style="width: 50%;">
                            <text class="tag-icon">
                                <image src="@/static/app/home/<USER>"></image>
                            </text>
                            <text class="tag-text" style="width: 90%;white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;">{{ jobData.address_name }}</text>
                        </view>
                        <view class="meta-tag">
                            <text class="tag-icon">
                                <image src="@/static/app/home/<USER>"></image>
                            </text>
                            <text class="tag-text">{{ jobData.experience }}</text>
                        </view>
                        <view class="meta-tag">
                            <text class="tag-icon">
                                <image src="@/static/app/home/<USER>"></image>
                            </text>
                            <text class="tag-text">{{ jobData.education }}</text>
                        </view>
                    </view>

                    <view class="recruiter-info">
                        <image size="40" :src="jobData.avatarUrl"></image>
                        <view class="recruiter-detail">
                            <text class="name">{{ jobData.username }}</text>
                            <text class="position">{{ jobData.job_position_name }}</text>
                        </view>
                    </view>
                </view>

                <!-- 分割线 -->
                <u-line color="#f5f5f5" margin="20rpx 0"></u-line>

                <!-- 职位详情 -->
                <view class="section">
                    <text class="section-title">职位详情</text>
                    <view class="job-info-skill">
                        <view class="skills-container">
                            <view v-for="(skill, index) in jobData.skill_name" :key="index" class="skill-tag">
                                <text class="skill-text">{{ skill }}</text>
                            </view>
                        </view>
                    </view>
                    <view class="job-desc">
                        <text class="sub-title">职位描述：</text>
                        <view class="desc-item">
                            <text>{{ jobData.content }}</text>
                        </view>
                        <!-- <view class="desc-item">
          <text>2.负责包装材料变更与验证、工艺标准制定与执行，并与其他职能团队密切合作，确保包装方案的顺利实施；</text>
        </view> -->
                    </view>

                    <view class="job-requirement">
                        <!-- <text class="sub-title">任职需求：</text> -->
                        <!-- <view class="requirement-item">
          <text>1.本科、硕士学历，包装设计、包装工程、工业设计</text>
        </view> -->
                    </view>
                </view>
                <!-- 员工福利 -->
                <view class="section" v-if="jobData.welfares && jobData.welfares.length > 0">
                    <text class="section-title">员工福利</text>
                    <view class="job-info-skill">
                        <view class="skills-container">
                            <view v-for="(skill, index) in jobData.welfares" :key="index" class="skill-tag">
                                <text class="skill-text">{{ skill }}</text>
                            </view>
                        </view>
                    </view>
                </view>
                <!-- 公司信息 -->
                <view class="section">
                    <text class="section-title">公司信息</text>
                    <view class="company-info">
                        <text class="company-name">{{ jobData.company_name }}</text>
                        <text class="company-detail">{{ jobData.size }}-{{ jobData.lthy }}</text>
                    </view> 

                    <view class="company-locations">
                        <view class="location-item">
                            <u-icon name="map" size="16" color="#666"></u-icon>
                            <text>{{ jobData.formatted_address }}</text>
                        </view>
                        <view class="map-container">
                            <map id="jobLocationMap" class="map" :show-location="false" :scale="scale"
                                :latitude="jobData.lat" :longitude="jobData.lon" :markers="markers"
                                style="width: 100%; height: 400rpx; border-radius: 16rpx;"></map>
                            <!-- 中心点标记 -->
                            <view class="center-marker">
                                <view class="marker-icon">📍</view>
                            </view>

                            <!-- 定位按钮 -->
                            <!-- <view class="location-btn" @click="getCurrentLocation">
                <uni-icons type="location" size="24" color="#10d2c3"></uni-icons>
            </view> -->
                        </view>
                    </view>
                </view>
            </view>
        </scroll-view>
        <!-- 底部操作栏 -->
        <view class="footer">
            <!-- <u-button type="default" plain size="medium" @click="handleCollect">收藏</u-button> -->
            <u-button v-if="jobData.merge_status == 0" type="primary" size="medium"
                @click="startConfirmlate">重新开放职位</u-button>
            <view v-if="jobData.merge_status == 1" style="display: flex;width: 100%;">
                <u-button style="width: 30%;border: 1px solid #6FC6C7;" @click="emitDate">编辑</u-button>
                <u-button style="width: 60%;" type="primary" size="medium" @click="Confirmlatebtn">
                    关闭职位</u-button>
            </view>
            <u-button v-if="jobData.merge_status == 2" type="primary" size="medium"
                @click="handleApply">职位审核中</u-button>
            <u-button v-if="jobData.merge_status == 3" type="primary" size="medium" @click="emitDate">编辑职位</u-button>
        </view>
    </view>
</template>

<script>
import { positionsApi } from '../../utils/api'
export default {
    data() {
        return {
            show: false,
            title: '标题',
            content: '确定退出登录吗',
            isExpanded: false, // 默认折叠状态
            jobData: {},
            scale: 16,
            markers: [], // 标记点
        }
    },
    computed: {
        // formattedContent() {
        //     // 将 \n 替换为 <br>
        //     return this.jobData.content ? this.jobData.content.replace(/\n/g, '<br>') : '';
        // }
    },
    async onLoad(options) {
        if (options) {
            uni.showLoading({
                title: "加载中",
            });
            const params = {
                job_id: options.job_id,
                type: options.type
            }
            let res = await positionsApi.postJobInfonew(params)
            if (res.code == 200) {
                this.jobData = res.data
                uni.hideLoading();
            } else {
                uni.hideLoading();
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }
        }
    },
    methods: {
        //修改职位
        emitDate() {
            uni.navigateTo({
                url: `/pages/position/detail?type=${this.jobData.type}&job_id=${this.jobData.id}`
            });
        },
        //删除职位
        async deletedate() {
            const params = {
                type: this.jobData.type,
                status: 2,
                job_id: this.jobData.id
            }
            let res = await positionsApi.postJobStatus(params)
            if (res.code == 200) {
                uni.showToast({
                    title: res.msg,
                })
                uni.switchTab({
                    url: '/pages/position/position'
                })
            } else {
                uni.showToast({
                    title: res.msg,
                })
            }
        },
        //暂停招聘
        async Confirmlatebtn() {
            const params = {
                status: 0,
                job_id: this.jobData.id
            }
            let res = await positionsApi.postJobStatus(params)
            if (res.code == 200) {
                uni.showToast({
                    title: res.msg,
                })
                uni.switchTab({
                    url: '/pages/position/position'
                })
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }

        },
        //开始招聘
        async startConfirmlate() {
            const params = {
                type: this.jobData.type,
                status: 1,
                job_id: this.jobData.id
            }
            let res = await positionsApi.postJobStatus(params)
            if (res.code == 200) {
                uni.showToast({
                    title: res.msg,
                })
                uni.switchTab({
                    url: '/pages/position/position'
                })
            } else {
                uni.showToast({
                    title: res.msg,
                })
            }
        },
        formatDate(datetime) {
            const date = new Date(datetime * 1000); // 转为毫秒

            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hour = String(date.getHours()).padStart(2, '0');
            const minute = String(date.getMinutes()).padStart(2, '0');
            const second = String(date.getSeconds()).padStart(2, '0');
            return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
        },
        handleBack() {
            uni.navigateBack();
        },
        toggleExpand() {
            this.isExpanded = !this.isExpanded; // 切换展开/折叠状态
        }
    }
}
</script>

<style lang="scss" scoped>
.page-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #fff;
}

.scroll-content {
    flex: 1;
    height: 0; /* 重要：让 flex 子元素正确计算高度 */
}

.container {
    padding: 20rpx 30rpx 120rpx; /* 增加底部 padding，为底部操作栏留出空间 */
    background-color: #fff;
    min-height: 100%;
}

.header {
    .title-section {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;

        .job-title {
            font-size: 36rpx;
            font-weight: bold;
            margin-right: 20rpx;
        }

        .salary {
            font-size: 32rpx;
            color: #ff5a5f;
        }
    }

    .meta-info {
        margin-bottom: 30rpx;
        display: flex;
        // flex-wrap: wrap;
        // align-items: center;
        // gap: 20rpx;

        .meta-tag {
            display: flex;
            align-items: center;
            // background: #f5f5f5;
            border-radius: 16rpx;
            padding: 8rpx 16rpx;

            .tag-icon {
                font-size: 24rpx;
                margin-right: 8rpx;

                image {
                    width: 25rpx;
                    height: 25rpx;
                }
            }

            .tag-text {
                font-size: 24rpx;
                color: #666666;
                line-height: 1;
            }
        }
    }

    .recruiter-info {
        display: flex;
        align-items: center;

        image {
            width: 45rpx;
            height: 45rpx;
        }

        .recruiter-detail {
            margin-left: 20rpx;
            display: flex;
            flex-direction: column;

            .name {
                font-size: 28rpx;
                font-weight: 500;
            }

            .position {
                font-size: 24rpx;
                color: #999;
            }
        }
    }
}

.section {
    margin-top: 30rpx;

    .section-title {
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 20rpx;
        display: block;
    }

    .sub-title {
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
        margin: 20rpx 0 10rpx;
        display: block;
    }

    // 技能标签样式
    .job-info-skill {
        margin-bottom: 30rpx;
    }

    .skills-container {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;
        margin-top: 20rpx;
    }

    .skill-tag {
        background: #f5f5f5;
        border-radius: 20rpx;
        padding: 12rpx 24rpx;
        border: 1rpx solid #e8e8e8;
    }

    .skill-text {
        font-size: 26rpx;
        color: #666;
        line-height: 1;
    }

    .desc-item,
    .requirement-item {
        font-size: 26rpx;
        color: #666;
        line-height: 1.6;
        margin-bottom: 10rpx;
    }

    .company-info {
        margin-bottom: 20rpx;

        .company-name {
            font-size: 28rpx;
            font-weight: 500;
            display: block;
            margin-bottom: 10rpx;
        }

        .company-detail {
            font-size: 24rpx;
            color: #999;
        }
    }

    .company-locations {
        .location-item {
            display: flex;
            align-items: center;
            padding: 15rpx 0;
            border-bottom: 1rpx solid #f5f5f5;

            &:last-child {
                border-bottom: none;
            }

            text {
                margin-left: 10rpx;
                font-size: 26rpx;
                color: #666;
            }
        }
    }
}

.section {
    margin-top: 30rpx;

    .section-title {
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 20rpx;
        display: block;
    }
}

// 地图容器
.map-container {
    height: 300rpx;
    position: relative;

    &.with-search {
        height: calc(100vh - 500rpx);
    }

    .jobLocationMap {
        width: 100%;
        height: 100%;
    }

    // 中心点标记
    .center-marker {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -100%);
        z-index: 10; // 提高z-index确保在最上层
        pointer-events: none; // 不阻止地图交互

        .marker-icon {
            font-size: 60rpx;
            color: #10d2c3;
            text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
            animation: bounce 2s infinite; // 添加跳动动画
        }
    }

    // 定位按钮
    .location-btn {
        position: absolute;
        right: 30rpx;
        bottom: 200rpx;
        width: 80rpx;
        height: 80rpx;
        background-color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
        z-index: 5;
    }
}

.footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    padding: 15rpx 30rpx;
    padding-bottom: calc(15rpx + env(safe-area-inset-bottom)); /* 适配安全区域 */
    background-color: #fff;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    z-index: 9999; /* 确保在最上层 */

    ::v-deep .u-btn {
        flex: 1;
        margin: 0 10rpx;
    }
}
</style>