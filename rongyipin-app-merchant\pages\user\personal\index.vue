<template>
	<view class="personal-info-container">
		<!-- 顶部导航栏 -->
		<view class="navbar">
			<view class="nav-left" @click="goBack">
				<text class="nav-icon">‹</text>
			</view>
			<view class="nav-title">
				<text class="title-text">个人信息</text>
			</view>
			<view class="nav-right"></view>
		</view>

		<!-- 内容区域 -->
		<view class="content">
			<!-- 头像 -->
			<view class="info-item avatar-item">
				<view class="item-left">
					<text class="item-label">头像</text>
				</view>
				<view class="item-right" @click="changeAvatar">
					<image class="avatar" :src="userInfo.avatarUrl" mode="aspectFill"></image>
				</view>
			</view>

			<!-- 姓名 -->
			<view class="info-item" @click="editName">
				<view class="item-left">
					<text class="item-label">姓名</text>
				</view>
				<view class="item-right">
					<text class="item-value">{{ userInfo.username }}</text>
					<!-- <text class="item-status">{{ userInfo.nameVerified ? '已实名' : '' }}</text> -->
					<text class="arrow-icon">›</text>
				</view>
			</view>

			<!-- 手机号 -->
			<view class="info-item" @click="editPhone">
				<view class="item-left">
					<text class="item-label">手机号</text>
				</view>
				<view class="item-right">
					<text class="item-value">{{ userInfo.telephone }}</text>
					<text class="arrow-icon">›</text>
				</view>
			</view>

			<!-- 微信号 -->
			<view class="info-item" @click="editWechat">
				<view class="item-left">
					<text class="item-label">微信号</text>
				</view>
				<view class="item-right">
					<text class="item-value">{{ userInfo.wechat }}</text>
					<text class="arrow-icon">›</text>
				</view>
			</view>
			<!-- 我的公司 -->
			<view class="info-item" @click="editCompany">
				<view class="item-left">
					<text class="item-label">我的公司</text>
				</view>
				<view class="item-right">
					<text class="item-value">{{ is_auth.name}}</text>
					<text class="arrow-icon">›</text>
				</view>
			</view>

			<!-- 我的职位 -->
			<view class="info-item" @click="editPosition">
				<view class="item-left">
					<text class="item-label">我的职位</text>
				</view>
				<view class="item-right">
					<text class="item-value">{{ userInfo.job_position_name }}</text>
					<text class="arrow-icon">›</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import {priseApi} from "@/utils/api.js"
export default {
	data() {
		return {
			userInfo: {
				// avatar: 'https://via.placeholder.com/80x80/4A90E2/FFFFFF?text=头像',
				// name: '王二小',
				// nameVerified: true,
				// phone: '132******54',
				// wechat: '132******54',
				// company: '羊羊羊（河北）网络科技有限公司',
				// position: '人事经理'
			},
			is_auth:{},
			imageConver1:''
		}
	},
	onShow(){
		this.userInfo = uni.getStorageSync('usinfo')
		this.is_auth =uni.getStorageSync('is_auth')
		console.log(this.userInfo,this.is_auth)
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 修改头像
		changeAvatar() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.userInfo.avatarUrl = res.tempFilePaths[0];
					// 这里可以添加上传头像的逻辑
					this.uploadImage(this.userInfo.avatarUrl)
				}
			});
		},
		uploadImage(filePath) { 
            console.log('选择的图片路径:', filePath)
            uni.showLoading({ title: '上传中' })
            // #ifdef APP-PLUS
            uni.uploadFile({
                url: 'http://8.130.152.121:82/api/common/upload', // 替换为你自己的上传地址
                filePath: filePath,
                name: 'file', // 根据后端字段设定
                formData: {
                    type: 1
                },
                success: (uploadFileRes) => {
                    console.log(uploadFileRes)
                    uni.hideLoading()
                    uni.showToast({ title: '上传成功', icon: 'success' })
                    this.imageConver1 = JSON.parse(uploadFileRes.data).data.url
                },
                fail: (err) => {
                    uni.hideLoading()
                    uni.showToast({ title: '上传失败', icon: 'none' })
                    console.error('上传失败:', err)
                }
            })
            // #endif

            // #ifdef H5
            uni.uploadFile({
                url: '/api/common/upload', // 替换为你自己的上传地址
                filePath: filePath,
                name: 'file', // 根据后端字段设定
                formData: {
                    type: 1
                },
                success: (uploadFileRes) => {
                    console.log(uploadFileRes)
                    uni.hideLoading()
                    uni.showToast({ title: '上传成功', icon: 'success' })
                    this.imageConver1 = JSON.parse(uploadFileRes.data).data.url
					this.emiturl(this.imageConver1)
                },
                fail: (err) => {
                    uni.hideLoading()
                    uni.showToast({ title: '上传失败', icon: 'none' })
                    console.error('上传失败:', err)
                }
            })
            // #endif
        },
		//修改头像
		 async emiturl(item){
            const res =await priseApi.userBasicEdit({
                avatarUrl: item
            })
            if(res.code == 200){
                uni.showToast({ title: '上传成功', icon: 'success' })
				this.userInfo.avatarUrl =item
				uni.setStorageSync('usinfo', this.userInfo)
            }else{
                uni.showToast({ title: res.msg, icon: 'error' })
            }
        },
		// 编辑姓名
		editName() {
			uni.navigateTo({
				url: './emitName'
			});
		},
		
		// 编辑手机号
		editPhone() {
			uni.navigateTo({
				url: './emitPhone'
			});
		},
		
		// 编辑微信号
		editWechat() {
			uni.navigateTo({
				url: './emitWechat'
			});
		},
		
		// 编辑公司
		editCompany() {
			uni.navigateTo({
				url: './emitCompany'
			});
		},
		
		// 编辑职位
		editPosition() {
			uni.navigateTo({
				url: './myPositions'
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.personal-info-container {
	min-height: 100vh;
	background: #f8f9fa;
}

/* 导航栏样式 */
.navbar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 88rpx;
	padding: 0 30rpx;
	background: #ffffff;
	border-bottom: 1rpx solid #f0f0f0;
	position: sticky;
	top: 0;
	z-index: 100;
}

.nav-left, .nav-right {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.nav-icon {
	font-size: 40rpx;
	color: #333333;
	font-weight: 500;
}

.nav-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

/* 内容区域 */
.content {
	padding: 20rpx 0;
}

/* 信息项样式 */
.info-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	background: #ffffff;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.3s ease;
	
	&:active {
		background: #f8f9fa;
	}
}

.avatar-item {
	padding: 40rpx 30rpx;
}

.item-left {
	flex-shrink: 0;
}

.item-label {
	font-size: 30rpx;
	color: #333333;
	font-weight: 500;
}

.item-right {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	gap: 16rpx;
}

.item-value {
	font-size: 28rpx;
	color: #666666;
	max-width: 400rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.item-status {
	font-size: 24rpx;
	color: #4A90E2;
	background: #E8F4FD;
	padding: 4rpx 12rpx;
	border-radius: 20rpx;
	flex-shrink: 0;
}

.arrow-icon {
	font-size: 32rpx;
	color: #cccccc;
	flex-shrink: 0;
}

/* 头像样式 */
.avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	border: 2rpx solid #f0f0f0;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
	.item-value {
		max-width: 300rpx;
	}
}
</style>
