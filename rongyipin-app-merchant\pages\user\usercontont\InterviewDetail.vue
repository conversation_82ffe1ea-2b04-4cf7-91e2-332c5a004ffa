<template>
	<view class="interview-detail-page">
		<!-- 顶部导航栏 -->
		<view class="navbar">
			<u-navbar height="44px" title="面试详情" :autoBack="true" :leftIconSize="30"
				:leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
			</u-navbar>
		</view>

		<!-- 标题和签到区域 -->
		<view class="header-section">
			<text class="page-title">参加线下面试</text>
			<view class="checkin-btn" @click="confirmCheckIn">
				<text class="checkin-text">确认签到</text>
			</view>
		</view>

		<!-- 内容区域 -->
		<scroll-view class="content-container" scroll-y>
			<!-- 公司信息卡片 -->
			<view class="company-card" v-for="item in interviewData.job_info" >
				<view class="company-header">
					<image class="company-logo" :src="item.logo" mode="aspectFill"></image>
					<view class="company-info">
						<text class="company-name">{{item.company_name || '公司' }}</text>
						<text class="job-title">{{ interviewData.username  }}</text>
					</view>
				</view>
			</view>

			<!-- 面试信息卡片 -->
			<view class="info-card">
				<view class="info-item">
					<text class="info-label">面试时间</text>
					<text class="info-value">{{ interviewData.interview_time }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">面试职位</text>
					<view class="salary-row" v-for="item in interviewData.job_info">
						<text class="info-value">{{item.name }}  {{ item.min_salary }}-{{ item.max_salary }}</text>
						<u-icon name="arrow-right" size="16" color="#999"></u-icon>
					</view>
				</view>
				<view class="info-item">
					<text class="info-label">面试地址</text>
					<view class="address-row">
						<text class="info-value address-text">{{ interviewData.address }}</text>
						<u-icon name="arrow-right" size="16" color="#999"></u-icon>
					</view>
				</view>
				<view class="info-item">
					<text class="info-label">联系人</text>
					<view class="contact-row">
						<text class="info-value">{{ interviewData.telephone }}</text>
						<view class="contact-btn" @click="contactInterviewer">
							<text class="contact-text">联系</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 地图卡片 -->
			<view class="map-card" v-for="item in interviewData.job_info" >
				<map
					id="interviewMap"
					class="map"
					:latitude="item.lat || 36.6253"
					:longitude="item.lon || 114.5391"
					:markers="markers"
					:scale="16"
					style="width: 100%; height: 400rpx; border-radius: 16rpx;"
					@markertap="onMarkerTap"
					@tap="onMapTap"
				></map>
			</view>

			<!-- 安全提醒卡片 -->
			<view class="safety-card">
				<view class="safety-header">
					<u-icon name="info-circle" size="20" color="#14B19E"></u-icon>
					<text class="safety-title">安全提醒</text>
				</view>
				<text class="safety-content">面试过程中，如对方有任何涉及金钱相关的不当要求，请立即终止面试并举报。请确保面试地点的安全性，建议选择公共场所进行面试。</text>
				<view class="safety-link" @click="viewReportGuide">
					<text class="link-text">查看举报手册</text>
				</view>
			</view>

			<!-- 面试流程卡片 -->
			<view class="process-card">
				<text class="process-title">面试流程</text>

				<view class="process-item">
					<view class="process-icon">
						<u-icon name="checkmark-circle" size="20" color="#14B19E"></u-icon>
					</view>
					<view class="process-content">
						<text class="process-step">建立联系</text>
						<text class="process-desc">您的简历通过了企业的初步筛选</text>
					</view>
				</view>

				<view class="process-item">
					<view class="process-icon">
						<u-icon name="checkmark-circle" size="20" color="#14B19E"></u-icon>
					</view>
					<view class="process-content">
						<text class="process-step">确定面试</text>
						<text class="process-desc">对方已确定面试时间，可开始准备面试</text>
					</view>
				</view>

				<view class="process-item">
					<view class="process-icon">
						<u-icon name="clock" size="20" color="#999"></u-icon>
					</view>
					<view class="process-content">
						<text class="process-step">开始面试</text>
						<text class="process-desc">面试当天，请准时到达面试地点，祝您面试顺利</text>
					</view>
				</view>

				<view class="process-item">
					<view class="process-icon">
						<u-icon name="clock" size="20" color="#999"></u-icon>
					</view>
					<view class="process-content">
						<text class="process-step">面试结果</text>
						<text class="process-desc">面试结束后，请耐心等待面试结果</text>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import { interview } from "@/utils/api"
export default {
	data() {
		return {
			interviewId: '',
			interviewData: {
				companyName: '公司',
				jobTitle: '张女士·人事HR',
				interviewTime: '2025-06-01 13:00',
				salary: '设计经理·4-5k',
				address: '邯郸市邯山区广场',
				contactName: '张女士',
				companyLogo: '/static/default-company.png',
				latitude: 36.6253,
				longitude: 114.5391
			},
			markers: []
		}
	},
	onLoad(options) {
		if (options.id) {
			this.interviewId = options.id
			this.loadInterviewDetail()
		}
		this.initMap()
	},
	methods: {
		// 加载面试详情
		async loadInterviewDetail() {
			try {
				uni.showLoading({ title: '加载中...' })

				// TODO: 调用API获取面试详情
				const response = await interview.InterviewInfo({ id: this.interviewId })
				if (response.code == 200) {
                    console.log(response.data)
				    this.interviewData = response.data
				    this.initMap()
				} else {
				    uni.showToast({
				        title: response.msg || '获取面试详情失败',
				        icon: 'none'
				    })
				}
                uni.hideLoading()

				// 模拟数据
				// setTimeout(() => {
				// 	this.interviewData = {
				// 		companyName: 'xxx传媒',
				// 		jobTitle: '张女士·人事HR',
				// 		interviewTime: '2025-06-01 13:00',
				// 		salary: '设计经理·4-5k',
				// 		address: '邯郸市邯山区广场',
				// 		contactName: '张女士',
				// 		companyLogo: '/static/default-company.png',
				// 		latitude: 36.6253,
				// 		longitude: 114.5391
				// 	}
				// 	this.initMap()
				// 	uni.hideLoading()
				// }, 500)
			} catch (error) {
				console.error('获取面试详情失败:', error)
				uni.hideLoading()
				uni.showToast({
					title: '获取面试详情失败',
					icon: 'none'
				})
			}
		},

		// 初始化地图
		initMap() {
			// 获取经纬度数据
			let lat = 36.6253; // 默认纬度
			let lon = 114.5391; // 默认经度
			let address = '面试地点'; // 默认地址

			// 从面试数据中获取经纬度
			if (this.interviewData.job_info && this.interviewData.job_info[0]) {
				lat = this.interviewData.job_info[0].lat || lat;
				lon = this.interviewData.job_info[0].lon || lon;
			}

			// 获取地址信息
			if (this.interviewData.address) {
				address = this.interviewData.address;
			}

			// 设置地图标记点
			this.markers = [{
				id: 1,
				latitude: lat,
				longitude: lon,
				width: 32,
				height: 32,
				iconPath: '/static/app/home/<USER>', // 自定义标记图标
				callout: {
					content: address,
					color: '#333',
					fontSize: 14,
					borderRadius: 4,
					bgColor: '#fff',
					padding: 8,
					display: 'ALWAYS',
					textAlign: 'center'
				},
				// 标记点样式
				anchor: {
					x: 0.5,
					y: 1
				}
			}]

			console.log('地图标记点设置完成:', this.markers);
		},

		// 确认签到
		confirmCheckIn() {
			uni.showModal({
				title: '确认签到',
				content: '确定要签到此次面试吗？',
				success: (res) => {
					if (res.confirm) {
						// 获取位置信息
						uni.showLoading({ title: '获取位置中...' })
						// #ifdef H5
						// 使用uni.getLocation获取当前位置
						uni.getLocation({
							type: 'wgs84', // 使用国测局坐标系
							success: async (location) => {
								console.log('获取位置成功:', location)

								// 获取位置成功，调用签到API
								uni.showLoading({ title: '签到中...' })

								try {
									// 调用签到API，传入经纬度
									const response = await interview.CheckIn({
										id: this.interviewId,
										lat: location.latitude,
										lon: location.longitude
									})

									uni.hideLoading()

									if (response.code == 200) {
										uni.showToast({
											title: '签到成功',
											icon: 'success'
										})
									} else {
										uni.showToast({
											title: response.msg || '签到失败',
											icon: 'none'
										})
									}
								} catch (error) {
									console.error('签到失败:', error)
									uni.hideLoading()
									uni.showToast({
										title: '签到失败',
										icon: 'none'
									})
								}
							},
							fail: (err) => {
								console.error('获取位置失败:', err)
								uni.hideLoading()

								// 位置获取失败处理
								if (err.errMsg && err.errMsg.includes('auth deny')) {
									// 权限被拒绝
									uni.showModal({
										title: '位置权限',
										content: '需要获取您的位置才能签到，是否前往设置开启位置权限？',
										success: (modalRes) => {
											if (modalRes.confirm) {
												uni.openSetting()
											}
										}
									})
								} else {
									// 其他错误
									uni.showToast({
										title: '获取位置失败，无法签到',
										icon: 'none'
									})
								}
							}
						})
						// #endif

						// #ifdef APP-PLUS
						uni.getLocation({
							type: 'gcj02', // 使用国测局坐标系
							success: async (location) => {
								console.log('获取位置成功:', location)

								// 获取位置成功，调用签到API
								uni.showLoading({ title: '签到中...' })

								try {
									// 调用签到API，传入经纬度
									const response = await interview.CheckIn({
										id: this.interviewId,
										lat: location.latitude,
										lon: location.longitude
									})

									uni.hideLoading()

									if (response.code == 200) {
										uni.showToast({
											title: '签到成功',
											icon: 'success'
										})
									} else {
										uni.showToast({
											title: response.msg || '签到失败',
											icon: 'none'
										})
									}
								} catch (error) {
									console.error('签到失败:', error)
									uni.hideLoading()
									uni.showToast({
										title: '签到失败',
										icon: 'none'
									})
								}
							},
							fail: (err) => {
								console.error('获取位置失败:', err)
								uni.hideLoading()

								// 位置获取失败处理
								if (err.errMsg && err.errMsg.includes('auth deny')) {
									// 权限被拒绝
									uni.showModal({
										title: '位置权限',
										content: '需要获取您的位置才能签到，是否前往设置开启位置权限？',
										success: (modalRes) => {
											if (modalRes.confirm) {
												uni.openSetting()
											}
										}
									})
								} else {
									// 其他错误
									uni.showToast({
										title: '获取位置失败，无法签到',
										icon: 'none'
									})
								}
							}
						})
						// #endif
					}
				}
			})
		},

		// 联系面试官
		contactInterviewer() {
			uni.showActionSheet({
				itemList: ['拨打电话', '发送短信'],
				success: (res) => {
					if (res.tapIndex === 0) {
						// 拨打电话
						uni.makePhoneCall({
							phoneNumber: this.interviewData.user_info[0].mobile, // 模拟电话号码
							fail: () => {
								uni.showToast({
									title: '拨打电话失败',
									icon: 'none'
								})
							}
						})
					} else if (res.tapIndex === 1) {
						// 发送短信
						uni.showToast({
							title: '短信功能暂未开放',
							icon: 'none'
						})
					}
				}
			})
		},

		// 查看举报手册
		viewReportGuide() {
			uni.navigateTo({
				url: '/pages/user/report/guide'
			})
		},

		// 地图标记点点击事件
		onMarkerTap(e) {
			console.log('标记点被点击:', e);
			this.openNavigation();
		},

		// 地图点击事件
		onMapTap(e) {
			console.log('地图被点击:', e);
		},

		// 打开导航
		openNavigation() {
			// 获取经纬度
			let lat = 36.6253;
			let lon = 114.5391;
			let address = '面试地点';

			if (this.interviewData.job_info && this.interviewData.job_info[0]) {
				lat = this.interviewData.job_info[0].lat || lat;
				lon = this.interviewData.job_info[0].lon || lon;
			}

			if (this.interviewData.address) {
				address = this.interviewData.address;
			}

			uni.showActionSheet({
				itemList: ['使用系统地图导航', '复制地址'],
				success: (res) => {
					if (res.tapIndex === 0) {
						// 打开系统地图导航
						uni.openLocation({
							latitude: lat,
							longitude: lon,
							name: address,
							address: address,
							success: () => {
								console.log('打开地图成功');
							},
							fail: (err) => {
								console.error('打开地图失败:', err);
								uni.showToast({
									title: '打开地图失败',
									icon: 'none'
								});
							}
						});
					} else if (res.tapIndex === 1) {
						// 复制地址
						uni.setClipboardData({
							data: address,
							success: () => {
								uni.showToast({
									title: '地址已复制',
									icon: 'success'
								});
							}
						});
					}
				}
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.interview-detail-page {
	min-height: 100vh;
	background-color: #e8fff5;
	display: flex;
	flex-direction: column;
}

/* 顶部导航栏 */
.navbar {

}

/* 标题和签到区域 */
.header-section {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	// margin-top: 88rpx;
}

.page-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

.checkin-btn {
	padding: 12rpx 24rpx;
	background-color: #14B19E;
	border-radius: 30rpx;
}

.checkin-text {
	font-size: 26rpx;
	color: #fff;
	font-weight: 500;
}

/* 内容容器 */
.content-container {
	flex: 1;
	padding: 0 30rpx 30rpx;
	box-sizing: border-box;
}

/* 公司卡片 */
.company-card {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.company-header {
	display: flex;
	align-items: center;
}

.company-logo {
	width: 80rpx;
	height: 80rpx;
	border-radius: 12rpx;
	margin-right: 20rpx;
	background: #f5f5f5;
}

.company-info {
	flex: 1;
}

.company-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
}

.job-title {
	font-size: 26rpx;
	color: #666;
}

/* 信息卡片 */
.info-card {
	background: white;
	border-radius: 16rpx;
	padding: 0 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.info-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 0;
	border-bottom: 1rpx solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}
}

.info-label {
	font-size: 28rpx;
	color: #666;
}

.info-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.salary-row, .address-row {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.address-text {
	max-width: 400rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.contact-row {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.contact-btn {
	padding: 8rpx 20rpx;
	background: rgba(20, 177, 158, 0.1);
	border-radius: 30rpx;
}

.contact-text {
	font-size: 24rpx;
	color: #14B19E;
	font-weight: 500;
}

/* 地图卡片 */
.map-card {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.map {
	width: 100%;
	height: 400rpx;
	border-radius: 16rpx;
}

/* 安全提醒卡片 */
.safety-card {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.safety-header {
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-bottom: 16rpx;
}

.safety-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #14B19E;
}

.safety-content {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
	margin-bottom: 16rpx;
}

.safety-link {
	text-align: right;
}

.link-text {
	font-size: 26rpx;
	color: #14B19E;
	text-decoration: underline;
}

/* 面试流程卡片 */
.process-card {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.process-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 30rpx;
	display: block;
}

.process-item {
	display: flex;
	margin-bottom: 30rpx;
	position: relative;

	&:last-child {
		margin-bottom: 0;
	}

	&:not(:last-child)::after {
		content: '';
		position: absolute;
		left: 10rpx;
		top: 40rpx;
		bottom: -10rpx;
		width: 1rpx;
		background: #e0e0e0;
	}
}

.process-icon {
	margin-right: 20rpx;
	z-index: 1;
}

.process-content {
	flex: 1;
}

.process-step {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
}

.process-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.4;
}
</style>