<template>
    <view class="settings-container">
        <!-- 状态栏和导航栏 -->
        <view class="header">
            <u-navbar style="background: #f0f1f6;" height="44px" title="推荐候选人详情" @rightClick="handleBack"
                :autoBack="true" :leftIconSize="30" :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>
        <view class="information">
            <view class="info-title">
                <p class="title-name">{{ option.username }}</p>
                <p class="title-city"><span style="display: inline-block;margin-right: 5rpx;"><uni-icons type="location"
                            size="18" color="#cad4de"></uni-icons></span> 距工作地点{{ option.distance }}km</p>
            </view>
            <view style="border-radius: 50%;">
                <image src="static/app/audit/illustration.png" alt=""/>
            </view>
        </view>
        <view class="basic">
            <p class="basic-information">基本信息</p>
            <view class="basic-conten">
                <view class="basic-cake">
                    <p>
                        <image src="@/static/app/home/<USER>" alt=""/>
                        <span>生日</span>
                    </p>
                    <p>{{ option.age }}</p>
                </view>
                <view class="basic-cake">
                    <p>
                        <image src="@/static/app/home/<USER>" alt=""/>
                        <span>性别</span>
                    </p>
                    <p>{{ option.sex_name }}</p>
                </view>
            </view>
        </view>
        <view class="basic" v-if="option.degree_name != ''">
            <p class="basic-information">教育经历</p>
            <view class="basic-conten">
                <!-- <view class="basic-cake">
                    <p>
                        <img src="@/static/app/home/<USER>" alt="">
                        <span>学校</span>
                    </p>
                    <p>{{ option.age }}</p>
                </view> -->
                <view class="basic-cake">
                    <p>
                        <image src="@/static/app/home/<USER>" alt=""/>
                        <span>学历</span>
                    </p>
                    <p>{{ option.degree_name }}</p>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            option: {}
        }
    },
    computed: {

    },
    async onLoad(options) {
        if (options.item) {
            const item = JSON.parse(decodeURIComponent(options.item));
            this.option = item
            console.log(this.option, '$$')
        }
    },
    methods: {
    }
}
</script>

<style lang="scss" scoped>
.header {
    height: 88rpx;
    /* display: flex;
    justify-content: space-between;
    align-items: center; */
    background-color: #fff;

    .navbar {
        height: 100%;
    }
}

.information {
    height: 230rpx;
    background-color: white;
    display: flex;
    padding: 70rpx 30rpx 0rpx 30rpx;

    .info-title {
        width: 80%;

        .title-name {
            font-size: 50rpx;
            font-weight: 600;
        }
    }

    .title-city {
        color: #83888d;
        margin-top: 10rpx;
    }

    image {
        width: 150rpx;
        height: 150rpx;
        border-radius: 20%;
    }
}

.basic {

    .basic-information {
        padding: 30rpx;
        // height: 100rpx;
        background-color: #f7f8fa;
        color: black;
        font-weight: 500;
        font-size: 35rpx;
    }

    .basic-conten {
        .basic-cake {
            // width: 100%;
            display: flex;
            align-items: center;
            height: 100rpx;
            justify-content: space-between;
            padding: 30rpx 30rpx 0 30rpx;

            p {
                font-size: 35rpx;
                display: flex;
                align-items: center;
                color: #848789;

                image {
                    width: 40rpx;
                    height: 40rpx;
                }

                span {
                    margin-left: 20rpx;
                    color: black;
                }
            }
        }
    }
}

.settings-container {
    min-height: 100vh;
    // background-color: #f6f7f9;
}
</style>