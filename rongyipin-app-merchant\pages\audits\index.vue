<template>
    <view class="certification-page">
        <image class="certification-image" src="@/static/app/audit/illustration.png" mode="widthFix"></image>
        <text class="certification-text">企业认证</text>
        <text class="status-text">审核中</text>
        <text class="tips-text">预计1-3天审核完成</text>
        <!-- <u-button text="确定" class="tips-button" @click="navigateToPublish"></u-button> -->
    </view>
</template>

<script>
import { priseApi } from "@/utils/api"
export default {
    data() {
        return {
            // You can add any dynamic data here if needed
        };
    },
    async onShow() {
        let reslate = await priseApi.getCompanyInfoe();
        if (reslate.code == 200) {
            if (reslate.data.is_auth == 1) {
                uni.setStorageSync('is_auth', reslate.data)
            }
            let res = await positionsApi.postJobList({
                page: 1,
                size: 10000,
                status: 1,
                order: 0
            })
            if (res.code == 200) {
                if (res.data.data.length > 0) {
                    console.log('1111')
                    uni.switchTab({
                        url: '/pages/homeIndex/index',
                    })
                } else {
                    console.log('222')
                    uni.redirectTo({
                        url: '/pages/second/second',
                    })
                }
            }

        } else if (reslate.code == 404) {
            uni.navigateTo({
                url: '/pages/authentication/index',
            })
        }else if (reslate.code == 405) {
            console.log('reslate', reslate.data.desc)
            uni.redirectTo({ url: `/pages/audits/failed?msg=${reslate.data.desc}` })
        }
    },
    methods: {
        // You can add any methods here if needed
        navigateToPublish() {
            console.log(1111)
            uni.switchTab({
                url: '/pages/index/index'
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.certification-page {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
    width: 100%;
    height: 100vh;
    /* Make it full screen */
    background-color: #f1f0f0;
    /* White background */

}

.certification-image {
    width: 60%;
    /* Adjust width as needed */
    max-width: 250px;
    /* Max width for larger screens */
    height: auto;
    margin-bottom: 20px;
}

.certification-text {
    font-size: 24px;
    font-weight: bold;
    color: #333333;
    margin-bottom: 8px;
}

.status-text {
    font-size: 18px;
    color: #666666;
    margin-bottom: 25px;
}

.tips-text {
    font-size: 16px;
    color: #888888;
    margin-bottom: 40px;
}

.confirm-button {
    width: 80%;
    max-width: 300px;
    padding: 12px;
    background-color: #f0f0f0;
    /* Light gray button */
    color: #555555;
    /* Darker gray text */
    border: none;
    border-radius: 25px;
    /* Rounded corners */
    font-size: 18px;
    text-align: center;
    cursor: pointer;
}

.tips-button {
    background-color: #02bdc4;
    color: white;
    height: 100rpx;
    width: 80%;
}

/* Add any additional styling here */
</style>