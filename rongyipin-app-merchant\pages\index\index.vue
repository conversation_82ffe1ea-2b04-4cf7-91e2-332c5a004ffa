<template>
	<view class="home-container">
		<!-- 顶部导航 -->
		<view v-if="userInfo == false || selectedPosition != 1 ">
			<view class="nav-header">
				<text class="title">候选人</text>
				<view class="service-btn">
					<image src="/static/service.png" mode="aspectFit" class="service-icon"></image>
					<text class="service-text">客服</text>
				</view>
			</view>

			<!-- 主要内容区 -->
			<view class="content">
				<view class="banner">
					<text class="banner-title">招人就用容翼聘</text>
					<text class="banner-subtitle">8600万人在这找工作</text>
				</view>

				<!-- 功能列表 -->
				<view class="feature-list">
					<view class="feature-item">
						<view class="feature-icon lightning"><image class="img" src="@/static/app/home/<USER>"
								alt=""/>
						</view>
						<view class="feature-info">
							<text class="feature-title">招人快</text>
							<text class="feature-desc">新商家享高速招聘</text>
						</view>
					</view>

					<view class="feature-item">
						<view class="feature-icon doc"><image class="img" src="@/static/app/home/<USER>" alt=""/>
						</view>
						<view class="feature-info">
							<text class="feature-title">简历多</text>
							<text class="feature-desc">8600万+全行业人才库</text>
						</view>
					</view>

					<view class="feature-item">
						<view class="feature-icon gift"><image class="img" src="@/static/app/home/<USER>" alt=""/></view>
						<view class="feature-info">
							<text class="feature-title">赠送招聘道具</text>
							<text class="feature-desc">免费职位刷新2次/日</text>
						</view>
					</view>
				</view>

				<!-- 发布按钮 -->
				<view class="publish-btn-wrapper">
					<button class="publish-btn" @click="navigateToPublish">
						<p class="plus-icon"><image class="img" src="@/static/app/home/<USER>" alt=""/></p>
						<p>快速发布职位</p>
						<p></p>
						<p class="quick-tag">最快30秒</p>
					</button>
				</view>

				<!-- 常见问题 -->
				<view class="faq-section">
					<text class="faq-title">常见问题</text>
					<view class="faq-item" @click="navigateToFaq('publish')">
						<text>发布职位相关问题</text>
						<text class="arrow"><image class="img" src="@/static/app/home/<USER>" alt=""/></text>
					</view>
					<view class="faq-item" @click="navigateToFaq('audit')">
						<text>职位审核相关问题</text>
						<text class="arrow"><image class="img" src="@/static/app/home/<USER>" alt=""/></text>
					</view>
				</view>
			</view>
		</view>
		<view v-else>
			<registration :jobApplyData="jobApplyData" :total="total" :activeTabs="activeTabs" />
		</view>
	</view>
</template>

<script>
import { priseApi, applyApi } from '../../utils/api'
import registration from "./registration/index.vue"
export default {
	data() {
		return {
			selectedPosition: '',
			jobApplyData: [],
			page: 1,
			size: 10,
			total: 0,
			activeTabs: {},
			userInfo:false
		}
	},
	components: {
		registration
	},

	async onShow() {
	
		let ress = await priseApi.getUserInfo()
		if (ress.code == 200) {
			if (ress.data.identity ) {
				this.userInfo = true
			} else {
				this.userInfo = false
			}
		} else {
			this.userInfo = false
		}

		let res = await priseApi.getCompanyInfoe()
		this.selectedPosition = res.data.is_auth
		if (res.data.is_auth == 1) {
			let res = await applyApi.getJobApply({
				size: this.size,
				page: this.page
			})
			this.jobApplyData = res.data.data
			this.total = res.data.total


			const active = uni.getStorageSync('targetTab')
			console.log(active, '^^')
			const list = uni.getStorageSync('itemData')
			if (active != '' && list != '') {
				this.activeTabs = {
					active: active,
					listname: list.name,
					listid: list.id
				}
				uni.removeStorageSync('itemData');
				uni.removeStorageSync('targetTab')
			}
		}

		uni.hideLoading();
	},
	methods: {
		navigateToFaq(type) {
			// 处理常见问题导航
			console.log('Navigate to FAQ:', type)
		},
		async navigateToPublish() {
			let res = await priseApi.getCompanyInfoe()
			console.log(res.data.is_auth, 'res')
			if (res.data.is_auth == 0) {
				uni.navigateTo({
					url: '/pages/audits/index'
				})
			} else {
				uni.navigateTo({
					url: '/pages/second/second'
				})
			}
			// uni.navigateTo({
			// 	url: '../second/second'
			// });
		}
	}
}
</script>

<style lang="scss" scoped>
.home-container {
	// min-height: 100vh;
	height: calc(100vh - 100rpx);
	// background: #02bdc4;
}

.nav-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 100rpx 40rpx 40rpx 50rpx;
	background-color: #02bdc4;

	.title {
		font-size: 36rpx;
		color: #fff;
		font-weight: bold;
	}

	.service-btn {
		display: flex;
		align-items: center;

		.service-icon {
			width: 40rpx;
			height: 40rpx;
		}

		.service-text {
			color: #fff;
			font-size: 28rpx;
			margin-left: 8rpx;
		}
	}
}

.content {
	// height: 100vh;
	// padding: 0 30rpx;
	background-color: white;
	// margin-top: -10rpx;
	border-radius: 30rpx 30rpx 0 0;
}

.banner {
	// background: #fff;
	padding: 40rpx;
	// border-radius: 20rpx;
	margin-bottom: 30rpx;
	border-radius: 30rpx 30rpx 0 0;
	margin-top: -20rpx;

	.banner-title {
		font-size: 50rpx;
		font-weight: bold;
		color: black;
		display: block;
		margin-bottom: 16rpx;
	}

	.banner-subtitle {
		font-size: 50rpx;
		color: black;
	}
}

.feature-list {
	background: #fff;
	border-radius: 20rpx;
	padding: 20rpx;
	margin-bottom: 30rpx;

	.feature-item {
		display: flex;
		align-items: center;
		padding: 20rpx;

		.feature-icon {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 36rpx;
			margin-right: 20rpx;

		}

		.lightning {
			width: 60rpx;
			height: 60rpx;

			.img {
				width: 100%;
				height: 100%;
			}
		}

		.doc {
			width: 60rpx;
			height: 60rpx;

			.img {
				width: 100%;
				height: 100%;
			}
		}

		.gift {
			width: 60rpx;
			height: 60rpx;

			.img {
				width: 100%;
				height: 100%;
			}
		}

		.feature-info {
			flex: 1;

			.feature-title {
				font-size: 38rpx;
				color: black;
				font-weight: 550;
				margin-bottom: 8rpx;
				display: block;
			}

			.feature-desc {
				font-size: 26rpx;
				color: #999;
			}
		}
	}
}

.publish-btn-wrapper {
	width: 90%;
	margin: 0 auto;

	// height: 300rpx;
	// border-radius: 190rpx;
	.publish-btn {
		background: #18cfcd;
		border-radius: 25rpx;
		// height: 120rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #fff;
		font-size: 37rpx;
		position: relative;
		border: none;
		line-height: center;

		.plus-icon {
			font-size: 40rpx;
			height: 110rpx;

			// margin-top: 40rpx;
			// margin-right: 10rpx;
			// display: inline-block;
			.img {
				width: 100%;
				height: 100%;
				// margin-top: 20rpx;
			}
		}

		.quick-tag {
			position: absolute;
			right: 0rpx;
			top: 0rpx;
			// transform: translateY(-50%);
			background: #ffeb3b;
			color: #333;
			font-size: 20rpx;
			padding: 0rpx 22rpx;
			border-radius: 0 0 0 20rpx;
			font-weight: 600;
		}
	}
}

.faq-section {
	background: #fff;
	border-radius: 20rpx;
	padding: 90rpx 40rpx 40rpx 40rpx;

	.faq-title {
		font-size: 27rpx;
		color: black;
		font-weight: bold;
		margin-bottom: 20rpx;
		display: block;
	}

	.faq-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		font-size: 30rpx;
		color: #848a94;
		// border-bottom: 1rpx solid #eee;

		&:last-child {
			border-bottom: none;
		}

		.arrow {
			color: #999;

			.img {
				width: 30rpx;
				height: 30rpx;
			}
		}
	}
}
</style>