<template>
    <view class="nearby">


        <view class="regis-list" v-if="getList.length == 0">
            <view v-for="(item, index) in mockList" :key="index" class="regis-listitem">
                <view class="listitem">
                    <view class="item-img">
                        <image src="@/static/app/home/<USER>" alt=""/>
                        <view style="margin-left: 30rpx;">
                            <p>
                                <span style="color: transparent; text-shadow: 0 0 10px rgba(0, 0, 0, 1);">{{ item.name
                                }}</span>
                                <span style="display: inline-block;margin-left: 30rpx;">{{ item.state }}</span>
                            </p>
                            <p style="margin-top: 10rpx;color: #a4aaae;">{{ item.age }} {{ item.degree }}</p>
                        </view>
                    </view>
                    <view>
                        <u-icon name="arrow-right"></u-icon>
                    </view>
                </view>
                <view class="list-but">
                    <view style="width: 30rpx;"></view>
                    <u-button class="cat-btn" text="立即邀约"></u-button>
                </view>
            </view>
        </view>
        <view class="regis-released" v-if="getList.length == 0">
            <p style="text-align: center;padding: 20rpx;font-size: 45rpx;color: white;">
                您与候选人之间只差一个职位
            </p>
            <p style="text-align: center;padding: 20rpx;color: #d5f7f8;">发布职位后还能获得加速招人等5项特权</p>
            <u-button class="released-btn" @click="released" text="发布职位"></u-button>
        </view>
        <view class="regis-title" @click="show1 = true" v-if="getList.length > 0">
            {{ positionName }} <u-icon name="arrow-down-fill" size="30"
                style="margin-top: 10rpx;margin-left: 10rpx;"></u-icon>
        </view>
        <view class="regis-invi" v-if="getList.length > 0">
            <view style="display: flex;">
                <image src="@/static/app/home/<USER>" alt=""/>
                <text style="margin-left: 10rpx;font-size: 35rpx;">今日剩余 0 次邀约</text>
            </view>
            <view style="display: flex;justify-content: center; margin-right: 20rpx;">去购买<u-icon
                    name="arrow-right"></u-icon></view>
        </view>

        <!-- 使用scroll-view实现上拉加载 -->
        <scroll-view
            class="regis-getlist-scroll"
            v-if="getList.length > 0"
            scroll-y="true"
            scroll-x="false"
            enable-flex="true"
            @scrolltolower="onLoadMore"
            :lower-threshold="100"
            :style="{ height: scrollViewHeight + 'px' }"
        >
            <view class="regis-getlist">
                <view v-for="(item, index) in getList" :key="index" class="regis-listitem">
                    <view class="listitem" @click="gotodetail(item)">
                        <view class="item-img">
                            <image src="@/static/app/home/<USER>" alt=""/>
                            <view style="margin-left: 30rpx;">
                                <p>
                                    <span>{{ item.username }}</span>
                                    <span style="display: inline-block;margin-left: 30rpx;">{{ item.state }}</span>
                                </p>
                                <p style="margin-top: 10rpx;color: #a4aaae;">{{ item.sex_name + '.' }} {{ item.age + '岁' }} {{ item.degree_name }}</p>
                            </view>
                        </view>
                        <view>
                            <u-icon name="arrow-right"></u-icon>
                        </view>
                    </view>
                    <view class="list-but">
                        <view>距工作地点 {{ item.distance }}km</view>
                        <u-button class="cat-btn" text="立即邀约"></u-button>
                    </view>
                </view>

                <!-- 加载状态提示 -->
                <view class="loading-status">
                    <view v-if="loading && getList.length > 0" class="loading-more">
                        <u-loading-icon mode="spinner" color="#02bdc4" size="32"></u-loading-icon>
                        <text class="loading-text">加载中...</text>
                    </view>
                    <view v-else-if="!hasMore && getList.length > 0" class="no-more">
                        <text>- 没有更多数据了 -</text>
                    </view>
                </view>
            </view>
        </scroll-view>

        <u-popup :show="show1" @close="close" @open="open" :round="20" mode="bottom">
            <view class="popup-content">
                <view class="popup-top">
                    <h2>切换职位</h2>
                    <uni-icons @click="show = false" type="closeempty" size="22"></uni-icons>
                </view>
                <view style="color: #afafb3;margin-top: 20rpx;">
                    已发布 {{ jobList.length || '0' }} 个职位，切换查看合适的人
                </view>
                <view class="popup-joblist" v-if="!loding">
                    <view class="taball" v-for="(item, index) in jobList" :key="index">
                        <view>
                            <view style="font-size: 40rpx;color: black;font-weight: 600;">{{ item.name }}</view>
                            <view style="color: #9a9ca4;margin-top: 10rpx; display: flex;align-items: center;">
                                <view style="padding: 3rpx 10rpx;margin-right: 10rpx;border: 1px solid #f5f5f5;">{{
                                    item.merge_status == 0 ? '已暂停' : item.merge_status == 1 ? '招聘中' : item.merge_status
                                        == 2
                                        ? '待审核' : '审核驳回' }}</view> 待处理候选人 {{ 0 }}
                            </view>
                        </view>
                        <view>
                            <u-button @click="selectJob(index, item)" class="code-btn" text="当前"
                                :class="{ activeJob: selectedJobId === index }"></u-button>
                        </view>
                    </view>
                </view>
                <view style="text-align: center;" v-else>
                    <u-loading-icon></u-loading-icon>
                    <view style="color: #9a9ca4;">加载中...</view>
                </view>
            </view>
        </u-popup>
    </view>
</template>
<script>
import { positionsApi, applyApi } from "@/utils/api"
export default {
    props: {
        nearList: {
            type: [Array, Object], // 允许接收数组或对象
            default: () => [] // 默认值为空数组

        },
        Position: {
            type: [Array, Object], // 允许接收数组或对象
            default: () => [] // 默认值为空数组
        },
        job_id:{
            type:Number,
            default:0
        }
    },
    watch: {
        nearList: {
            immediate: true, // 初始化时立即执行
            handler(newVal) {
                uni.showLoading({
                    title: '加载中'
                });
                // console.log('接收到的数据:', newVal);
                this.getList = newVal || []

                // 初始化分页状态
                if (newVal && newVal.length > 0) {
                    this.total = newVal.length
                    this.hasMore = newVal.length >= this.size
                }

                setTimeout(function () {
                    uni.hideLoading();
                }, 500);
            }
        },
        Position: {
            immediate: true, // 初始化时立即执行
            handler(newVal) {
                if(newVal !=''){
                    this.positionName = newVal[0].name
                }

            }
        },
        job_id:{
            immediate:true,
            handler(newVal){
                console.log(newVal,'$$$')
                this.job_ids = newVal
            }
        }
    },
    data() {
        return {
            show1: false,
            loding: false,
            getList: [
            ],
            mockList: [
                {
                    name: '111',
                    state: '活跃中',
                    age: '女',
                    degree: '硕士'
                },
                {
                    name: '111',
                    state: '刚刚活跃',
                    age: '男',
                    degree: '高中'
                },
                {
                    name: '111',
                    state: '活跃中',
                    age: '女',
                    degree: '硕士'
                },
                {
                    name: '111',
                    state: '刚刚活跃',
                    age: '男',
                    degree: '高中'
                },
                {
                    name: '111',
                    state: '活跃中',
                    age: '女',
                    degree: '硕士'
                },
                {
                    name: '111',
                    state: '刚刚活跃',
                    age: '男',
                    degree: '高中'
                }
            ],
            jobList: [],
            selectedJobId: 0,
            positionName: '',
            page: 1,
            size: 10,
            job_ids: null,
            // 上拉加载相关属性
            loading: false,      // 是否正在加载
            hasMore: true,       // 是否还有更多数据
            total: 0,            // 总数据量
            scrollViewHeight: 0, // 滚动容器高度
            currentJobId: null   // 当前选中的职位ID
        };
    },
    mounted() {
        // 计算滚动容器高度
        this.calculateScrollHeight();
    },
    methods: {
        gotodetail(item) {
            console.log(item)
            uni.navigateTo({
                url: `./nearbydetail/index?item=${encodeURIComponent(JSON.stringify(item))}`
            });
        },
        released() {
            uni.navigateTo({
                url: '/pages/second/second'
            });
        },
        async open() {
            const params = {
                page: 1,
                status: 100,
                status: 1
            }
            let res = await positionsApi.postJobList(params)
            this.jobList = res.data.data
            this.loding == false
        },
        close() {
            this.show1 = false
            uni.hideLoading()
            // console.log('close');
        },
        async selectJob(index, item) {
            console.log(index, item)
            this.selectedJobId = index
            this.positionName = item.name
            this.show1 = false

            // 重置分页状态
            this.page = 1
            this.hasMore = true
            this.currentJobId = item.id

            const param = {
                page: this.page,
                size: this.size,
                job_id: item.id
            }

            try {
                let res = await applyApi.getRecommend(param)
                if (res.code === 200) {
                    this.getList = res.data.data || []
                    this.total = res.data.total || 0

                    // 判断是否还有更多数据
                    this.hasMore = this.getList.length >= this.size && this.getList.length < this.total

                    console.log(`切换职位完成: 当前${this.getList.length}条，总共${this.total}条，还有更多: ${this.hasMore}`)
                }
                uni.hideLoading()
            } catch (error) {
                console.error('获取推荐数据失败:', error)
                uni.showToast({
                    title: '获取数据失败',
                    icon: 'none',
                    duration: 1500
                })
            }
            uni.hideLoading()
        },

        // 上拉加载更多
        async onLoadMore() {
            console.log('上拉加载更多')
            if (this.loading || !this.hasMore) {
                console.log('正在加载或没有更多数据')
                return
            }

            this.loading = true
            this.page++

            try {
                const param = {
                    page: this.page,
                    size: this.size,
                    job_id: this.currentJobId || this.job_ids
                }

                console.log('上拉加载请求参数:', param)
                let res = await applyApi.getRecommend(param)

                if (res.code === 200) {
                    const newData = res.data.data || []
                    this.total = res.data.total || 0

                    // 追加新数据
                    this.getList = [...this.getList, ...newData]

                    // 判断是否还有更多数据
                    this.hasMore = newData.length >= this.size && this.getList.length < this.total

                    console.log(`上拉加载完成: 当前${this.getList.length}条，总共${this.total}条，还有更多: ${this.hasMore}`)
                } else {
                    throw new Error(res.msg || '获取数据失败')
                }
            } catch (error) {
                console.error('上拉加载失败:', error)
                this.page-- // 加载失败时回退页码
                uni.showToast({
                    title: '加载失败',
                    icon: 'none',
                    duration: 1500
                })
            } finally {
                this.loading = false
            }
            uni.hideLoading()
        },

        // 计算滚动容器高度
        calculateScrollHeight() {
            uni.getSystemInfo({
                success: (res) => {
                    // 计算可用高度：屏幕高度 - 状态栏 - 导航栏 - 底部固定区域
                    const windowHeight = res.windowHeight
                    const statusBarHeight = res.statusBarHeight || 0
                    const navigationBarHeight = 44 // 导航栏高度
                    const fixedAreaHeight = 230 // 顶部固定区域高度(rpx转px)

                    this.scrollViewHeight = windowHeight - statusBarHeight - navigationBarHeight - (fixedAreaHeight * res.windowWidth / 750)
                    console.log('计算的滚动容器高度:', this.scrollViewHeight)
                }
            })
        },
    }
}
</script>
<style lang="scss" scoped>
.nearby {
    // height: 100%;
    overflow-y: auto;
    position: relative;

}

.regis-released {
    width: 100%;
    height: 360rpx;
    position: absolute;
    bottom: 0rpx;
    background-color: #0dc9d5;
    border-radius: 30rpx 30rpx 0rpx 0rpx;

    .released-btn {
        width: 90%;
        border-radius: 20rpx;
        height: 120rpx;
    }
}

.regis-title {
    padding: 20rpx;
    display: flex;
}

.regis-invi {
    width: 96%;
    margin: 0 auto;
    padding: 20rpx 0rpx;
    // padding: 20rpx;
    display: flex;
    background-color: white;
    align-items: center;
    justify-content: space-between;
    border-radius: 20rpx;

    image {
        width: 50rpx;
        height: 50rpx;
        margin-left: 20rpx;
    }
}

.regis-list {
    height: calc(100% - 370rpx);
    overflow-y: auto;
    padding: 20rpx;

    .regis-listitem {
        height: 270rpx;
        background-color: white;
        margin-top: 20rpx;
        border-radius: 20rpx;
        padding: 20rpx 20rpx;
        margin-bottom: 30rpx;

        .listitem {
            height: 100rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20rpx;

            .item-img {
                display: flex;

                image {
                    width: 100rpx;
                    height: 100rpx;
                }
            }
        }

        .list-but {
            width: 100%;
            margin-top: 45rpx;
            display: flex;

            // justify-content: space-between;
            .cat-btn {
                width: 250rpx;
                background-color: #02bdc6;
                color: white;
                font-size: 40rpx;
                border-radius: 20rpx;
                margin-right: 30rpx;
            }

        }

    }

}

.regis-getlist-scroll {
    padding: 20rpx;
    width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;
}

.regis-getlist {
    width: 100%;
    box-sizing: border-box;

    .regis-listitem {
        width: 100%;
        height: 270rpx;
        background-color: white;
        margin-top: 20rpx;
        border-radius: 20rpx;
        padding: 20rpx 20rpx;
        margin-bottom: 30rpx;
        box-sizing: border-box;

        .listitem {
            height: 100rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20rpx;

            .item-img {
                display: flex;

                image {
                    width: 100rpx;
                    height: 100rpx;
                }
            }
        }

        .list-but {
            width: 100%;
            margin-top: 45rpx;
            display: flex;
            align-items: center;
            font-size: 30rpx;
            color: #b3b8bb;

            .cat-btn {
                width: 250rpx;
                background-color: #02bdc6;
                color: white;
                font-size: 40rpx;
                border-radius: 20rpx;
                margin-right: 30rpx;
            }
        }
    }
}

// 加载状态样式
.loading-status {
    padding: 30rpx 0;
    text-align: center;

    .loading-more {
        display: flex;
        justify-content: center;
        align-items: center;
        color: #666;

        .loading-text {
            margin-left: 20rpx;
            font-size: 28rpx;
        }
    }

    .no-more {
        color: #999;
        font-size: 28rpx;
    }
}

.popup-content {
    height: 1200rpx;
    padding: 30rpx 20rpx;
    background-color: #f6f7f9;

    .popup-top {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .code-btn::after {
        border: none !important;
    }

    // .popup-taball {
    //     display: flex;
    //     height: 80rpx;
    //     display: flex;
    //     align-items: center;


    // }

    .taball {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 40rpx 30rpx;
        background-color: white;
        border-radius: 20rpx;
        margin-top: 20rpx;

        .code-btn {
            width: 200rpx;
            margin-left: 30rpx;
            border: none !important;
            background-color: #f6f7f9;
            border-radius: 20rpx;
            color: #989fa4;
            font-weight: 600;
        }

        .activeJob {
            background-color: #02bdc4 !important;
            color: white;
            /* 高亮边框 */
        }
    }

    .popup-botom {
        display: flex;
        padding: 30rpx 0;
        color: black;
    }

    .popup-joblist {
        height: calc(100% - 150rpx);
        overflow: hidden;
        overflow-y: auto;
        margin-top: 20rpx;
    }
}

::v-deep .uni-button {
    margin-right: none !important;
}

::v-deep .u-button__text span {
    font-size: 33rpx;
}
</style>