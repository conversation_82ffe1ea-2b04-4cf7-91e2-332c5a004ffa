<template>
    <view class="page-container">
        <!-- 顶部占位，如果使用自定义导航栏则需要 -->
        <!-- <view class="nav-bar-placeholder"></view> -->
        <u-navbar :autoBack="true" title="招聘数据" :titleStyle="{
            color: '#222',
            fontWeight: 'bold',
            fontSize: '36rpx'
        }" :leftIconSize="30" bgColor="#ffffff" :leftIconColor="'#222'" fixed safeAreaInsetTop placeholder></u-navbar>
        <!-- 主体内容 -->
        <view class="content-wrapper">
            <!-- 今日数据标题和标签 -->
            <view class="section-header">
                <text class="section-title">今日数据</text>
                <view class="job-tag">
                    <text v-if="getSelectedJob" @click="show = true">{{ getSelectedJob.name }} </text>
                    <text v-else @click="show = true">请选择职位</text>
                    <text class="lightning-icon">⚡️</text>
                </view>
            </view>

            <!-- 数据卡片网格 -->
            <view class="data-grid">
                <view v-for="(card, index) in dataCards" :key="index" class="data-card"
                    :class="'card-icon-' + (index + 1)">
                    <view class="card-content">
                        <text class="card-title">{{ card.title }}</text>
                        <text class="card-value">{{ card.value }}</text>
                        <view class="card-comparison">
                            <text class="compare-text">较昨日</text>
                            <text class="compare-value">{{ card.comparison }}</text>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 底部条形图 -->
            <view class="chart-section">
                <view class="chart-container">
                    <view v-for="(bar, index) in chartData" :key="index" class="chart-bar-wrapper">
                        <view class="chart-bar" :style="{ height: bar.height }"></view>
                    </view>
                </view>
                <view class="chart-axis-line"></view>
            </view>
        </view>

        <u-popup :show="show" @close="closelist" @open="openlist" :round="20" mode="bottom">
            <view class="popup-content">
                <view class="popup-top">
                    <h2>选择置顶职位</h2>
                    <uni-icons @click="show = false" type="closeempty" size="22"></uni-icons>
                </view>
                <scroll-view class="popup-joblist-scroll" v-if="!loding" scroll-y="true"
                    @scrolltolower="onJobListLoadMore" :lower-threshold="100" style="height: 400rpx;">
                    <view class="popup-joblist">
                        <view class="taball" v-for="(item, index) in jobList" :key="index" @click="handleSelect(item)">
                            <uni-icons :type="selectedJobId === item.id ? 'checkbox' : 'circle'"
                                :color="selectedJobId === item.id ? '#02bdc4' : '#dcdfe6'" size="30"></uni-icons>
                            <view style="color: #535353;font-size: 30rpx;color: black;">{{ item.name }} . {{
                                item.city_name }} . {{ item.min_salary }} - {{ item.max_salary }}</view>
                        </view>

                        <!-- 职位列表加载状态提示 -->
                        <view class="job-loading-status">
                            <view v-if="jobListLoading && jobList.length > 0" class="loading-more">
                                <u-loading-icon mode="spinner" color="#02bdc4" size="32"></u-loading-icon>
                                <text class="loading-text">加载中...</text>
                            </view>
                            <view v-else-if="!jobListHasMore && jobList.length > 0" class="no-more">
                                <text>- 没有更多职位了 -</text>
                            </view>
                        </view>
                    </view>
                </scroll-view>
                <view style="text-align: center;" v-else>
                    <u-loading-icon></u-loading-icon>
                    <view style="color: #9a9ca4;">加载中...</view>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
// 假设您的 api.js 和 vuex store 配置是正确的
import { positionsApi, recru } from "@/utils/api.js";
import { mapGetters, mapActions } from 'vuex';

export default {
    data() {
        return {
            // 卡片数据
            dataCards: [
                { title: '我看过', value: 0, comparison: '+0' },
                { title: '看过我', value: 0, comparison: '+0' },
                { title: '职位面试', value: 0, comparison: '+0' },
                { title: '收到简历', value: 0, comparison: '+0' },
                { title: '收到手机号', value: 0, comparison: '+0' },
                { title: '收到微信号', value: 0, comparison: '+0' },
                { title: '收藏人数', value: 0, comparison: '+0' },
                { title: '沟通数', value: 0, comparison: '+0' }
            ],
            // 图表数据
            chartData: [
                { height: '60%' },
                { height: '85%' },
                { height: '40%' },
                { height: '55%' },
                { height: '80%' },
                { height: '35%' },
            ],
            show: false,
            jobList: [],
            jobListLoading: false,
            jobListHasMore: true,
            pagelate: 1,
            pagesize: 10,
            totallate: 0,
            loding: false,
            selectedJobId: null,
            recruitmen: {}
        };
    },
    computed: {
        ...mapGetters(['getSelectedJob']),
    },
    // onLoad 在页面首次加载时执行，最适合处理初始化逻辑
    onLoad() {
        this.initializePage();
    },
    // onShow 在每次页面显示时执行，确保数据同步
    onShow() {
        // 如果 Vuex 中有值，确保本地的 selectedJobId 与之同步
        if (this.getSelectedJob) {
            this.selectedJobId = this.getSelectedJob.id;
        }
    },
    methods: {
        ...mapActions(['selectJob']),

        // 页面初始化逻辑
        async initializePage() {
            // 检查 Vuex 中是否已经有选中的职位
            if (this.getSelectedJob) {
                console.log("Vuex中存在已选中的职位，直接使用:", this.getSelectedJob);
                this.selectedJobId = this.getSelectedJob.id;
                // 此处可以根据已选中的职位ID去获取对应的招聘数据
                // await this.fetchRecruitmentData(this.selectedJobId);
                const res = await recru.jobDataCount({
                    job_id: this.selectedJobId
                })
                if (res.code == 200) {
                    this.recruitmen = res.data
                    this.dataCards = [
                        { title: '我看过', value: res.data.countSawMy || 0, comparison: '+' + res.data.newlyIncreasedSawMy || '+0' },
                        { title: '看过我', value: res.data.countSawYou, comparison: '+' + res.data.newlyIncreasedSawYou || '+0' },
                        { title: '职位面试', value: res.data.countAnAudition, comparison: '+' + res.data.newlyIncreasedAnAudition || '+0' },
                        { title: '收到简历', value: res.data.countVitae, comparison: '+' + res.data.newlyIncreasedVitae || '+0' },
                        { title: '收到手机号', value: res.data.countPhone, comparison: '+' + res.data.newlyIncreasedPhone || '+0' },
                        { title: '收到微信号', value: res.data.countWechat, comparison: '+' + res.data.newlyIncreasedWechat || '+0' },
                        { title: '收藏人数', value: res.data.countCollect, comparison: '+' + res.data.newlyIncreasedCollect || '+0' },
                        { title: '沟通数', value: res.data.countCommunicate, comparison: '+' + res.data.newlyIncreasedCommunicate || '+0' }
                    ]
                    uni.hideLoading()
                } else {
                    uni.showToast({
                        title: '获取职位数据失败',
                        icon: 'error'
                    });
                }
                uni.hideLoading()
            } else {
                console.log("Vuex中无选中职位，开始请求职位列表以设置默认值。");
                // 如果没有，则请求职位列表并设置第一个为默认
                await this.fetchAndSetDefaultJob();
            }
        },

        // 获取职位列表并设置默认的第一个
        async fetchAndSetDefaultJob() {
            this.loding = true;
            try {
                const params = { page: 1, size: 10, status: 4 };
                let res = await positionsApi.postJobList(params);

                if (res.code === 200 && res.data && res.data.data.length > 0) {
                    const jobListData = res.data.data;
                    const firstJob = jobListData[0]; // 获取第一个职位

                    console.log("获取职位列表成功，将设置第一个职位为默认:", firstJob);

                    // 将第一个职位信息存储到 Vuex
                    this.selectJob(firstJob);

                    // 更新本地的 selectedJobId 用于UI高亮
                    this.selectedJobId = firstJob.id;

                    // 此时可以根据新设置的职位ID获取招聘数据
                    // await this.fetchRecruitmentData(this.selectedJobId);
                    uni.hideLoading();

                } else {
                    console.log("获取职位列表为空或失败");
                }
            } catch (error) {
                console.error('初始化获取职位列表失败:', error);
            } finally {
                this.loding = false;
            }
        },

        // 点击职位标签，打开弹窗
        async openlist() {
            this.show = true; // 先打开弹窗
            this.loding = true; // 显示加载状态

            // 重置职位列表的分页和状态
            this.pagelate = 1;
            this.jobList = [];
            this.jobListHasMore = true;

            try {
                const params = { page: this.pagelate, size: this.pagesize, status: 4 };
                let res = await positionsApi.postJobList(params);

                if (res.code === 200) {
                    this.jobList = res.data.data || [];
                    this.totallate = res.data.total || 0;
                    this.jobListHasMore = this.jobList.length < this.totallate;
                } else {
                    uni.showToast({ title: '加载失败', icon: 'none' });
                }
                uni.hideLoading()
            } catch (error) {
                console.error('打开弹窗时加载职位列表失败:', error);
                uni.showToast({ title: '加载失败', icon: 'none' });
            } finally {
                this.loding = false;
            }
        },

        // 弹窗中选中一个职位
        async handleSelect(selectedItem) {
            console.log("用户选择了新职位:", selectedItem);
            const res = await recru.jobDataCount({
                job_id: selectedItem.id
            })
            if (res.code == 200) {
                this.recruitmen = res.data
                this.dataCards = [
                    { title: '我看过', value: res.data.countSawMy || 0, comparison: '+' + res.data.newlyIncreasedSawMy || '+0' },
                    { title: '看过我', value: res.data.countSawYou, comparison: '+' + res.data.newlyIncreasedSawYou || '+0' },
                    { title: '职位面试', value: res.data.countAnAudition, comparison: '+' + res.data.newlyIncreasedAnAudition || '+0' },
                    { title: '收到简历', value: res.data.countVitae, comparison: '+' + res.data.newlyIncreasedVitae || '+0' },
                    { title: '收到手机号', value: res.data.countPhone, comparison: '+' + res.data.newlyIncreasedPhone || '+0' },
                    { title: '收到微信号', value: res.data.countWechat, comparison: '+' + res.data.newlyIncreasedWechat || '+0' },
                    { title: '收藏人数', value: res.data.countCollect, comparison: '+' + res.data.newlyIncreasedCollect || '+0' },
                    { title: '沟通数', value: res.data.countCommunicate, comparison: '+' + res.data.newlyIncreasedCommunicate || '+0' }
                ]
                uni.hideLoading()
            } else {
                uni.showToast({
                    title: '获取职位数据失败',
                    icon: 'error'
                });
            }
            // 更新 Vuex 中的数据
            this.selectJob(selectedItem);

            // 更新本地的 selectedJobId
            this.selectedJobId = selectedItem.id;

            // 关闭弹窗
            this.show = false;

            // 选择新职位后，应该重新获取该职位的招聘数据
            // this.fetchRecruitmentData(this.selectedJobId);
        },

        // 弹窗列表滚动到底部加载更多
        async onJobListLoadMore() {
            if (this.jobListLoading || !this.jobListHasMore) return;

            this.jobListLoading = true;
            this.pagelate++;

            try {
                const params = { page: this.pagelate, size: this.pagesize, status: 4 };
                let res = await positionsApi.postJobList(params);

                if (res.code === 200) {
                    const newJobList = res.data.data || [];
                    this.jobList = [...this.jobList, ...newJobList];
                    this.jobListHasMore = this.jobList.length < this.totallate;
                }
            } catch (error) {
                console.error('职位列表加载更多失败:', error);
                this.pagelate--; // 失败回滚页码
            } finally {
                this.jobListLoading = false;
            }
        },

        closelist() {
            this.show = false;
        },
    }
}
</script>

<style scoped>
/* 页面整体容器 */
.page-container {
    min-height: 100vh;
    background: linear-gradient(to bottom, #f0f8ff 20%, #fffff0 80%);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.content-wrapper {
    padding: 16px;
}

/* 章节头部 */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.job-tag {
    display: flex;
    align-items: center;
    background-color: #ff9a3f;
    color: white;
    padding: 4px 10px;
    border-radius: 16px;
    font-size: 13px;
}

.lightning-icon {
    font-size: 10px;
    margin-left: 4px;
}

/* 数据卡片网格 */
.data-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

/* 单个数据卡片 */
.data-card {
    width: calc(50% - 8px);
    /* 减去 gap */
    background-color: #ffffff;
    border-radius: 16px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
}

.card-content {
    position: relative;
    z-index: 1;
}

.card-title {
    display: block;
    font-size: 15px;
    color: #666;
}

.card-value {
    display: block;
    font-size: 32px;
    font-weight: bold;
    color: #333;
    margin-top: 4px;
}

.card-comparison {
    display: flex;
    align-items: center;
    font-size: 12px;
    margin-top: 4px;
}

.compare-text {
    color: #999;
}

.compare-value {
    color: #fa5151;
    margin-left: 4px;
}

/* 卡片背景图标伪元素 */
.data-card::after {
    content: '';
    position: absolute;
    right: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 60px;
    height: 60px;
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0.1;
    z-index: 0;
}

/* 
 * 您需要将下面的 background-image 的 url() 替换成您自己的图标路径
 * 为了预览方便，此处使用内联SVG作为占位符
*/
.card-icon-1::after {
    /* 看过我 - 眼睛 */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z'%3E%3C/path%3E%3C/svg%3E");
}

.card-icon-2::after {
    /* 看过我 - 眼睛 */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z'%3E%3C/path%3E%3C/svg%3E");
}

.card-icon-3::after {
    /* 我打招呼 - 点赞 */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M21 7h-6.31l.95-4.57.03-.32c0-.41-.17-.79-.44-1.06L14.17 0 7.59 6.59C7.22 6.95 7 7.45 7 8v10c0 1.1.9 2 2 2h9c.83 0 1.54-.5 1.84-1.22l3.02-7.05c.09-.23.14-.47.14-.73v-2c0-1.1-.9-2-2-2zM5 8H1v12h4V8z'%3E%3C/path%3E%3C/svg%3E");
}

.card-icon-4::after {
    /* 牛人打招呼 - 笑脸 */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm3.5-9c.83 0 1.5-.67 1.5-1.5S16.33 8 15.5 8 14 8.67 14 9.5s.67 1.5 1.5 1.5zm-7 0c.83 0 1.5-.67 1.5-1.5S9.33 8 8.5 8 7 8.67 7 9.5 7.67 11 8.5 11zm3.5 6.5c2.33 0 4.31-1.46 5.11-3.5H6.89c.8 2.04 2.78 3.5 5.11 3.5z'%3E%3C/path%3E%3C/svg%3E");
}

.card-icon-5::after {
    /* 我沟通 - 聊天 */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z'%3E%3C/path%3E%3C/svg%3E");
}

.card-icon-6::after {
    /* 收获简历 - 用户 */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'%3E%3C/path%3E%3C/svg%3E");
}

.card-icon-7::after {
    /* 交换微信 - 微信图标简化 */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M20.45 5.23c-2.18-2.1-5.3-3.23-8.45-3.23C6.44 2 2 6.44 2 12c0 2.23.77 4.3 2.07 5.96L2 22l4.35-2.04c1.58.98 3.42 1.54 5.4 1.54 6.63 0 10.25-5.46 10.25-11.5 0-2.9-1.25-5.59-3.55-7.77zm-3.8 8.12c-.24.72-1.46 1.34-1.92 1.38-.46.04-1.02.04-1.56-.22-.54-.26-1.14-.57-2.18-1.34-1.04-.77-1.9-1.77-2.22-2.38-.32-.61-.06-1 .24-1.3.3-.3.66-.4.84-.4.18 0 .36.02.48.04.12.02.24.42.36.66.12.24.18.42.06.66-.12.24-.18.3-.36.48-.18.18-.36.42-.24.66.12.24.6 1.08 1.38 1.74.78.66 1.32.72 1.56.84.24.12.36.12.54-.06.18-.18.72-.84.9-1.08.18-.24.36-.24.6-.12.24.12.84.72 1.02.84.18.12.24.24.18.42z'%3E%3C/path%3E%3C/svg%3E");
}

.card-icon-8::after {
    /* 接受面试 - 奖杯 */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M20.2 3H3.8C3.36 3 3 3.36 3 3.8v3.8c0 .44.36.8.8.8h16.4c.44 0 .8-.36.8-.8V3.8c0-.44-.36-.8-.8-.8zM18 11h-2v2c0 .55-.45 1-1 1s-1-.45-1-1v-2h-4v2c0 .55-.45 1-1 1s-1-.45-1-1v-2H6v9h12v-9zM12 4c-1.66 0-3 1.34-3 3h6c0-1.66-1.34-3-3-3z'%3E%3C/path%3E%3C/svg%3E");
}

/* 图表部分 */
.chart-section {
    margin-top: 30px;
    padding: 0 10px;
}

.chart-container {
    height: 120px;
    display: flex;
    justify-content: space-around;
    align-items: flex-end;
}

.chart-bar-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column-reverse;
    /* 使 bar 从底部开始 */
}

.chart-bar {
    width: 15px;
    background-color: #ffb44f;
    border-radius: 8px 8px 0 0;
    transition: height 0.5s ease-out;
}

.chart-axis-line {
    height: 2px;
    width: 100%;
    background-color: #ffb44f;
    border-radius: 2px;
    margin-top: 2px;
}


.popup-content {
    padding: 20rpx 0;
}

.popup-header {
    text-align: center;
    padding: 20rpx 0;
    position: relative;

    .popup-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #303133;
    }
}

.price-section {
    text-align: center;
    padding: 40rpx 0;

    .currency-symbol {
        font-size: 40rpx;
        font-weight: bold;
        color: #303133;
    }

    .amount-text {
        font-size: 72rpx;
        font-weight: bold;
        color: #303133;
        margin-left: 8rpx;
    }
}

.payment-list {
    padding: 0 40rpx;
}

.payment-item {
    display: flex;
    align-items: center;
    padding: 30rpx 0;
    border-bottom: 1rpx solid #f5f5f5;

    &:last-child {
        border-bottom: none;
    }

    .payment-icon {
        width: 48rpx;
        height: 48rpx;
        margin-right: 24rpx;
    }

    .payment-info {
        flex: 1;
        display: flex;
        flex-direction: column;

        .payment-name {
            font-size: 30rpx;
            color: #303133;
        }

        .points-balance {
            font-size: 24rpx;
            color: #909399;
            margin-top: 4rpx;
        }
    }
}

.popup-footer {
    padding: 40rpx;

    .confirm-btn {
        height: 90rpx;
        line-height: 90rpx;
        border-radius: 45rpx;
        font-size: 32rpx;
        font-weight: 500;
        color: #ffffff;
        background: #00c8a0;
        border: none;
        background: linear-gradient(90deg, #00d2af, #00c8a0);

        &::after {
            border: none;
        }

        &:active {
            opacity: 0.8;
        }
    }
}

.popup-content {
    height: 800rpx;
    padding: 30rpx 20rpx;
    background-color: #f6f7f9;

    .popup-top {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .code-btn::after {
        border: none !important;
    }


    .taball {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 40rpx 30rpx;
        background-color: white;
        border-radius: 20rpx;
        margin-top: 20rpx;

        .code-btn {
            width: 200rpx;
            margin-left: 30rpx;
            border: none !important;
            background-color: #f6f7f9;
            border-radius: 20rpx;
            color: #989fa4;
            font-weight: 600;
        }

        .activeJob {
            background-color: #02bdc4 !important;
            color: white;
            /* 高亮边框 */
        }
    }

    .popup-botom {
        display: flex;
        padding: 30rpx 0;
        color: black;
    }

    .popup-joblist-scroll {
        width: 100%;
        height: calc(100% - 100rpx) !important;
        margin-top: 30rpx;
    }

    .popup-joblist {
        /* 职位列表容器样式 */
    }

    .job-loading-status {
        padding: 30rpx 0;
        text-align: center;

        .loading-more {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .loading-text {
                margin-top: 15rpx;
                font-size: 26rpx;
                color: #999;
            }
        }

        .no-more {
            padding: 15rpx 0;
            font-size: 26rpx;
            color: #999;
            text-align: center;
        }
    }
}
</style>