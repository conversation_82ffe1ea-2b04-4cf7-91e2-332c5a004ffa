<template>
    <view class="cancellation-page">
        <!-- 顶部导航栏 -->
        <view class="header">
            <u-navbar height="44px" title="注销账号" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>

        <!-- 页面内容 -->
        <view class="content">
            <!-- 注销说明卡片 -->
            <view class="notice-card">

                <view class="notice-text">
                    感谢您一直以来的陪伴！注销账号前，我们需要对以下信息进行审核，以保证您的账号安全
                </view>
                <view class="notice-icon">
                    <image src="@/static/app/audit/image.png" class="robot-image" mode="aspectFit"></image>
                </view>
            </view>

            <!-- 注销条款列表 -->
            <view class="terms-list">
                <view class="terms-item" v-for="(item, index) in termsList" :key="index">
                    <text class="terms-number">{{ index + 1 }}.</text>
                    <text class="terms-text">{{ item }}</text>
                </view>
            </view>

        </view>

        <!-- 底部按钮 -->
        <view class="footer-section">
            <!-- 同意条款 -->
            <view class="agreement-section">
                <view class="agreement-item" @click="toggleAgreement">
                    <view class="checkbox" :class="{ 'checked': isAgreed }">
                        <text class="check-icon" v-if="isAgreed">✓</text>
                    </view>
                    <text class="agreement-text">
                        我已阅读并同意《用户协议》及《隐私政策》，申请注销账号时需要验证身份信息，以保障您的账号安全。
                    </text>
                </view>
                <view class="link-text">
                    查看《<text class="link" @click="agreement">账号注销协议</text>》
                </view>
            </view>

            <button class="cancel-btn" :class="{ 'active': isAgreed }" :disabled="!isAgreed"
                @click="handleCancellation">
                已知风险，申请注销
            </button>
        </view>
    </view>
</template>

<script>
import { cancel } from "@/utils/api"
export default {
    data() {
        return {
            isAgreed: false,
            termsList: [
                '自愿放弃在平台的所有产品服务权益',
                '账号关闭后无法重新激活',
                '账号关闭后内容将无法恢复',
                '无法在其他中心设备等第三方设备登录',
                '账号与身份信息解绑，并清除相关数据',
                '账号内不存在未完成的产品交易',
                '账号内未完成的订单将无法履行',
                '账号内未完成的服务将终止'
            ]
        }
    },
    methods: {
        // 返回上一页
        goBack() {
            uni.navigateBack();
        },

        // 切换同意状态
        toggleAgreement() {
            this.isAgreed = !this.isAgreed;
        },
        agreement() {
            uni.navigateTo({
                url: '/pages/user/settings/emitpassword/canceagr'
            })
        },
        // 处理注销申请
        handleCancellation() {
            if (!this.isAgreed) {
                uni.showToast({
                    title: '请先同意用户协议',
                    icon: 'none'
                });
                return;
            }

            uni.showModal({
                title: '确认注销',
                content: '您确定要申请注销账号吗？此操作不可撤销。',
                success: (res) => {
                    if (res.confirm) {
                        // 这里调用注销API
                        this.submitCancellation();
                    }
                }
            });
        },

        // 提交注销申请
        async submitCancellation() {
            try {
                uni.showLoading({
                    title: '提交中...'
                });

                // 这里应该调用注销API
                const res = await cancel.cancelOrder();
                if (res.code === 200) {
                    // 模拟API调用
                    setTimeout(() => {
                        uni.hideLoading();
                        uni.showToast({
                            title: '注销申请已提交',
                            icon: 'success'
                        });

                        // 跳转到结果页面或返回
                        setTimeout(() => {
                            uni.navigateBack();
                        }, 1500);
                    }, 2000);
                }else{
                    uni.showToast({
                        title: res.msg,
                        duration: 2000
                    });
                }


            } catch (error) {
                uni.hideLoading();
                uni.showToast({
                    title: '提交失败，请重试',
                    icon: 'none'
                });
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.cancellation-page {
    min-height: 100vh;
    background-color: #f8f9fa;
    font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    padding-bottom: 200rpx;
    /* 为固定底部区域预留更多空间 */
}

/* 顶部导航栏 */
.nav-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    background: #ffffff;
    border-bottom: 1rpx solid #e9ecef;
    padding: 0 30rpx;
    position: sticky;
    top: 0;
    z-index: 100;

    .nav-left {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        .back-icon {
            font-size: 40rpx;
            color: #333333;
            font-weight: 300;
        }
    }

    .nav-title {
        font-size: 36rpx;
        color: #333333;
        font-weight: 600;
    }

    .nav-right {
        width: 60rpx;
    }
}

/* 页面内容 */
.content {
    padding: 30rpx;
}

/* 注销说明卡片 */
.notice-card {
    background: #ffffff;
    border-radius: 16rpx;
    // padding: 30rpx;
    margin-bottom: 30rpx;
    display: flex;
    align-items: flex-start;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

    .notice-icon {
        width: 160rpx;
        height: 160rpx;
        // margin-right: 20rpx;
        flex-shrink: 0;

        .robot-image {
            width: 100%;
            height: 100%;
        }
    }

    .notice-text {
        flex: 1;
        font-size: 28rpx;
        color: #666666;
        line-height: 1.6;
    }
}

/* 条款列表 */
.terms-list {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

    .terms-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20rpx;

        &:last-child {
            margin-bottom: 0;
        }

        .terms-number {
            font-size: 30rpx;
            color: #333333;
            margin-right: 10rpx;
            flex-shrink: 0;
            font-weight: 500;
        }

        .terms-text {
            flex: 1;
            font-size: 30rpx;
            color: #666666;
            line-height: 1.6;
        }
    }
}

/* 同意条款区域 - 现在在底部区域内 */
.agreement-section {
    padding: 20rpx 0;
    margin-bottom: 20rpx;

    .agreement-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 16rpx;
        cursor: pointer;

        .checkbox {
            width: 36rpx;
            height: 36rpx;
            border: 2rpx solid #d9d9d9;
            border-radius: 4rpx;
            margin-right: 16rpx;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;

            &.checked {
                background-color: #1890ff;
                border-color: #1890ff;

                .check-icon {
                    color: #ffffff;
                    font-size: 24rpx;
                    font-weight: bold;
                }
            }
        }

        .agreement-text {
            flex: 1;
            font-size: 26rpx;
            color: #666666;
            line-height: 1.6;
        }
    }

    .link-text {
        font-size: 26rpx;
        color: #666666;
        margin-left: 52rpx;

        .link {
            color: #1890ff;
            text-decoration: underline;
        }
    }
}

/* 底部区域 - 包含同意条款和按钮 */
.footer-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #ffffff;
    padding: 20rpx 30rpx;
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
    z-index: 100;
    /* 适配iPhone X等底部安全区域 */
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    .cancel-btn {
        width: 100%;
        height: 88rpx;
        background-color: #d9d9d9;
        color: #999999;
        border: none;
        border-radius: 44rpx;
        font-size: 32rpx;
        font-weight: 500;
        transition: all 0.3s ease;
        margin-bottom: 30rpx;

        &::after {
            border: none;
        }

        &.active {
            background-color: #ff4d4f;
            color: #ffffff;

            &:active {
                transform: scale(0.98);
                background-color: #ff7875;
            }
        }

        &:disabled {
            background-color: #d9d9d9;
            color: #999999;
        }
    }
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
    .notice-card {
        padding: 24rpx;

        .notice-text {
            font-size: 26rpx;
        }
    }

    .terms-list {
        padding: 24rpx;

        .terms-item {

            .terms-number,
            .terms-text {
                font-size: 30rpx;
            }
        }
    }

    .agreement-section {
        padding: 24rpx;

        .agreement-text,
        .link-text {
            font-size: 30rpx;
        }
    }
}
</style>