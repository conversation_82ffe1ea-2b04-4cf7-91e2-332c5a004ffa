
import Vue from 'vue'
import Vuex from 'vuex'
import { chat } from "@/utils/api.js"
Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    count: 0,
    user: null,
    city: '',
    currentCity: '',
    work: '',
    cation: '',
    cationdetail: '',
    realTimeData: '',
    yourDataList: [],
    introduction: '',
    address: '',
    partTab: 0,
    selectedJob: null,
    urgentJobs: null,
    lastProcessedMessages: new Map() // 防重复处理消息的记录
  },
  mutations: {
    SET_SELECTED_JOB(state, job) {
      state.selectedJob = job;
    },
    URGENT_JOB(state, job) {
      state.urgentJobs = job;
    },

    setPartTab(state, partTab) {
      state.partTab = partTab
    },
    setaddress(state, address) {
      state.address = address
    },
    setIntroduction(state, introduction) {
      console.log(introduction, 'introduction')
      state.introduction = introduction
    },
    setUser(state, user) {
      state.user = user
    },
    setCity(state, city) {
      state.city = city
    },
    setCurrentCity(state, currentCity) {
      state.currentCity = currentCity
    },
    setwork(state, work) {
      state.work = work
    },
    clearWork(state) {
      state.work = ''; // 清除 work 的值
    },
    setCation(state, cation) {
      console.log(cation, 'cation')
      state.cation = cation
    },
    setCationdetail(state, cationdetail) {
      console.log(cationdetail, 'cationdetail')
      state.cationdetail = cationdetail
    },
    updateData(state, data) {
      state.realTimeData = data;
    },
    UPDATE_DATA_LIST(state, payload) {
      state.yourDataList = payload;
    }
  },
  getters: {
    getCity: state => state.city,
    getUser: state => state.user,
    getSelectedJob: state => state.selectedJob,
    geturgentJob: state => state.urgentJobs,
  },
  actions: {
    selectJob({ commit }, job) {
      commit('SET_SELECTED_JOB', job);
    },
    urgentJob({ commit }, job) {
      commit('URGENT_JOB', job);
    },
    updateCity({ commit }, city) {
      commit('setCity', city)
    },
    async handleWebSocketMessage({ commit, state }, message) {
      console.log(message, 'message')
      // 根据消息类型处理
      const res = await chat.chatList({ silent: true })
      if(res.code == 200){
        // 更新角标
        if(res.data.Unread > 0){
          uni.setTabBarBadge({
            index: 3,
            text: res.data.Unread.toString()
          })
        } else {
          uni.removeTabBarBadge({
            index: 3
          })
        }

        // 通知消息页面更新数据
        uni.$emit('message-list-updated', {
          messageList: res.data.data || [],
          unreadCount: res.data.Unread || 0,
          total: res.data.total || 0
        })
      }
      uni.hideLoading()
    }
  }
})







