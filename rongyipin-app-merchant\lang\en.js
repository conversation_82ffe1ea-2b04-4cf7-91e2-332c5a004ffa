export default {
	tabbar:'List,Grid,contacts,Mine',
	agreementsTitle:'User service agreement,Privacy policy',
	common: {
		wechatFriends: "friends",
		wechatBbs: "bbs",
		weibo: "weibo",
		more: "more",
		agree:"agree",
		copy: "copy",
		wechatApplet: "applet",
		cancelShare: "cancel sharing",
		updateSucceeded: "update succeeded",
		phonePlaceholder: "Please enter your mobile phone number",
		verifyCodePlaceholder: "Please enter the verification code",
		newPasswordPlaceholder: "Please enter a new password",
		confirmNewPasswordPlaceholder: "Please confirm the new password",
		confirmPassword: "Please confirm the password",
		verifyCodeSend: "Verification code has been sent to via SMS",
		passwordDigits: "The password is 6 - 20 digits",
		getVerifyCode: "Get Code",
		noAgree: "You have not agreed to the privacy policy agreement",
		gotIt: "got it",
		login: "sign in",
		error: "error",
		complete: "complete",
		submit: "Submit",
		formatErr: "Incorrect mobile phone number format",
		sixDigitCode: "Please enter a 6-digit verification code",
		resetNavTitle:"Reset password"
	},
	list: {
		inputPlaceholder: "Please enter the search content",
	},
	search: {
		cancelText: "cancel",
		searchHistory: "search history",
		searchDiscovery: "search discovery",
		deleteAll: "delete all",
		delete: "delete",
		deleteTip: "Are you sure to clear the search history ?",
		complete: "complete",
		searchHiddenTip: "Current search found hidden",
	},
	grid: {
		grid: "Grid Assembly",
		visibleToAll: "Visible to all",
		invisibleToTourists: "Invisible to tourists",
		adminVisible: "Admin visible",
		clickTip: "Click the",
		clickTipGrid: "grid",
	},
	mine: {
		showText: "Text",
		signIn: "Check In Reward",
		signInByAd:"Check In Reward By AD",
		toEvaluate: "To Evaluate",
		readArticles: "Read Articles",
		myScore: "My Score",
		invite: "Invite Friends",
		feedback: "Problems And Feedback",
		settings: "Settings",
		about: "About",
		checkUpdate: "Check for Updates",
		clicked: "You Clicked",
		checkScore: "Please check your points after logging in",
		currentScore: "The current score is ",
		noScore: "There are currently no points",
		notLogged: "not logged in",
	},
	userinfo: {
		navigationBarTitle:"My Profile",
		ProfilePhoto: "Profile Photo",
		nickname: "Nickname",
		notSet: "not set",
		phoneNumber: "Phone Number",
		notSpecified: "Not Specified",
		setNickname: "Set Nickname ",
		setNicknamePlaceholder: "Please enter a nickname to set",
		bindPhoneNumber: "One click binding of local number",
		bindOtherLogin: "Other number binding",
		noChange: "No change",
		uploading: "uploading",
		requestFail: "Request for service failed",
		setting: "setting",
		deleteSucceeded: "Delete succeeded",
		setSucceeded: "Set successfully",
	},
	smsCode: {
		resendVerifyCode: "resend",
		phoneErrTip: "Mobile phone number format error",
		sendSuccessTip: "SMS verification code sent successfully",
	},
	loadMore: {
		noData: "No Data",
		noNetwork: "Network error",
		toSet: "Go to settings",
		error: "error",
	},
	uniFeedback: {
		navigationBarTitle: "Problems and feedback",
		msgTitle: "Message content",
		imgTitle: "Picture list",
		contacts: "contacts",
		phone: "contact number",
		submit: "submit",
	},
	settings: {
		navigationBarTitle:"Settings",
		userInfo: "Personal Data",
		changePassword: "change password",
		clearTmp: "clean cache",
		pushServer: "push function",
		fingerPrint: "fingerprint unlock",
		facial: "face unlock",
		deactivate: "Deactivate",
		logOut: "Logout",
		login: "Login",
		changeLanguage: "Language",
		please: "please",
		successText: "success",
		failTip: "Authentication failed. Please try again",
		authFailed: "authentication failed",
		deviceNoOpen: "The device is not turned on",
		fail: "fail",
		tips: "tips",
		exitLogin: "Do you want to log out？",
		cancelText: "cancel",
		confirmText: "confirm",
		clearing: "clearing",
		clearedSuccessed: "Cleared successfully",
	},
	deactivate: {
		cancelText: "cancel",
		nextStep: "next step",
		navigationBarTitle:"Logout prompt"
	},
	about: {
		sacnQR: "Scan the QR Code and your friends can also download it",
		client: "applCantion",
		and: "And",
		about: "About",
	},
	invite: {
		download: "Download",
	},
	login: {
		phoneLogin: "After logging in, you can show yourself",
		phoneLoginTip: "Unregistered mobile phone numbers will be automatically registered after verification",
		getVerifyCode: "Get Code",
	},
	uniQuickLogin: {
		accountLogin: "Account",
		SMSLogin: "SMS",
		wechatLogin: "wechat",
		appleLogin: "Apple",
		oneClickLogin: "One click login",
		QQLogin: "QQ",
		xiaomiLogin: "Xiaomi",
		getProviderFail: "Failed to get service provider",
		loginErr: "Login service initialization error",
		chooseOtherLogin: "Click the third-party login",
	},
	pwdLogin: {
		pwdLogin: "User name password login",
		placeholder: "Please enter mobile number / user name",
		passwordPlaceholder: "Please input a password",
		verifyCodePlaceholder: "Please enter the verification code",
		login: "sign in",
		forgetPassword: "Forget password",
		register: "Registered account",
	},
	register: {
		navigationBarTitle:"register",
		usernamePlaceholder: "Please enter user name",
		nicknamePlaceholder: "Please enter user nickname",
		passwordDigitsPlaceholder: "Please enter a 6-20 digit password",
		passwordAgain: "Enter the password again",
		registerAndLogin: "Register and log in",
	},
	listDetail: {
		follow: "Click follow",
		newsErr: "Error, news ID is empty",
	},
	newsLog:{
		navigationBarTitle:"Reading Log"
	},
	bindMobile:{
		navigationBarTitle:"Bind Mobile"
	}
}
