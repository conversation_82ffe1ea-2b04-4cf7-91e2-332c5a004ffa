/**
 * 封装请求方法
 * 处理跨域问题和请求拦截
 */

// 基础URL，根据平台自动切换
let BASE_URL = ""

// #ifdef H5
BASE_URL = "/api" // H5环境使用代理路径，配合manifest.json中的proxy
// #endif

// #ifdef APP-PLUS
BASE_URL = "http://8.130.152.121:82/api" // App环境使用完整URL
// #endif

// #ifdef MP
BASE_URL = "http://8.130.152.121:82/api" // 小程序环境使用完整URL
// #endif

// 打印当前使用的BASE_URL，方便调试
console.log('当前运行平台BASE_URL:', BASE_URL)
// 请求超时时间
const TIMEOUT = 10000

// 请求拦截器
const requestInterceptor = (config) => {
  // 获取token
  const token = uni.getStorageSync("token")
  if (token) {
    config.header = {
      ...config.header,
      Authorization: token,
    }
  }
  return config
}

// 响应拦截器
const responseInterceptor = (response) => {
  console.log("Response:", response)
  
  // 这里可以对响应数据做处理
  if (response.data.code === 200 || response.data.status === 0) {
    uni.showLoading({
      title: '加载中'
    });
    return response.data
  } else if (response.data.code === 401) {
    // token过期，跳转到登录页
    uni.showToast({
      title: "登录已过期，请重新登录",
      icon: "none",
    })
    uni.removeStorageSync('token');
    uni.removeStorageSync('is_auth');
    uni.removeStorageSync('usinfo');
    setTimeout(() => {
      uni.navigateTo({
        url: "/pages/login/login"
      })
    }, 1500)
    return Promise.reject("登录已过期")
  } else if (response.data.code === 400||response.data.code === 403 || response.data.code === 404 || response.data.code === 405 || response.data.code === 406) {
    return response.data;
  } else {
    uni.showToast({
      title: response.data.msg || "请求失败",
      icon: "none",
    })
    // return Promise.reject(response.data)
  }
}

// 错误处理
const errorHandler = (error) => {
  uni.hideLoading()
  uni.showToast({
    title: "网络异常，请稍后再试",
    icon: "none",
  })
  return Promise.reject(error)
}

// 封装请求方法
const request = (options) => {
  console.log("Request options:", BASE_URL)
  // 合并选项
  const config = {
    url: BASE_URL + options.url,
    data: options.data || {},
    method: options.method || "GET",
    header: {
      "content-type": "application/json",
      ...options.header,
    },
    timeout: TIMEOUT,
    dataType: "json",
  }

  // 应用请求拦截器
  const interceptedConfig = requestInterceptor(config)

  // 发起请求
  return new Promise((resolve, reject) => {
    uni.request({
      ...interceptedConfig,
      success: (res) => {
        try {
          const result = responseInterceptor(res)
          resolve(result)
        } catch (error) {
          reject(error)
        }
      },
      fail: (err) => {
        console.log("Request error:", err)
        errorHandler(err)
        reject(err)
      },
    })
  })
}

// 导出请求方法
export default {
  get: (url, data = {}, options = {}) => {
    return request({
      url,
      data,
      method: "GET",
      ...options,
    })
  },
  post: (url, data = {}, options = {}) => {
    return request({
      url,
      data,
      method: "POST",
      ...options,
    })
  },
  put: (url, data = {}, options = {}) => {
    return request({
      url,
      data,
      method: "PUT",
      ...options,
    })
  },
  delete: (url, data = {}, options = {}) => {
    return request({
      url,
      data,
      method: "DELETE",
      ...options,
    })
  },
}
